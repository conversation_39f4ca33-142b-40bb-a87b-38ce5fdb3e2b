<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.wormhole</groupId>
        <artifactId>wormhole-parent</artifactId>
        <version>1.3.8</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>wormhole-hotelds-api</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <name>wormhole-hotelds-api</name>
    <packaging>pom</packaging>

    <modules>
        <module>wormhole-hotelds-api-client</module>
        <module>wormhole-hotelds-api-service</module>
        <module>wormhole-hotelds-core</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <task.version>1.1.26-pms-event-SNAPSHOT</task.version>
        <agent.version>1.1.12</agent.version>
        <hotelds-api.version>1.2.9</hotelds-api.version>
        <wormhole-chanel.version>1.1.13</wormhole-chanel.version>
        <wormhole-out-call.version>1.2.6</wormhole-out-call.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wormhole</groupId>
                <artifactId>wormhole-agent-client</artifactId>
                <version>${agent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wormhole</groupId>
                <artifactId>wormhole-hotelds-api-client</artifactId>
                <version>${hotelds-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wormhole</groupId>
                <artifactId>wormhole-task-client</artifactId>
                <version>${task.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wormhole</groupId>
                <artifactId>wormhole-channel-client</artifactId>
                <version>${wormhole-chanel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wormhole</groupId>
                <artifactId>wormhole-hotelds-core</artifactId>
                <version>${hotelds-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wormhole</groupId>
                <artifactId>wormhole-out-call-starter</artifactId>
                <version>${wormhole-out-call.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
            <testResource>
                <directory>src/test/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </testResource>
        </testResources>
    </build>

    <repositories>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>spring-snapshots</id>
            <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
    </repositories>
</project>
