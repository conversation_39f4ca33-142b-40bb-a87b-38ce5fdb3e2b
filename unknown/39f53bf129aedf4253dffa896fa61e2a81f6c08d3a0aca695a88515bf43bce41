package com.wormhole.hotelds.api.hotel.constant;

import cn.hutool.core.util.ObjectUtil;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ChatTypeEnum {
    /**
     * 文字
     */
    TEXT(1, "文字"),
    /**
     * 客房
     */
    CALL(2, "语音"),
    ;

    private final int code;
    private final String description;

    public static String getDescByTicket(HdsServiceTicketEntity ele) {
        if (ele != null && ObjectUtil.isNotEmpty(ele.getRtcRoomId())){
            return CALL.getDescription();
        }
        return TEXT.getDescription();
    }
}
