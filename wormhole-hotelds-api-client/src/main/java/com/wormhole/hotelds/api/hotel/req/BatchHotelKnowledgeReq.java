package com.wormhole.hotelds.api.hotel.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * 批量查询门店知识库信息请求
 * <AUTHOR>
 * @date 2025/8/20
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BatchHotelKnowledgeReq {

    /**
     * 门店编码列表
     */
    private List<String> hotelCodes;
}
