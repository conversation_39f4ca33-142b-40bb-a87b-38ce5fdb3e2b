package com.wormhole.hotelds.api.hotel.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 设备初始化请求对象
 * <AUTHOR>
 * @date 2025/5/22 14:28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceInitReq implements Serializable {

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 酒店代码
     */
    private String hotelCode;

    /**
     * 设备应用类型
     */
    private String deviceAppType;

    private String userId;

    private String userName;

    private String appVersion;

    private String language;
}