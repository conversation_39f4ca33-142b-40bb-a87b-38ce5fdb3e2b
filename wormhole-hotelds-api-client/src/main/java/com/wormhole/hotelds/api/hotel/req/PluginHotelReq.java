package com.wormhole.hotelds.api.hotel.req;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PluginHotelReq {
    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 酒店的名称
     */
    private String hotelName;


    private String dataSource;

    private OperatorInfo operatorInfo;

//    private PluginRoom pluginRoom;

    private PluginHotel pluginHotel;

    private PluginPlace pluginPlace;

    @Data
    @Builder
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PluginRoom {
        private Integer room_count;
        private JsonNode physic_room_map;
    }

    @Data
    @Builder
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PluginPlace {
        private JsonNode place_info_list;
    }

    @Data
    @Builder
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PluginHotel {
        /**
         * 酒店的政策信息，使用 HotelPolicies 对象表示
         */
        private JsonNode hotelPolicy;

        private JsonNode hotelFacility;

        private JsonNode reservationNoticeTipsInfo;

        private Map<String, Object> hotelInfo;

        private JsonNode positionInfo;

        private JsonNode hotelComment;
    }

    @Data
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class OperatorInfo {
        private String operatorId;
        private String operatorName;
        private String hotelCode;
        private String hotelName;
        private List<ExternalHotel> externalHotels;

        @Data
        @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
        public static class ExternalHotel {
            private String code;
            private String name;
            private String group;
        }
    }
}
