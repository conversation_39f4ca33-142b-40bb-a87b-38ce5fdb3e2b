package com.wormhole.hotelds.api.hotel.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Request for updating ticket tag flag
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateTicketTagFlagReq implements Serializable {
    /**
     * Ticket number
     */
    private String ticketNo;
    
    /**
     * Tag flag (0=无标注，1=已协助处理，2=需优先标记)
     */
    private Integer tagFlag;
}
