package com.wormhole.hotelds.api.hotel.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TicketAdminListResp {
    private Long id;

    private String hotelCode;

    private String hotelName;

    private String ticketNo;

    private String ticketCategory;

    private String ticketCategoryName;

    private String positionName;

    private String guestRequest;

    private String completedBy;

    private String completedByName;

    private String completedAt;

    private Integer handleMethod;

    private String handleMethodDesc;

    private String showFlag;

    private Integer status;

    private Integer feedbackStatus;

    private String rtcRoomId;

    private String createdAt;

    private String createdBy;

    private String callStartTime;

    private String callEndTime;

    private String chatType;

    private String createdByName;

    private Integer tagFlag;

}
