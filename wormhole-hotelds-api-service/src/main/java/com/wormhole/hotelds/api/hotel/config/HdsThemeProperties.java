package com.wormhole.hotelds.api.hotel.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 主题配置属性类
 *
 * <AUTHOR>
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "theme")
public class HdsThemeProperties {

    /**
     * 默认logo URL
     */
    private String defaultLogo;

    /**
     * 默认酒店logo URL
     */
    private String defaultHotelLogo;

    /**
     * 默认banner URL
     */
    private String defaultBanner;
}