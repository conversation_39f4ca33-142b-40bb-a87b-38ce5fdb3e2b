package com.wormhole.hotelds.api.hotel.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "tts.request")
public class TtsProperties {
    /**
     * TTS合成的音色。
     * 例如: "zh_female_meilinvyou_moon_bigtts"
     */
    private String voice = "zh_female_meilinvyou_moon_bigtts";

    /**
     * 生成的音频格式。
     * 例如: "mp3"
     */
    private String format = "mp3";

    /**
     * 语速，取值范围：[0.5, 2.0]。
     */
    private Double speed = 1.0;

    /**
     * 如果调用时未指定语言，则使用此默认语言代码。
     * 例如: "zh"
     */
    private String defaultLanguage = "zh";

    private String provider = "doubao";


}
