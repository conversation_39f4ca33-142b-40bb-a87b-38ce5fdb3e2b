package com.wormhole.hotelds.api.hotel.config;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * <AUTHOR>
 * &#064;date  2025/4/9 13:50
 */
@Data
@ConfigurationProperties(WechatProperties.CONFIG_PREFIX)
@RefreshScope
@Slf4j
public class WechatProperties {

    public static final String CONFIG_PREFIX = "wormhole.wechat.miniapp";

    private String qrcodePage;

    private String distance = "1000"; // 1 km in meters

    private String botCode = "bot_347bec7d61b2ff46823b2501cbf1f1b9";

    private String i18nBot = "bot_1e7302b374766aea7ba814bbc6c70e0d";

    public String getChatBot(String language){
        log.info("WechatProperties.getChatBot  language:{}",language);
        if (StrUtil.isNotBlank(language) && ObjectUtil.notEqual(language, "zh_CN") && ObjectUtil.notEqual(language, "cn")) {
            return i18nBot;
        }
        return botCode;
    }

}
