package com.wormhole.hotelds.api.hotel.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "wormhole")
@Data
public class WormholeConfiguration {
    private String bdwAiChatH5Url = "https://dev-chat.bwagent.net/room-chat?hotelCode=%s&positionCode=%s&bdwHotelCode=%s&childOrderNo=%s&type=1&accessType=0&entryType=1&withbdwapp=true&chatScene=bdw";

    private String textChinese = "中文";

    private String hoteldsHost = "http://wormhole-hotelds.delonix.test";

}