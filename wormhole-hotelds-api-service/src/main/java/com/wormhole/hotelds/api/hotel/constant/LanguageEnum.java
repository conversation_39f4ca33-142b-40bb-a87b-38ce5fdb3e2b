package com.wormhole.hotelds.api.hotel.constant;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@Getter
@RequiredArgsConstructor
public enum LanguageEnum {

    /**
     * 中文
     */
    CHINESE("zh"),

    /**
     * 英文
     */
    ENGLISH("en"),

    /**
     * 印尼语
     */
    INDONESIAN("id");

    /**
     * 语言代码 (例如: "zh", "en")
     */
    private final String code;

    public static List<String> getAllCodes() {
        return Arrays.stream(values())
                .map(LanguageEnum::getCode)
                .collect(Collectors.toList());
    }

}
