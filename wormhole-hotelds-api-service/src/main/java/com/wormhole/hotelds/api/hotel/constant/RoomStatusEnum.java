package com.wormhole.hotelds.api.hotel.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RoomStatusEnum {

    NORMAL(0, "正常"),
    ABNORMAL(1, "异常"),
    ;
    private final Integer code;
    private final String description;

    public static RoomStatusEnum getEnumByCode(Integer actionType) {
        for (RoomStatusEnum ele : RoomStatusEnum.values()) {
            if (ele.getCode().equals(actionType)) {
                return ele;
            }
        }
        return null;
    }
}
