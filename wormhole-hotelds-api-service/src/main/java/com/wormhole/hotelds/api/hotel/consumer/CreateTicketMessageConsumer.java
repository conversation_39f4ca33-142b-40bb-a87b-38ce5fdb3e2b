package com.wormhole.hotelds.api.hotel.consumer;

import com.wormhole.channel.consts.MessageBody;
import com.wormhole.channel.consts.event.SubtitleEvent;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.web.model.req.TicketCreatedMessage;
import com.wormhole.hotelds.api.hotel.web.service.CallLogService;
import com.wormhole.hotelds.api.hotel.web.service.RtcService;
import com.wormhole.hotelds.api.hotel.web.service.ServiceTicketService;
import com.wormhole.mq.consumer.AbstractReactiveMessageListener;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/5/6 11:44
 */

@Component
@RocketMQMessageListener(
        topic = "hotelds_api_create_ticket_push_delay_message",
        consumerGroup = "hotelds_api_create_ticket_push_consumer_group"
)
@Slf4j
public class CreateTicketMessageConsumer extends AbstractReactiveMessageListener<MessageBody> {

    @Resource
    private ServiceTicketService service;

    @Override
    protected Mono<Void> processMessage(MessageBody payload) {
        TicketCreatedMessage ticketCreatedMessage = JacksonUtils.convertValue(payload.getData(), TicketCreatedMessage.class);
        log.info("CreateTicketMessageConsumer processMessage: {}", JacksonUtils.writeValueAsString(ticketCreatedMessage));
        return service.createTicketMessage(ticketCreatedMessage)
                .doOnSuccess(result -> {
                    log.info("CreateTicketMessageConsumer processMessage processed successfully: {}", JacksonUtils.writeValueAsString(ticketCreatedMessage));
                }).doOnError(e -> log.error("Error CreateTicketMessageConsumer processMessage: {}", JacksonUtils.writeValueAsString(ticketCreatedMessage), e))
                .then();
    }
}
