package com.wormhole.hotelds.api.hotel.job;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.web.service.TicketKeywordStatisticsService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.wormhole.hotelds.api.hotel.web.service.HotelDailyAiStatisticsService;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.List;

/**
 * 酒店每日AI统计定时任务
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@Component
public class HotelAiDailyStatsJob {

    @Autowired
    private HotelDailyAiStatisticsService statisticsService;

    @Autowired
    private TicketKeywordStatisticsService ticketKeyWordStatisticsService;

    /**
     * 每日14:10执行，统计昨天的酒店AI数据
     * 数据查询范围：昨天14:00到今天14:00（不超过）
     * 执行参数：
     * - 无参数：统计昨天的数据
     * - 指定日期：格式为 yyyy-MM-dd，统计指定日期的数据
     */
    @XxlJob("hotelAiDailyStatsJob")
    public void hotelAiDailyStatsJob() {
        log.info("开始执行酒店每日AI统计任务");
        
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        
        try {
            // 获取任务参数
            String jobParam = XxlJobHelper.getJobParam();
            LocalDate statDate;
            
            if (jobParam != null && !jobParam.trim().isEmpty()) {
                // 如果指定了日期参数，使用指定日期
                statDate = LocalDate.parse(jobParam.trim());
                log.info("使用指定日期进行统计：{}", statDate);
            } else {
                // 默认统计昨天的数据
                statDate = LocalDate.now().minusDays(1);
                log.info("使用默认日期（昨天）进行统计：{}", statDate);
            }
            
            // 执行统计任务
            statisticsService.generateDailyStatistics(statDate)
                    .doOnSuccess(v -> {
                        stopWatch.stop();
                        log.info("酒店每日AI统计任务执行成功，统计日期：{}，耗时：{} ms", statDate, stopWatch.getTotalTimeMillis());
                        XxlJobHelper.handleSuccess("统计任务执行成功，统计日期：" + statDate);
                    })
                    .doOnError(throwable -> {
                        stopWatch.stop();
                        log.error("酒店每日AI统计任务执行失败，统计日期：{}，耗时：{} ms", statDate, stopWatch.getTotalTimeMillis(), throwable);
                        XxlJobHelper.handleFail("统计任务执行失败：" + throwable.getMessage());
                    })
                    .block();
                    
        } catch (Exception e) {
            stopWatch.stop();
            log.error("酒店每日AI统计任务执行异常，耗时：{} ms", stopWatch.getTotalTimeMillis(), e);
            XxlJobHelper.handleFail("统计任务执行异常：" + e.getMessage());
        }
    }

    @XxlJob("generateTicketKeyWordStatistics")
    public void generateTicketKeyWordStatistics() {
        log.info("开始生成工单关键词统计数据");
        String jobParam = XxlJobHelper.getJobParam();
        GenerateTicketKeyWordStatisticsParam param = JacksonUtils.readValue(jobParam, GenerateTicketKeyWordStatisticsParam.class);
        ticketKeyWordStatisticsService.generateTicketKeywordStatistics(param.getHotelCodes(),param.getBusinessDate()).block();
        log.info("生成工单关键词统计数据完成");
    }

    @Data
    public static class GenerateTicketKeyWordStatisticsParam {
        private List<String> hotelCodes;
        private LocalDate businessDate;
    }

}
