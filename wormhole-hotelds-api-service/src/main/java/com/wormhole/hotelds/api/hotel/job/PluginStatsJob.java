package com.wormhole.hotelds.api.hotel.job;

import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.plugin.service.PluginCollectionDetailsService;
import com.wormhole.hotelds.plugin.service.PluginCollectionSummaryService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/8/7
 */
@Service
@Slf4j
public class PluginStatsJob {


    @Autowired
    private PluginCollectionSummaryService pluginCollectionSummaryService;

    /**
     * 酒店数据统计任务
     * 每日00:01执行，统计酒店相关的各种数据指标
     *
     * 执行参数格式（JSON）：
     * {
     *   "startDate": "2025-01-01",     // 开始日期，必填
     *   "endDate": "2025-01-07",       // 结束日期，必填
     *   "externalHotelId": "hotel123"  // 外部酒店ID，可选，不填则统计所有酒店
     * }
     *
     * 如果不传参数，默认统计昨天的数据
     */
    @XxlJob("hotelDataStatisticsJob")
    public void hotelDataStatisticsJob() {
        log.info("开始执行酒店数据统计任务");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String jobParam = XxlJobHelper.getJobParam();
        HotelDataStatisticsParam param;
        if(StringUtil.isNotBlank(jobParam)) {
             param = JacksonUtils.readValue(jobParam, HotelDataStatisticsParam.class);
        }else {
             param = HotelDataStatisticsParam.builder()
                    .startDate(LocalDate.now().minusDays(1))
                    .endDate(LocalDate.now().minusDays(1)).build();
        }
        pluginCollectionSummaryService.executeHotelDataStatistics(param.getStartDate(), param.getEndDate(), param.getHotelCode()).block();
        log.info("开始执行酒店数据统计任务,耗时:{}",stopWatch.prettyPrint());

    }


    /**
     * 酒店数据统计任务参数
     */
    @Data
    @lombok.Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HotelDataStatisticsParam implements Serializable {
        /**
         * 开始日期
         */
        private LocalDate startDate;

        /**
         * 结束日期
         */
        private LocalDate endDate;

        /**
         * 酒店Code
         */
        private String hotelCode;
    }

}
