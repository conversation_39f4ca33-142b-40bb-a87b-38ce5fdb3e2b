package com.wormhole.hotelds.api.hotel.util;

import cn.hutool.core.util.StrUtil;
import com.google.common.base.Joiner;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;

public class DeviceUtils {

    private final static Joiner JOINER = Joiner.on(" ").skipNulls();

    public static String getPositionFullName(HdsDevicePositionEntity position) {
        if (position == null) return null;
        String block = position.getBlock();
        String room = position.getPositionName();
        if (StrUtil.isBlank(block) || "主楼".equals(block)) {
            return room;
        } else {
            return JOINER.join(block, room);
        }
    }

    public static String getBlockFloorName(HdsDevicePositionEntity position) {
        if (position == null) return null;
        return JOINER.join(StrUtil.isNotBlank(position.getBlock()) ? position.getBlock() : null,
                StrUtil.isNotBlank(position.getArea()) ? position.getArea()  : null);
    }
}
