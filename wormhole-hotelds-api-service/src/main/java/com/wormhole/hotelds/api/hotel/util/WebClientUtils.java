package com.wormhole.hotelds.api.hotel.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.wormhole.trace.TraceConstant.TRACE_ID_ATTR;
import static com.wormhole.trace.TraceConstant.TRACE_ID_HEADER;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@Service
@Slf4j
public class WebClientUtils {

    @Autowired
    private static final WebClient WEB_CLIENT;

    static {
        WEB_CLIENT = WebClient.builder()
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 设置为 5MB
                .filter((request, next) -> {
                    String traceId = MDC.get(TRACE_ID_ATTR);

                    ClientRequest newRequest = ClientRequest.from(request)
                            .headers(headers -> {
                                if (traceId != null) {
                                    headers.set(TRACE_ID_HEADER, traceId);
                                }
                            })
                            .build();

                    return next.exchange(newRequest);
                })
                .build();
    }

    public <T> Mono<T> sendPost(String url, Map<String, String> paramMap, Object body, Map<String, String> headers, TypeReference<T> typeReference) {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        if (CollectionUtil.isNotEmpty(paramMap)){
            paramMap.forEach(params::add);
        }

        // 使用headers方法，它接受一个Consumer<HttpHeaders>
        return WEB_CLIENT.post()
                .uri(url)
                .headers(httpHeaders -> {
                    // 添加请求头
                    if (CollUtil.isNotEmpty(headers)) {
                        headers.forEach(httpHeaders::add);
                    }
                })
                .bodyValue(Optional.ofNullable(body).orElse(new HashMap<>()))
                .retrieve()
                .onStatus(HttpStatusCode::isError, response ->{
                    log.error("Remote service returned error status: {}", response.statusCode());
                    return response.bodyToMono(String.class)
                            .doOnNext(errorBody -> log.error("Error response body: {}", errorBody))
                            .flatMap(errorBody ->
                                    Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR))
                            );
                })
                .bodyToMono(new ParameterizedTypeReference<T>() {
                })
                .doOnError(e -> log.error("webClientPost request failed url {} params {} body {}", url, JacksonUtils.writeValueAsString(paramMap), JacksonUtils.writeValueAsString(body), e))
                .onErrorResume(error -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, error)))
                .doOnNext(res -> log.info("webClientPost for: url {} params {} body {} resp {}",url, JacksonUtils.writeValueAsString(paramMap),
                        JacksonUtils.writeValueAsString(body), JacksonUtils.writeValueAsString(res)))
                .flatMap(result -> {
//                    try {
//                        WebClientResult errorValue = JacksonUtils.convertValue(result,WebClientResult.class);
//                        // 正常响应obj，不一定能转化为WebClientResult；能转化为WebClientResult 的也不一定是响应体
//                        if (errorValue!= null && StringUtils.isNotBlank(errorValue.getCode()) && StringUtils.isNotBlank(errorValue.getMsg()) && !errorValue.isSuccess()){
//                            return Mono.error(new BusinessException(errorValue.getCode(), errorValue.getMsg()));
//                        }
//                    } catch (Exception e) {
//                        log.error("webClientPost result parse error {}", JacksonUtils.writeValueAsString(result));
//                    }
                    T resultData = JacksonUtils.convertValue(result, typeReference);
                    log.info("webClientPost result {}", JacksonUtils.writeValueAsString(resultData));
                    return Mono.just(resultData);
                })
                .switchIfEmpty(Mono.empty());
    }

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    private static class WebClientResult {
        private String code;
        private String msg;
        private String traceId;

        public boolean isSuccess() {
            return  ResultCode.SUCCESS.getCode().equals(this.code);
        }

    }
}
