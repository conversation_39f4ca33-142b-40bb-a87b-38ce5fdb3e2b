package com.wormhole.hotelds.api.hotel.web.controller.toB;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.web.model.req.CallLogQueryReq;
import com.wormhole.hotelds.api.hotel.web.model.req.PageCallLogQO;
import com.wormhole.hotelds.api.hotel.web.model.req.RoomStatusQueryReq;
import com.wormhole.hotelds.api.hotel.web.model.res.CallLogWithTicketsRes;
import com.wormhole.hotelds.api.hotel.web.model.res.HdsCallLogVO;
import com.wormhole.hotelds.api.hotel.web.model.res.RoomStatusRes;
import com.wormhole.hotelds.api.hotel.web.service.CustomerAdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/30 13:50
 */

@RestController
@RequestMapping("/customer/admin")
public class CustomerAdminController {

    @Autowired
    private CustomerAdminService customerAdminService;


    @PostMapping("/query/roomList")
    public Mono<Result<List<RoomStatusRes>>> postRoomStatus(@RequestBody RoomStatusQueryReq request) {
        return customerAdminService.queryRoomStatus(request).flatMap(Result::success);
    }


    @PostMapping("/callLog/page")
    public Mono<Result<PageResult<CallLogWithTicketsRes>>> page(@RequestBody CallLogQueryReq callLogQueryReq){
        return customerAdminService.getCallLogDetailPage(callLogQueryReq).flatMap(Result::success);
    }




}
