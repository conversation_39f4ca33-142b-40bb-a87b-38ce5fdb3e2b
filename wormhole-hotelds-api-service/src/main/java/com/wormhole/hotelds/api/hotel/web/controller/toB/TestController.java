package com.wormhole.hotelds.api.hotel.web.controller.toB;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.job.PluginStatsJob;
import com.wormhole.hotelds.api.hotel.web.service.TtsService;
import com.wormhole.hotelds.plugin.service.PluginCollectionSummaryService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/8/7
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/test")
public class TestController {

    @Autowired
    private PluginCollectionSummaryService pluginCollectionSummaryService;

    @Autowired
    private TtsService ttsService;

    @PostMapping("/testJob")
    public Mono<Result<Boolean>> testJob(@RequestBody TestJobQO testJobQO){
        return  pluginCollectionSummaryService.executeHotelDataStatistics(testJobQO.getStartDate(), testJobQO.getEndDate(), testJobQO.getHotelCode()).then(Result.success(true));
    }
    @Data
    public static class TestJobQO{
        private LocalDate startDate;
        private LocalDate endDate;
        private String hotelCode;
    }

    @GetMapping("/getAudioUrl")
    public Mono<Result<String>> getAudioUrl(@RequestParam("text") String text,
                                             @RequestParam(value = "language") String language){
        return  ttsService.generateAndUploadAudio(text, language).flatMap(Result::success);
    }
}
