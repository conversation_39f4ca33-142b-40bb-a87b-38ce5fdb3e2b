package com.wormhole.hotelds.api.hotel.web.controller.toB;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.job.PluginStatsJob;
import com.wormhole.hotelds.plugin.service.PluginCollectionSummaryService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/8/7
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/test")
public class TestController {

    @Autowired
    private PluginCollectionSummaryService pluginCollectionSummaryService;

    @PostMapping("/testJob")
    public Mono<Result<Boolean>> testJob(@RequestBody TestJobQO testJobQO){
        return  pluginCollectionSummaryService.executeHotelDataStatistics(testJobQO.getStartDate(), testJobQO.getEndDate(), testJobQO.getHotelCode()).then(Result.success(true));
    }
    @Data
    public static class TestJobQO{
        private LocalDate startDate;
        private LocalDate endDate;
        private String hotelCode;
    }
}
