package com.wormhole.hotelds.api.hotel.web.controller.toC;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.config.TicketProperties;
import com.wormhole.hotelds.api.hotel.resp.MatchWormholeRoomsResp;
import com.wormhole.hotelds.api.hotel.resp.QueryAiHotelResp;
import com.wormhole.hotelds.api.hotel.req.MatchWormholeRoomsReq;
import com.wormhole.hotelds.api.hotel.req.QueryAiHotelReq;
import com.wormhole.hotelds.api.hotel.config.WormholeConfiguration;
import com.wormhole.hotelds.api.hotel.web.model.req.BatchCreatePositionTicketReq;
import com.wormhole.hotelds.api.hotel.web.model.req.BatchRoomMappingReq;
import com.wormhole.hotelds.api.hotel.web.model.req.BatchRoomMappingWithTypeReq;
import com.wormhole.hotelds.api.hotel.web.model.res.AllAiHotelsRes;
import com.wormhole.hotelds.api.hotel.web.model.res.BatchCreateTicketResp;
import com.wormhole.hotelds.api.hotel.web.model.res.BatchRoomMappingRes;
import com.wormhole.hotelds.api.hotel.web.service.BdwAiCustomerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/12
 */
@RestController
@RequestMapping("/bdw_ai_customer")
public class BdwAiCustomerController {

    @Autowired
    private BdwAiCustomerService bdwAiCustomerService;
    @Autowired
    private WormholeConfiguration wormholeConfiguration;
    @Autowired
    private TicketProperties ticketProperties;


    @PostMapping("/match_wormhole_rooms")
    public Mono<Result<MatchWormholeRoomsResp>> matchWormholeRooms(@RequestBody MatchWormholeRoomsReq matchWormholeRoomsReq) {
        return bdwAiCustomerService.matchWormholeRooms(matchWormholeRoomsReq).flatMap(Result::success);
    }

    @PostMapping("/batch_room_mapping")
    public Mono<Result<BatchRoomMappingRes>> batchRoomMapping(@RequestBody BatchRoomMappingReq req) {
        boolean empty = CollUtil.isEmpty(req.getPositionList());
        if (empty && StringUtils.isNotBlank(req.getThirdRoomNo())){
            req.setPositionList(List.of(BatchRoomMappingReq.PositionMappingData.builder().thirdRoomNo(req.getThirdRoomNo()).build()));
        }
        if (empty && CollUtil.isNotEmpty(req.getThirdRoomNoList())){
            List<BatchRoomMappingReq.PositionMappingData> collect = req.getThirdRoomNoList().stream().map(thirdRoomNo -> BatchRoomMappingReq.PositionMappingData.builder().thirdRoomNo(thirdRoomNo).build()).toList();
            req.setPositionList(collect);
        }
        return bdwAiCustomerService.batchRoomMapping(req).flatMap(Result::success);
    }

    @PostMapping("/all_ai_hotels")
    public Mono<AllAiHotelsRes> getAllAiHotels() {
        return bdwAiCustomerService.getAllAiHotels();
    }

    @PostMapping("/batch_create_position_ticket")
    public Mono<Result<BatchCreateTicketResp>> batchCreatePositionTicket(@RequestBody BatchCreatePositionTicketReq req) {
        return bdwAiCustomerService.batchCreatePositionTicket(req).flatMap(Result::success);
    }

    @GetMapping("/test_pms")
    public Mono<Result<Map<String,Object>>> testPms(@RequestBody Map<String,String>  params) {
        Map<String,Object> map = new HashMap<>();
        map.put("hotelCode","MTSZSK");
        map.put("roomNoList",List.of("8502","8505","8507"));
        return Result.success(map);
    }



    @PostMapping("/query_ai_hotels")
    public Mono<Result<QueryAiHotelResp>> queryAiHotels(@RequestBody QueryAiHotelReq queryAiHotelReq) {
        return bdwAiCustomerService.queryAiHotels(queryAiHotelReq).flatMap(Result::success);
    }

    @GetMapping("/getText")
    public Mono<List<String>> getText() {
        return Mono.just(ticketProperties.getTicketDescList());
    }


}
