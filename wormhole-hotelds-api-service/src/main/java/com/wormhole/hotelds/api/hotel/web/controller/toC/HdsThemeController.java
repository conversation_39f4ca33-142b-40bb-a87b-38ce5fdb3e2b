package com.wormhole.hotelds.api.hotel.web.controller.toC;

import com.wormhole.common.constant.HeaderConstant;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.web.model.res.ThemeResp;
import com.wormhole.hotelds.api.hotel.web.service.HdsThemeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 主题相关控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/theme")
@Slf4j
public class HdsThemeController {

    @Autowired
    private HdsThemeService themeService;

    /**
     * 初始化主题资源
     *
     * @param hotelCode 酒店代码（可选）
     * @return 主题初始化数据
     */
    @GetMapping("/init")
    public Mono<Result<ThemeResp>> initTheme(@RequestHeader(value = HeaderConstant.HOTEL_CODE, required = false) String hotelCode) {
        return themeService.initTheme(hotelCode).flatMap(Result::success);
    }

}