package com.wormhole.hotelds.api.hotel.web.controller.toC;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.req.BatchHotelKnowledgeReq;
import com.wormhole.hotelds.api.hotel.req.PluginHotelReq;
import com.wormhole.hotelds.api.hotel.resp.HotelKnowledgeResp;
import com.wormhole.hotelds.api.hotel.web.model.res.HotelDetailVO;
import com.wormhole.hotelds.api.hotel.web.service.HotelService;
import com.wormhole.task.model.vo.TaskScheduleDetailVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/23 11:42
 */
@RestController
@RequestMapping("/hotel")
public class HotelController {

    @Resource
    private HotelService hotelService;



    @PostMapping("/hasAccess")
    public Mono<Boolean> hasAccess(@RequestParam("hotel_code") String hotelCode,
                                   @RequestParam("source") String source) {
        return hotelService.checkHotelAccess(hotelCode, source);
    }

    /**
     * 获取酒店详细信息
     *
     * @param hotelCode 酒店编码
     * @return 酒店详细信息
     */
    @GetMapping("/info")
    public Mono<HotelDetailVO> getHotelInfo(@RequestParam("hotel_code") String hotelCode) {
        return hotelService.getHotelInfo(hotelCode);
    }

    @PostMapping("/sync")
    public Mono<Result<Boolean>> hotelInfoSync(@RequestBody PluginHotelReq req) {
        return hotelService.hotelInfoSync(req).flatMap(Result::success);
    }


    /**
     * 批量查询门店知识库信息
     *
     * @param req 包含门店编码列表的请求
     * @return 门店知识库信息列表
     */
    @PostMapping("/knowledge/batch-query")
    public Mono<Result<List<HotelKnowledgeResp>>> batchQueryHotelKnowledge(@RequestBody BatchHotelKnowledgeReq req) {
        return hotelService.batchQueryHotelKnowledge(req).flatMap(Result::success);
    }
}
