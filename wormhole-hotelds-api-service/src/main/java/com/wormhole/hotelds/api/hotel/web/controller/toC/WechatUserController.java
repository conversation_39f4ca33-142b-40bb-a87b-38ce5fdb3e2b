package com.wormhole.hotelds.api.hotel.web.controller.toC;

import com.wormhole.common.constant.HeaderConstant;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.constant.*;
import com.wormhole.hotelds.api.hotel.web.model.req.LocationReq;
import com.wormhole.hotelds.api.hotel.web.model.req.WechatInitReq;
import com.wormhole.hotelds.api.hotel.web.model.res.WechatInitRes;
import com.wormhole.hotelds.api.hotel.web.model.res.WechatJsapiTicketRes;
import com.wormhole.hotelds.api.hotel.web.model.res.WechatLocationRes;
import com.wormhole.hotelds.api.hotel.web.service.WechatUserService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.*;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;
import reactor.util.function.*;

/**
 * <AUTHOR>
 * @date 2025/4/9 13:50
 */

@RestController
@RequestMapping("/wechat")
public class WechatUserController {
    @Resource
    private WechatUserService wechatUserService;

    @PostMapping(value = "/location/validate")
    public Mono<Result<WechatLocationRes>> validateUserLocation(@RequestBody LocationReq request) {
        return wechatUserService.validateUserLocation(request).flatMap(Result::success);
    }

    @GetMapping("/qrcode/url")
    public Mono<Result<String>> generateQrCodeUrl(@RequestParam("hotel_code") String hotelCode,
                                                  @RequestParam("position_code") String positionCode,
                                                  @RequestParam("type") String type) {
        Mono<Tuple2<String, String>> tuple2Mono = wechatUserService.generateQRCodeAndUpload(hotelCode, positionCode);
        return tuple2Mono
                .map(tuple -> StringUtils.equals(type, QrCodeTypeEnum.NORMAL.getCode()) ? tuple.getT2() : tuple.getT1())
                .flatMap(Result::success);
    }

    @PostMapping("/init")
    public Mono<Result<WechatInitRes>> initWechat(@RequestBody WechatInitReq req, @RequestHeader HttpHeaders httpHeaders) {
        req.setUserId(httpHeaders.getFirst(HeaderConstant.USER_ID));
        req.setLanguage(httpHeaders.getFirst(HeaderConstant.LANGUAGE));
        return wechatUserService.initWechat(req).flatMap(Result::success);
    }

    @GetMapping("/get/jsapiTicket")
    public Mono<Result<WechatJsapiTicketRes>> getJsapiTicket(@RequestParam("app_id") String appId, @RequestParam("share_url") String shareUrl) {
        return wechatUserService.getJsapiTicket(appId, shareUrl).flatMap(Result::success);
    }

}
