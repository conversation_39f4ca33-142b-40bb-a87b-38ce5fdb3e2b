package com.wormhole.hotelds.api.hotel.web.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.wormhole.hotelds.api.hotel.web.model.req.UpdateDeviceQo;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsDeviceQO;
import com.wormhole.hotelds.core.enums.DeviceStatusEnum;
import com.wormhole.hotelds.core.model.entity.HdsDeviceEntity;
import com.wormhole.hotelds.core.model.entity.HdsDeviceFieldEnum;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/14
 */
@Repository
public class HdsDeviceDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public Mono<Long> updateByDeviceId(UpdateDeviceQo updateDeviceQO) {
        Query query = Query.query(Criteria.where(HdsDeviceFieldEnum.device_id.name()).is(updateDeviceQO.getDeviceId()));

        Update update = Update.update(HdsDeviceFieldEnum.device_id.name(), updateDeviceQO.getDeviceId());

        if(StringUtils.isNotBlank(updateDeviceQO.getUserId())) {
            update = Update.update(HdsDeviceFieldEnum.user_id.name(), updateDeviceQO.getUserId());
        }
        if(StringUtils.isNotBlank(updateDeviceQO.getUserName())) {
            update = update.set(HdsDeviceFieldEnum.user_name.name(), updateDeviceQO.getUserName());
        }
        if(StringUtils.isNotBlank(updateDeviceQO.getRtcUserId())) {
            update = update.set(HdsDeviceFieldEnum.rtc_user_id.name(), updateDeviceQO.getRtcUserId());
        }
        if (updateDeviceQO.getDeviceStatus() != null){
            update = update.set(HdsDeviceFieldEnum.device_status.name(), updateDeviceQO.getDeviceStatus());
        }
        return r2dbcEntityTemplate.update(HdsDeviceEntity.class)
                .matching(query)
                .apply(update);
    }
    public Mono<List<HdsDeviceEntity>> findList(HdsDeviceQO qo){
        Criteria criteria = getCriteria(qo);
        if (criteria.isEmpty()){
            return Mono.just(CollectionUtil.newArrayList());
        }
        return r2dbcEntityTemplate.select(Query.query(criteria), HdsDeviceEntity.class).collectList();
    }
    public Mono<Set<String>> getBindPositionCodes(String hotelCode){
        Criteria deviceCr = Criteria.where(HdsDeviceFieldEnum.hotel_code.name()).is(hotelCode)
                .and(HdsDeviceFieldEnum.device_status.name()).not(DeviceStatusEnum.PENDING_ACTIVATION.getCode());
        return r2dbcEntityTemplate.select(Query.query(deviceCr), HdsDeviceEntity.class).switchIfEmpty(Mono.empty())
                .filter(ele -> ObjectUtil.isNotNull(ele.getDeviceStatus()))
                .map(HdsDeviceEntity::getPositionCode).collect(Collectors.toSet());
    }

    public Mono<HdsDeviceEntity> findOne(HdsDeviceQO qo){
        Criteria criteria = getCriteria(qo);
        return r2dbcEntityTemplate.selectOne(Query.query(criteria).limit(1), HdsDeviceEntity.class);
    }

    @NotNull
    private static Criteria getCriteria(HdsDeviceQO qo) {
        Criteria criteria = Criteria.empty();

        if(StringUtils.isNotBlank(qo.getUserId())) {
            criteria = criteria.and(HdsDeviceFieldEnum.user_id.name()).is(qo.getUserId());
        }
        if(CollUtil.isNotEmpty(qo.getDeviceAppType())) {
            criteria = criteria.and(HdsDeviceFieldEnum.device_app_type.name()).in(qo.getDeviceAppType());
        }
        if(StringUtils.isNotBlank(qo.getHotelCode())) {
            criteria = criteria.and(HdsDeviceFieldEnum.hotel_code.name()).is(qo.getHotelCode());
        }
        if(CollUtil.isNotEmpty(qo.getUserIds())) {
            criteria = criteria.and(HdsDeviceFieldEnum.user_id.name()).in(qo.getUserIds());
        }

        if (CollUtil.isNotEmpty(qo.getPositionCodes())) {
            criteria = criteria.and(HdsDeviceFieldEnum.position_code.name()).in(qo.getPositionCodes());
        }
        if(StringUtils.isNotBlank(qo.getDeviceId())) {
            criteria = criteria.and(HdsDeviceFieldEnum.device_id.name()).is(qo.getDeviceId());
        }
        if (qo.getDeviceStatus() != null){
            criteria = criteria.and(HdsDeviceFieldEnum.device_status.name()).is(qo.getDeviceStatus());
        }
        if(StringUtils.isNotBlank(qo.getRtcUserId())) {
            criteria = criteria.and(HdsDeviceFieldEnum.rtc_user_id.name()).is(qo.getRtcUserId());
        }
        if(CollUtil.isNotEmpty(qo.getRtcUserIds())) {
            criteria = criteria.and(HdsDeviceFieldEnum.rtc_user_id.name()).in(qo.getRtcUserIds());
        }

        return criteria;
    }


}
