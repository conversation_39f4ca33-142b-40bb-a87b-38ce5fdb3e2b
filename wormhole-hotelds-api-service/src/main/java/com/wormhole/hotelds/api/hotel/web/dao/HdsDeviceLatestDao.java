package com.wormhole.hotelds.api.hotel.web.dao;

import cn.hutool.core.util.StrUtil;
import com.google.common.base.Strings;
import com.wormhole.hotelds.api.hotel.req.DeviceInitReq;
import com.wormhole.hotelds.core.model.entity.HdsDeviceEntity;
import com.wormhole.hotelds.core.model.entity.HdsDeviceLatestInfoEntity;
import com.wormhole.hotelds.core.model.entity.HdsDeviceLatestInfoFieldEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 设备最新版本信息 DAO
 * <AUTHOR>
 */
@Repository
@Slf4j
public class HdsDeviceLatestDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public Mono<HdsDeviceLatestInfoEntity> insertOrUpdate(DeviceInitReq req) {
        if(StrUtil.isBlank(req.getAppVersion())){
            // appVersion 为空的时候不更新
            return Mono.just(new HdsDeviceLatestInfoEntity());
        }
        return findByDeviceId(req.getDeviceId())
                .flatMap(lastEntity -> {
                    lastEntity.setUpdatedBy(getUserId(req));
                    lastEntity.setUpdatedByName(getUserName(req));
                    lastEntity.setAppVersion(req.getAppVersion());
                    lastEntity.setUpdatedAt(LocalDateTime.now());
                    return updateAppVersionByDeviceId(lastEntity).thenReturn(lastEntity);
                })
                .switchIfEmpty(insertOne(transferByReq(req)));

    }

    private static String getUserId(DeviceInitReq req){
        return Strings.nullToEmpty(req.getUserId());
    }

    private static String getUserName(DeviceInitReq req){
        return Strings.nullToEmpty(req.getUserName());
    }


    private static HdsDeviceLatestInfoEntity transferByReq(DeviceInitReq req) {
        HdsDeviceLatestInfoEntity entity = new HdsDeviceLatestInfoEntity();
        entity.setDeviceId(req.getDeviceId());
        entity.setAppVersion(req.getAppVersion());
        entity.setCreatedAt(LocalDateTime.now());
        entity.setCreatedBy(getUserId(req));
        entity.setCreatedByName(getUserName(req));
        entity.setUpdatedAt(LocalDateTime.now());
        entity.setUpdatedBy(getUserId(req));
        entity.setUpdatedByName(getUserName(req));
        return entity;
    }

    /**
     * 根据设备ID查找设备最新版本信息
     */
    public Mono<HdsDeviceLatestInfoEntity> findByDeviceId(String deviceId) {
        Criteria criteria = Criteria.where(HdsDeviceLatestInfoFieldEnum.device_id.name()).is(deviceId);
        return r2dbcEntityTemplate.selectOne(Query.query(criteria), HdsDeviceLatestInfoEntity.class);
    }

    /**
     * 插入新的设备版本信息
     */
    public Mono<HdsDeviceLatestInfoEntity> insertOne(HdsDeviceLatestInfoEntity entity) {
        return r2dbcEntityTemplate.insert(entity);
    }

    /**
     * 更新设备版本信息
     */
    public Mono<Long> updateAppVersionByDeviceId(HdsDeviceLatestInfoEntity entity) {
        Criteria criteria = Criteria.where(HdsDeviceLatestInfoFieldEnum.id.name()).is(entity.getId());
        Update update = Update.update(HdsDeviceLatestInfoFieldEnum.app_version.name(), entity.getAppVersion())
                .set(HdsDeviceLatestInfoFieldEnum.updated_at.name(), LocalDateTime.now())
                .set(HdsDeviceLatestInfoFieldEnum.updated_by.name(), entity.getUpdatedBy())
                .set(HdsDeviceLatestInfoFieldEnum.updated_by_name.name(), entity.getUpdatedByName());
        return r2dbcEntityTemplate.update(Query.query(criteria), update, HdsDeviceLatestInfoEntity.class);
    }
}
