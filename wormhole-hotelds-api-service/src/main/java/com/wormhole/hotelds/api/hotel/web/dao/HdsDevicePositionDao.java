package com.wormhole.hotelds.api.hotel.web.dao;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsDevicePositionQO;
import com.wormhole.hotelds.api.hotel.web.model.res.RoomStatusRes;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionFieldEnum;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/11
 */
@Repository
public class HdsDevicePositionDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;



    public Mono<List<HdsDevicePositionEntity>> findList(HdsDevicePositionQO hdsDevicePositionQO) {
        Criteria criteria = getCriteria(hdsDevicePositionQO);

        return r2dbcEntityTemplate.select(Query.query(criteria),HdsDevicePositionEntity.class).collectList();
    }

    public Mono<HdsDevicePositionEntity> findOne(HdsDevicePositionQO hdsDevicePositionQO) {
        Criteria criteria = getCriteria(hdsDevicePositionQO);

        return r2dbcEntityTemplate.selectOne(Query.query(criteria).limit(1),HdsDevicePositionEntity.class);
    }

    @NotNull
    private static Criteria getCriteria(HdsDevicePositionQO hdsDevicePositionQO) {
        Criteria criteria = Criteria.empty();

        // 精确匹配positionCode
        if (StringUtils.isNotBlank(hdsDevicePositionQO.getPositionCode())) {
            criteria = criteria.and(HdsDevicePositionFieldEnum.position_code.name()).is(hdsDevicePositionQO.getPositionCode());
        }

        // 精确匹配hotelCode
        if (StringUtils.isNotBlank(hdsDevicePositionQO.getHotelCode())) {
            criteria = criteria.and(HdsDevicePositionFieldEnum.hotel_code.name()).is(hdsDevicePositionQO.getHotelCode());
        }

        if (StringUtils.isNotBlank(hdsDevicePositionQO.getBlock())) {
            criteria = criteria.and(HdsDevicePositionFieldEnum.block.name()).is(hdsDevicePositionQO.getBlock());
        }

        // 模糊匹配positionName
        if (StringUtils.isNotBlank(hdsDevicePositionQO.getPositionName())) {
            criteria = criteria.and(HdsDevicePositionFieldEnum.position_name.name()).like("%" + hdsDevicePositionQO.getPositionName() + "%");
        }

        if(StringUtils.isNotBlank(hdsDevicePositionQO.getFullPositionName())) {
            criteria = criteria.and(HdsDevicePositionFieldEnum.position_name.name()).is(hdsDevicePositionQO.getFullPositionName());
        }

        if (StringUtils.isNotBlank(hdsDevicePositionQO.getDeviceAppType())){
            criteria = criteria.and(HdsDevicePositionFieldEnum.device_app_type.name()).is(hdsDevicePositionQO.getDeviceAppType());
        }

        return criteria;
    }

    public Mono<List<String>> transferAreaCodesToPositions(String areaCodes, String hotelCode) {
        if(StringUtils.isNotBlank(areaCodes)){
            String[] split = areaCodes.split(",");
            List<String> areas = Arrays.asList(split);
            Criteria criteria = Criteria.where(HdsDevicePositionFieldEnum.area_code.name()).in(areas)
                            .and(HdsDevicePositionFieldEnum.hotel_code.name()).is(hotelCode);
            return r2dbcEntityTemplate.select(Query.query(criteria), HdsDevicePositionEntity.class)
                    .collectList()
                    .map(list -> list.stream().map(HdsDevicePositionEntity::getPositionCode).toList());
        }
        return Mono.just(new ArrayList<>());
    }

    public Flux<RoomStatusRes> findRoomInfos(String hotelCode, Set<String> positionCodes) {
        if (positionCodes.isEmpty()) return Flux.empty();

        Criteria criteria = Criteria.where(HdsDevicePositionFieldEnum.position_code.name()).in(positionCodes)
                .and(HdsDevicePositionFieldEnum.hotel_code.name()).is(hotelCode);

        return r2dbcEntityTemplate.select(Query.query(criteria), HdsDevicePositionEntity.class)
                .map(position ->{
                    RoomStatusRes vo = new RoomStatusRes();
                    vo.setPositionCode(position.getPositionCode());
                    vo.setPositionName(position.getPositionName());
                    vo.setCreateAt(position.getCreatedAt());
                    return vo;
                });
    }

    /**
     * 根据酒店编码和位置名称列表批量查询位置信息
     */
    public Mono<List<HdsDevicePositionEntity>> findByHotelCodeAndPositionNames(String hotelCode, List<String> positionNames) {
        if (CollUtil.isEmpty(positionNames)) {
            return Mono.just(new ArrayList<>());
        }

        Criteria criteria = Criteria.where(HdsDevicePositionFieldEnum.hotel_code.name()).is(hotelCode)
                .and(HdsDevicePositionFieldEnum.position_name.name()).in(positionNames)
                .and(HdsDevicePositionFieldEnum.device_app_type.name()).is(DeviceTypeEnum.ROOM.getCode());

        return r2dbcEntityTemplate.select(Query.query(criteria), HdsDevicePositionEntity.class)
                .collectList();
    }


}
