package com.wormhole.hotelds.api.hotel.web.dao;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.enums.EmployeeTypeEnum;
import com.wormhole.hotelds.api.hotel.web.model.req.EmployeeInfoQo;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsEmployeeTicketMappingQO;
import com.wormhole.hotelds.core.enums.AcceptTicketStatusEnum;
import com.wormhole.hotelds.core.enums.ServiceCategory;
import com.wormhole.hotelds.core.model.entity.HdsEmployeeTicketMappingEntity;
import com.wormhole.hotelds.core.model.entity.HdsEmployeeTicketMappingFieldEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.wormhole.hotelds.core.enums.AccountTypeEnum.EXTENSION;
import static com.wormhole.hotelds.core.enums.AccountTypeEnum.MAIN_SWITCHBOARD;
import static com.wormhole.hotelds.core.model.entity.HdsEmployeeTicketMappingFieldEnum.*;

/**
 * <AUTHOR>
 * @date 2025/4/16
 */
@Repository
@Slf4j
public class HdsEmployeeTicketMappingDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;


    public Mono<List<HdsEmployeeTicketMappingEntity>> findList(HdsEmployeeTicketMappingQO qo){

        Criteria criteria = getCriteria(qo);

        return r2dbcEntityTemplate.select(Query.query(criteria), HdsEmployeeTicketMappingEntity.class)
                .collectList()
                .map(list->{
                    if (CollectionUtil.isEmpty(list) || StringUtils.isBlank(qo.getAreaCode())) return list;
                    // 位置限制不为空的时候，过滤没有该位置的分机
                    return list.stream().filter(ele -> !checkNotAuth(ele, qo)).collect(Collectors.toList());
                });
    }

    private static boolean checkNotAuth(HdsEmployeeTicketMappingEntity ele, HdsEmployeeTicketMappingQO qo) {
        // 位置限制：分机，配置了位置限制，不包含目标位置。非公区
        boolean positionLimit =  ObjectUtil.equal(ele.getEmployeeType(), EXTENSION.getCode())
                && !qo.isAreaPublic()
                && StringUtils.isNotBlank(qo.getAreaCode())
                && (
                (StringUtils.isNotBlank(ele.getAreaCodes()) && !StringUtils.contains(ele.getAreaCodes(), qo.getAreaCode()))
                || StringUtils.isBlank(ele.getAreaCodes())
                );
        return positionLimit;
    }


    public Mono<HdsEmployeeTicketMappingEntity> findOne(HdsEmployeeTicketMappingQO qo){

        Criteria criteria = getCriteria(qo);

        return r2dbcEntityTemplate.selectOne(Query.query(criteria).limit(1), HdsEmployeeTicketMappingEntity.class)
                .switchIfEmpty(Mono.empty())
                .flatMap(ele->{
                    if (ele == null) return Mono.empty() ;
                    // 位置限制，当分机配置了位置限制时，需要过滤
                    if (checkNotAuth(ele, qo)){
                        // 分机没有当前位置的工单的权限
                        return Mono.empty() ;
                    }
                    return Mono.just(ele);
                });
    }

    @NotNull
    private static Criteria getCriteria(HdsEmployeeTicketMappingQO qo) {
        Criteria criteria = Criteria.empty();
        if(Objects.nonNull(qo.getAcceptTicketStatus())) {
            criteria = criteria.and(HdsEmployeeTicketMappingFieldEnum.accept_ticket_status.name()).is(qo.getAcceptTicketStatus());
        }
        if(Objects.nonNull(qo.getHotelCode())) {
            criteria = criteria.and(HdsEmployeeTicketMappingFieldEnum.hotel_code.name()).is(qo.getHotelCode());
        }
        if(Objects.nonNull(qo.getAccountTypeEnum())) {
            criteria = criteria.and(employee_type.name()).is(qo.getAccountTypeEnum().getCode());
        }
        if(StringUtils.isNotBlank(qo.getEmployId())) {
            criteria = criteria.and(employee_id.name()).is(qo.getEmployId());
        }
        if (CollectionUtil.isNotEmpty(qo.getEmployIdList())){
            criteria = criteria.and(employee_id.name()).in(qo.getEmployIdList());
        }

        if(Objects.nonNull(qo.getServiceCategory())) {
            // 获取服务类别
            ServiceCategory serviceCategory = ServiceCategory.getByCode(
                    qo.getServiceCategory(),
                    Objects.equals(qo.getUserType(), 1)
            );
            criteria = criteria.and(Criteria.where(employee_type.name()).is(MAIN_SWITCHBOARD.getCode())
                    .or(Criteria.where(employee_type.name()).is(EXTENSION.getCode())
                            .and(ticket_categories.name())
                            .like("%" + serviceCategory.getCode() + "%")));
        }
        return criteria;
    }

    public Mono<List<HdsEmployeeTicketMappingEntity>> findAvailableEmployee(EmployeeInfoQo infoQo){
        log.info("findAvailableEmployee infoQo:{}", JacksonUtils.writeValueAsString(infoQo));
        if (StringUtils.isBlank(infoQo.getHotelCode()) && CollectionUtil.isEmpty(infoQo.getEmployeeIdList())){
            return Mono.just(new ArrayList<>());
        }
        Criteria criteria = Criteria.empty();
        if(CollectionUtil.isNotEmpty(infoQo.getEmployeeIdList())) {
            criteria = criteria.and(HdsEmployeeTicketMappingFieldEnum.employee_id.name()).in(infoQo.getEmployeeIdList());
        }
        if(StringUtils.isNotBlank(infoQo.getHotelCode())) {
            criteria = criteria.and(HdsEmployeeTicketMappingFieldEnum.hotel_code.name()).is(infoQo.getHotelCode());
        }
        criteria = criteria.and(HdsEmployeeTicketMappingFieldEnum.accept_ticket_status.name()).is(0);
        // 如果是总机，直接返回总机；分机没有就返回总机
        return r2dbcEntityTemplate.select(Query.query(criteria), HdsEmployeeTicketMappingEntity.class)
                .collectList()
                .flatMap(list -> {
                    if (ObjectUtil.equal(infoQo.getEmployeeType(), EmployeeTypeEnum.MAIN_LINE.getCode())){
                        return Mono.just(list.stream().filter(ele -> ObjectUtil.equal(ele.getEmployeeType(), EmployeeTypeEnum.MAIN_LINE.getCode())).toList());
                    } else if (ObjectUtil.equal(infoQo.getEmployeeType(), EmployeeTypeEnum.EXTENSION.getCode())){
                        List<HdsEmployeeTicketMappingEntity> main = new ArrayList<>();
                        List<HdsEmployeeTicketMappingEntity> extension = new ArrayList<>();
                        for (HdsEmployeeTicketMappingEntity allEmployee : list) {
                            if (ObjectUtil.equal(allEmployee.getEmployeeType(), EmployeeTypeEnum.MAIN_LINE.getCode())){
                                main.add(allEmployee);
                            } else {
                                if (StringUtils.isNotBlank(infoQo.getTicketCategory()) && allEmployee.getTicketCategories().contains(infoQo.getTicketCategory())){
                                    extension.add(allEmployee);
                                }else if (StringUtils.isBlank(infoQo.getTicketCategory())){
                                    extension.add(allEmployee);
                                }
                            }
                        }
                        return Mono.just(extension.isEmpty() ? main : extension);
                    } else {
                        return Mono.just(list);
                    }
                });
    }

    public Mono<Long> updateReceiveStatusByUserId(HdsEmployeeTicketMappingQO qo, AcceptTicketStatusEnum acceptTicketStatusEnum) {
        Query query = Query.query(getCriteria(qo));
        Update update = Update.update(accept_ticket_status.name(), acceptTicketStatusEnum.getCode());
        return r2dbcEntityTemplate.update(query, update, HdsEmployeeTicketMappingEntity.class);
    }
}
