package com.wormhole.hotelds.api.hotel.web.dao;

import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.hotelds.core.model.entity.HdsHotelInfoEntity;
import com.wormhole.hotelds.core.model.entity.HdsHotelInfoFieldEnum;
import jakarta.annotation.Resource;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/4/17 19:11
 */

@Repository
public class HdsHotelInfoDao {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public Mono<HdsHotelInfoEntity> queryByHotelCode(String hotelCode) {
        Criteria criteria = Criteria.where(HdsHotelInfoFieldEnum.hotel_code.name()).is(hotelCode)
                .and(HdsHotelInfoFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId());
        return r2dbcEntityTemplate.select(Query.query(criteria), HdsHotelInfoEntity.class).next();
    }


    public Mono<List<HdsHotelInfoEntity>> findList(Set<String> hotelCodes) {
        Criteria criteria = Criteria.where(HdsHotelInfoFieldEnum.hotel_code.name()).in(hotelCodes)
                .and(HdsHotelInfoFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId());
        return r2dbcEntityTemplate.select(Query.query(criteria), HdsHotelInfoEntity.class).collectList();
    }

    /**
     * 查询所有营业中的酒店
     *
     * @return 所有营业中的酒店列表
     */
    public Mono<List<HdsHotelInfoEntity>> findAllActiveHotels() {
        Criteria criteria = Criteria.where(HdsHotelInfoFieldEnum.book_flag.name()).is(1) // 1-营业
                .and(HdsHotelInfoFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId())
                .and(HdsHotelInfoFieldEnum.status.name()).is(RowStatusEnum.VALID.getId());
        return r2dbcEntityTemplate.select(Query.query(criteria), HdsHotelInfoEntity.class).collectList();
    }
}
