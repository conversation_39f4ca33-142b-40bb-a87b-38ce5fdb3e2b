package com.wormhole.hotelds.api.hotel.web.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Preconditions;
import com.wormhole.channel.consts.message.CallInfoMessage;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.hotelds.api.hotel.constant.SystemConstant;
import com.wormhole.hotelds.api.hotel.constant.ExpiredFlagEnum;
import com.wormhole.hotelds.api.hotel.constant.TicketHandleMethodEnum;
import com.wormhole.hotelds.api.hotel.web.model.req.GetTopNTicketReq;
import com.wormhole.hotelds.api.hotel.web.model.req.RoomTicketPriorityDTO;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsDevicePositionQO;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsTicketQO;
import com.wormhole.hotelds.api.hotel.req.TicketAdminPageReq;
import com.wormhole.hotelds.api.hotel.req.TicketInfoReq;
import com.wormhole.hotelds.core.enums.ServiceCategory;
import com.wormhole.hotelds.core.enums.ServiceType;
import com.wormhole.hotelds.core.enums.TicketStatus;
import com.wormhole.hotelds.core.enums.UserType;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketEntity;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketFieldEnum;
import com.wormhole.hotelds.plugin.model.dto.TicketStatisticsDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.r2dbc.core.BeanPropertyRowMapper;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;
import reactor.core.publisher.Flux;

import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/8
 */
@Repository
@Slf4j
public class HdsServiceTicketDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    @Autowired
    private HdsDevicePositionDao hdsDevicePositionDao;

    public Mono<Tuple2<List<RoomTicketPriorityDTO>, Long>> findRoomsWithPriorityAndCountByConditions(
            String positionName,
            String hotelCode,
            List<String> categories,
            int page,
            int size, List<String> limitPositionCodes) {

        // 如果提供了positionName，先通过模糊查询找到匹配的房间位置
        if (StringUtils.isNotBlank(positionName)) {
            HdsDevicePositionQO query = new HdsDevicePositionQO();
            query.setHotelCode(hotelCode);
            query.setPositionName(positionName);

            return hdsDevicePositionDao.findList(query)
                    .flatMap(positionEntities -> {
                        // 如果没有找到匹配的位置，返回空结果
                        if (positionEntities == null || positionEntities.isEmpty()) {
                            return Mono.just(Tuples.of(Collections.emptyList(), 0L));
                        }

                        // 提取所有匹配的positionCode
                        List<String> positionCodes = positionEntities.stream()
                                .map(HdsDevicePositionEntity::getPositionCode)
                                .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(limitPositionCodes)){
                            positionCodes.addAll(limitPositionCodes);
                        }
                        // 调用修改后的查询方法，使用IN条件
                        return findRoomsByPositionCodesAndPriorityWithTotal(positionCodes, hotelCode, categories, page, size);
                    });
        } else {
            // 如果没有提供positionName，执行常规查询，不需要positionCode条件
            return findRoomsByPositionCodesAndPriorityWithTotal(limitPositionCodes, hotelCode, categories, page, size);
        }
    }

    private Mono<Tuple2<List<RoomTicketPriorityDTO>, Long>> findRoomsByPositionCodesAndPriorityWithTotal(
            List<String> positionCodes,
            String hotelCode,
            List<String> categories,
            int page,
            int size) {

        // 获取分页数据
        Mono<List<RoomTicketPriorityDTO>> itemsMono = findRoomsByPositionCodesAndPriority(
                positionCodes, hotelCode, categories, page, size);

        // 获取总条数
        Mono<Long> totalMono = countRoomsByPositionCodesAndPriority(
                positionCodes, hotelCode, categories);

        // 合并结果
        return Mono.zip(itemsMono, totalMono);
    }

    private Mono<Long> countRoomsByPositionCodesAndPriority(
            List<String> positionCodes,
            String hotelCode,
            List<String> subcategories) {

        // 直接使用DatabaseClient实现复杂SQL查询
        DatabaseClient databaseClient = r2dbcEntityTemplate.getDatabaseClient();

        // 构建统计用SQL
        StringBuilder sqlBuilder = new StringBuilder("""
            SELECT COUNT(DISTINCT position_code) as total
            FROM hds_service_tickets
            WHERE hotel_code = :hotelCode
            AND row_status = 1
            AND expired_flag = 0
            AND end_of_call_time is not null
            """);

        // 动态添加positionCodes条件（如果有）
        if (positionCodes != null && !positionCodes.isEmpty()) {
            sqlBuilder.append("AND position_code in (:positionCodes) ");
        }

        // 动态添加subcategories条件
        if (subcategories != null && !subcategories.isEmpty()) {
            sqlBuilder.append("AND service_category in (:subcategories) ");
        }

        // 准备查询
        DatabaseClient.GenericExecuteSpec executeSpec = databaseClient.sql(sqlBuilder.toString())
                .bind("hotelCode", hotelCode);

        // 如果有positionCodes，绑定这个参数
        if (positionCodes != null && !positionCodes.isEmpty()) {
            executeSpec = executeSpec.bind("positionCodes", positionCodes);
        }

        // 如果有subcategories，绑定这个参数
        if (subcategories != null && !subcategories.isEmpty()) {
            executeSpec = executeSpec.bind("subcategories", subcategories);
        }

        // 执行查询并返回总数
        return executeSpec.map((row, metadata) -> {
                    return row.get("total", Long.class);
                })
                .one()
                .defaultIfEmpty(0L);
    }

    private Mono<List<RoomTicketPriorityDTO>> findRoomsByPositionCodesAndPriority(
            List<String> positionCodes,
            String hotelCode,
            List<String> subcategories,
            int page,
            int size) {

        // 直接使用DatabaseClient实现复杂SQL查询
        DatabaseClient databaseClient = r2dbcEntityTemplate.getDatabaseClient();

        // 构建基础SQL查询
        StringBuilder sqlBuilder = new StringBuilder("""
        SELECT position_code, 
               MIN(CASE 
                   WHEN priority = 1 AND status = 0 THEN 1
                   WHEN status = 0 THEN 2
                   ELSE 3 
               END) as room_priority,
               MIN(end_of_call_time) as earliest_call_time
        FROM hds_service_tickets
        WHERE hotel_code = :hotelCode
        AND row_status = 1
        AND expired_flag = 0
        AND end_of_call_time is not null
        """);

        // 动态添加positionCodes条件（如果有）
        if (positionCodes != null && !positionCodes.isEmpty()) {
            sqlBuilder.append("AND position_code in (:positionCodes) ");
        }

        // 动态添加subcategories条件
        if (subcategories != null && !subcategories.isEmpty()) {
            sqlBuilder.append("AND service_category in (:subcategories) ");
        }

        // 添加排序和分页
        sqlBuilder.append("""
        GROUP BY position_code
        ORDER BY room_priority ASC, earliest_call_time ASC
        LIMIT :limit OFFSET :offset
        """);

        // 准备查询
        DatabaseClient.GenericExecuteSpec executeSpec = databaseClient.sql(sqlBuilder.toString())
                .bind("hotelCode", hotelCode)
                .bind("limit", size)
                .bind("offset", (page - 1) * size);

        // 如果有positionCodes，绑定这个参数
        if (positionCodes != null && !positionCodes.isEmpty()) {
            executeSpec = executeSpec.bind("positionCodes", positionCodes);
        }

        // 如果有subcategories，绑定这个参数
        if (subcategories != null && !subcategories.isEmpty()) {
            executeSpec = executeSpec.bind("subcategories", subcategories);
        }

        // 执行查询并映射结果
        return executeSpec.map((row, metadata) -> {
                    RoomTicketPriorityDTO priority = new RoomTicketPriorityDTO();
                    priority.setPositionCode(row.get("position_code", String.class));
                    priority.setRoomPriority(row.get("room_priority", Integer.class));
                    priority.setEarliestCallTime(row.get("earliest_call_time", LocalDateTime.class));
                    return priority;
                })
                .all().collectList();
    }

    /**
     * 获取通话中用户创建的未反馈的[服务]工单分类
     *
     * @return
     */
    public Mono<Set<String>> getServiceTicketsByCall(CallInfoMessage callbackReq, ServiceType serviceType) {
        // 构建查询条件
        Criteria criteria = Criteria.where(HdsServiceTicketFieldEnum.rtc_room_id.name()).is(callbackReq.getRtcRoomId())
                .and(HdsServiceTicketFieldEnum.service_type.name()).is(serviceType.name())
                .and(HdsServiceTicketFieldEnum.expired_flag.name()).is(0)
//                .and(HdsServiceTicketFieldEnum.feedback_status.name()).isNull()
                .and(HdsServiceTicketFieldEnum.status.name()).is(0)
                .and(HdsServiceTicketFieldEnum.user_type.name()).is(0);
        // 构建查询对象
        Query query = Query.query(criteria).columns(HdsServiceTicketFieldEnum.service_category.name()).sort(Sort.by(Sort.Direction.ASC, HdsServiceTicketFieldEnum.id.name()));
        // 执行查询并返回结果列表
        return r2dbcEntityTemplate.select(query, HdsServiceTicketEntity.class)
                .collectList()
                .flatMap(list -> Mono.just(list.stream().map(ele -> ServiceCategory.getChineseNameByCode(ele.getServiceCategory())).collect(Collectors.toSet())));
    }

    /**
     * 获取通话中用户创建的[未反馈]的工单
     *
     * @param rtcRoomId
     * @param serviceTypes
     * @return
     */
    public Mono<List<HdsServiceTicketEntity>> completedTicketsInCallByReq(String rtcRoomId, List<String> serviceTypes) {
        // 构建查询条件
        Criteria criteria = Criteria.where(HdsServiceTicketFieldEnum.rtc_room_id.name()).is(rtcRoomId)
                .and(HdsServiceTicketFieldEnum.service_type.name()).in(serviceTypes)
                .and(HdsServiceTicketFieldEnum.expired_flag.name()).is(0)
//                .and(HdsServiceTicketFieldEnum.feedback_status.name()).isNull()
                .and(HdsServiceTicketFieldEnum.status.name()).is(1)
                .and(HdsServiceTicketFieldEnum.user_type.name()).is(0);
        // 构建查询对象
        Query query = Query.query(criteria).sort(Sort.by(Sort.Direction.ASC, HdsServiceTicketFieldEnum.id.name()));
        // 执行查询并返回结果列表
        return r2dbcEntityTemplate.select(query, HdsServiceTicketEntity.class)
                .collectList();
    }


    /**
     * 获取服务工单
     */
    public Mono<List<HdsServiceTicketEntity>> findList(HdsTicketQO hdsTicketQO) {
        Criteria criteria = getCriteria(hdsTicketQO);
        return r2dbcEntityTemplate.select(Query.query(criteria), HdsServiceTicketEntity.class).collectList();
    }

    /**
     * 分页查询工单数据
     */
    public Flux<HdsServiceTicketEntity> findListPaged(HdsTicketQO qo, int page, int pageSize) {
        Criteria criteria = Criteria.where("created_at").greaterThanOrEquals(qo.getStart())
            .and("created_at").lessThan(qo.getEnd());
        if (qo.getUserType() != null){
            criteria = criteria.and(HdsServiceTicketFieldEnum.user_type.name()).is(qo.getUserType());
        }
        Query query = Query.query(criteria)
            .limit(pageSize)
            .offset((long) page * pageSize);
        return r2dbcEntityTemplate.select(query, HdsServiceTicketEntity.class);
    }


    public Mono<Long> fillCallEndTime(HdsTicketQO hdsTicketQO, LocalDateTime localDateTime) {
        Criteria criteria = getCriteria(hdsTicketQO);

        return r2dbcEntityTemplate.update(Query.query(criteria)
                , Update.update(HdsServiceTicketFieldEnum.end_of_call_time.name(), localDateTime), HdsServiceTicketEntity.class);
    }
    /**
     * 获取服务工单
     */
    public Mono<HdsServiceTicketEntity> findOne(HdsTicketQO hdsTicketQO) {
        Criteria criteria = getCriteria(hdsTicketQO);
        return r2dbcEntityTemplate.selectOne(Query.query(criteria).limit(1), HdsServiceTicketEntity.class);
    }

    @NotNull
    private static Criteria getCriteria(HdsTicketQO hdsTicketQO) {
        Criteria criteria = Criteria.where(HdsServiceTicketFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId());
        if(StringUtils.isNotBlank(hdsTicketQO.getReturnRoomId())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.return_room_id.name()).is(hdsTicketQO.getReturnRoomId());
        }
        if (CollUtil.isNotEmpty(hdsTicketQO.getPositionCodes())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.position_code.name()).in(hdsTicketQO.getPositionCodes());
        }
        if(StringUtils.isNotBlank(hdsTicketQO.getPositionCode())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.position_code.name()).is(hdsTicketQO.getPositionCode());
        }
        if (StringUtils.isNotBlank(hdsTicketQO.getHotelCode())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.hotel_code.name()).is(hdsTicketQO.getHotelCode());
        }
        if (CollUtil.isNotEmpty(hdsTicketQO.getHotelCodes())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.hotel_code.name()).in(hdsTicketQO.getHotelCodes());
        }

        if (StringUtils.isNotBlank(hdsTicketQO.getRtcRoomId())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.rtc_room_id.name()).is(hdsTicketQO.getRtcRoomId());
        }
        if(Objects.nonNull(hdsTicketQO.getTicketStatus())){
            criteria = criteria.and(HdsServiceTicketFieldEnum.status.name()).is(hdsTicketQO.getTicketStatus());
        }
        if(Objects.nonNull(hdsTicketQO.getClosedLoopLevel())){
            criteria = criteria.and(HdsServiceTicketFieldEnum.closed_loop_level.name()).is(hdsTicketQO.getClosedLoopLevel());
        }
        if(Objects.nonNull(hdsTicketQO.getExpiredFlag())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.expired_flag.name()).is(hdsTicketQO.getExpiredFlag());
        }

        if(CollUtil.isNotEmpty(hdsTicketQO.getTicketIds())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.id.name()).in(hdsTicketQO.getTicketIds());
        }

        if(CollUtil.isNotEmpty(hdsTicketQO.getTicketNos())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.ticket_no.name()).in(hdsTicketQO.getTicketNos());
        }
        if (Objects.equals(hdsTicketQO.getHasEndOfCall(), true)) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.end_of_call_time.name()).isNotNull();
        } else if (Objects.equals(hdsTicketQO.getHasEndOfCall(), false)) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.end_of_call_time.name()).isNull();
        }

//        if(Objects.equals(hdsTicketQO.getHasFeedback(),true)) {
//            criteria = criteria.and(HdsServiceTicketFieldEnum.feedback_status.name()).isNotNull();
//        }else {
//            criteria = criteria.and(HdsServiceTicketFieldEnum.feedback_status.name()).isNull();
//        }

        if(StringUtils.isNotBlank(hdsTicketQO.getServiceSubcategory())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.service_subcategory.name()).is(hdsTicketQO.getServiceSubcategory());
        }
        if(StringUtils.isNotBlank(hdsTicketQO.getServiceCategory())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.service_category.name()).is(hdsTicketQO.getServiceCategory());
        }
        if(CollUtil.isNotEmpty(hdsTicketQO.getServiceCategories())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.service_category.name()).in(hdsTicketQO.getServiceCategories());
        }
        if (StringUtils.isNotBlank(hdsTicketQO.getServiceType())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.service_type.name()).is(hdsTicketQO.getServiceType());
        }

        if (hdsTicketQO.getStart() != null && hdsTicketQO.getEnd() != null) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.created_at.name()).greaterThanOrEquals(hdsTicketQO.getStart())
                    .and(HdsServiceTicketFieldEnum.created_at.name()).lessThan(hdsTicketQO.getEnd());
        }
        return criteria;
    }

    /**
     * 查找最新工单
     */
    public Mono<HdsServiceTicketEntity> findLatestTicket(String hotelCode, List<String> includeServiceCategory, List<String> excludeServiceCategory) {
        Criteria criteria = Criteria.where(HdsServiceTicketFieldEnum.hotel_code.name()).is(hotelCode)
                .and(HdsServiceTicketFieldEnum.row_status.name()).is(1)
                .and(HdsServiceTicketFieldEnum.end_of_call_time.name()).isNotNull()
                .and(HdsServiceTicketFieldEnum.expired_flag.name()).is(0); // 确保只查询正常状态的记录

        if (CollUtil.isNotEmpty(includeServiceCategory)) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.service_category.name()).in(includeServiceCategory);
        }
        if (CollUtil.isNotEmpty(excludeServiceCategory)) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.service_category.name()).notIn(excludeServiceCategory);
        }

        Query query = Query.query(criteria)
                .sort(Sort.by(Sort.Direction.DESC, HdsServiceTicketFieldEnum.end_of_call_time.name()))
                .limit(1); // 只获取第一条记录

        return r2dbcEntityTemplate.selectOne(query, HdsServiceTicketEntity.class); // 获取第一条结果
    }

    public Mono<Boolean> hasPriorityTicket(String hotelCode) {
        Criteria criteria = Criteria.where(HdsServiceTicketFieldEnum.hotel_code.name()).is(hotelCode)
                .and(HdsServiceTicketFieldEnum.row_status.name()).is(1)
                .and(HdsServiceTicketFieldEnum.end_of_call_time.name()).isNotNull()
                .and(HdsServiceTicketFieldEnum.status.name()).is(0)
                .and(HdsServiceTicketFieldEnum.expired_flag.name()).is(0)
                .and(HdsServiceTicketFieldEnum.priority.name()).is(1); // 确保只查询正常状态的记录

        return r2dbcEntityTemplate.selectOne(Query.query(criteria).limit(1), HdsServiceTicketEntity.class)
                .hasElement()
                .map(exist -> exist ? Boolean.TRUE : Boolean.FALSE)
                .defaultIfEmpty(Boolean.FALSE); // 如果没有找到记录，返回false
    }

    public Mono<List<HdsServiceTicketEntity>> findPageList(TicketAdminPageReq req) {
        return  r2dbcEntityTemplate.select(buildTicketPageQuery(req), HdsServiceTicketEntity.class)
                .collectList();
    }

    public Mono<Long> countByReq(TicketAdminPageReq req) {
        return r2dbcEntityTemplate.count(buildTicketPageQuery(req), HdsServiceTicketEntity.class);
    }

    private Query buildTicketPageQuery(TicketAdminPageReq req){
        Criteria criteria = Criteria.empty();
        // 添加过滤条件
        if (StringUtils.isNotBlank(req.getTicketNo())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.ticket_no.name()).is(req.getTicketNo());
        }

        if (req.getStartTime() != null) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.created_at.name()).greaterThanOrEquals(DateUtil.parseLocalDateTime(req.getStartTime()));
        }
        if (req.getEndTime() != null) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.created_at.name()).lessThanOrEquals(DateUtil.parseLocalDateTime(req.getEndTime()));
        }
        if (StringUtils.isNotBlank(req.getHotelCode())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.hotel_code.name()).is(req.getHotelCode());
        }
        if (CollectionUtil.isNotEmpty(req.getDeviceIdList())){
            criteria = criteria.and(HdsServiceTicketFieldEnum.device_id.name()).in(req.getDeviceIdList());
        }
//        if (req.getFeedbackStatus() != null){
//            criteria = criteria.and(HdsServiceTicketFieldEnum.feedback_status.name()).is(req.getFeedbackStatus());
//        }
        // todo  修改变量名
        Criteria byCode = TicketHandleMethodEnum.getByCode(req.getHandleMethod());
        if(byCode != null){
            criteria = criteria.and(byCode);
        }
        if (req.getClosedLoopLevel() != null){
            criteria = criteria.and(HdsServiceTicketFieldEnum.closed_loop_level.name()).is(req.getClosedLoopLevel());
        }
        if (req.getStatus() != null){
            criteria = criteria.and(HdsServiceTicketFieldEnum.status.name()).is(req.getStatus());
        }
        if (req.getExpiredFlag() != null){
            criteria = criteria.and(HdsServiceTicketFieldEnum.expired_flag.name()).is(req.getExpiredFlag());
        }
        if (StringUtils.isNotBlank(req.getTicketCategory())){
            criteria = criteria.and(HdsServiceTicketFieldEnum.service_category.name()).is(req.getTicketCategory());
        }
        if (StringUtils.isNotBlank(req.getRtcRoomId())){
            criteria = criteria.and(HdsServiceTicketFieldEnum.rtc_room_id.name()).is(req.getRtcRoomId());
        }

        if (CollectionUtil.isNotEmpty(req.getPositionCodes())){
            criteria = criteria.and(HdsServiceTicketFieldEnum.position_code.name()).in(req.getPositionCodes());
        }
        if (CollectionUtil.isNotEmpty(req.getTicketCategoryList())){
            criteria = criteria.and(HdsServiceTicketFieldEnum.service_category.name()).in(req.getTicketCategoryList());
        }
        // 设置排序
        Sort sort = Sort.by(Sort.Direction.DESC, HdsServiceTicketFieldEnum.id.name());
        // 设置分页
        PageRequest pageRequest = PageRequest.of(req.getCurrent() - 1, req.getPageSize());

        return Query.query(criteria).with(pageRequest).sort(sort);

    }

    public Mono<Set<String>> selectNotExpired(String hotelCode, String positionCode, String conversationId, Integer targetStatus,Integer userType) {
        // 构建查询条件
        Criteria criteria = buildBaseCriteria(hotelCode, positionCode,conversationId,targetStatus, userType);
        Query logsQuery = Query.query(criteria);

        return r2dbcEntityTemplate.select(logsQuery, HdsServiceTicketEntity.class).collectList()
                .flatMap(list -> Mono.just(list.stream().map(HdsServiceTicketEntity::getTicketNo).collect(Collectors.toSet())));
    }

    public Mono<Long> updateDeviceTicketExpired(String hotelCode, String positionCode, String conversationId, Integer targetStatus,Integer userType) {
        Criteria criteria = buildBaseCriteria(hotelCode, positionCode,conversationId, targetStatus, userType);
        // 构建更新操作
        Update update = Update.update(HdsServiceTicketFieldEnum.expired_flag.name(), ExpiredFlagEnum.EXPIRED.getCode());
        // 执行更新
        return r2dbcEntityTemplate.update(Query.query(criteria), update, HdsServiceTicketEntity.class);
    }

    private Criteria buildBaseCriteria(String hotelCode,
                                       String positionCode,
                                       String conversationId, Integer targetStatus,Integer userType) {
        Criteria criteria = Criteria.empty();

        if (StringUtils.isNotBlank(hotelCode)) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.hotel_code.name()).is(hotelCode);
        }

        if (StringUtils.isNotBlank(positionCode)) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.position_code.name()).is(positionCode);
        }

        if (StringUtils.isNotBlank(conversationId)) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.conversation_id.name()).is(conversationId);
        }
        if (targetStatus != null){
            criteria = criteria.and(HdsServiceTicketFieldEnum.status.name()).is(targetStatus);
        }
        if (userType != null){
            criteria = criteria.and(HdsServiceTicketFieldEnum.user_type.name()).is(userType);
        }
        criteria = criteria.and(HdsServiceTicketFieldEnum.expired_flag.name()).is(ExpiredFlagEnum.NORMAL.getCode());
        return criteria;
    }

    public Mono<Long> updateTicketFeedbackStatus(String ticketNo, Integer feedbackStatus) {
        Preconditions.checkArgument(StringUtils.isNotBlank(ticketNo) && feedbackStatus != null, "参数错误");
        Criteria criteria = Criteria.where(HdsServiceTicketFieldEnum.ticket_no.name()).is(ticketNo);
        // 构建更新操作
        Update update = Update.update("", feedbackStatus);
        // 执行更新
        return r2dbcEntityTemplate.update(Query.query(criteria), update, HdsServiceTicketEntity.class);
    }

    public Mono<HdsServiceTicketEntity> findOneTicket(TicketInfoReq req) {
        // 构建查询条件
        Criteria criteria = Criteria.empty();
        if (req.getId() != null) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.id.name()).is(req.getId());
        }
        if (StringUtils.isNotBlank(req.getTicketNo())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.ticket_no.name()).is(req.getTicketNo());
        }
        Query logsQuery = Query.query(criteria).limit(1);
        return r2dbcEntityTemplate.selectOne(logsQuery, HdsServiceTicketEntity.class);
    }

    public Mono<Long> updateTicketOverdue(String ticketNo) {
        Criteria criteria = Criteria.where(HdsServiceTicketFieldEnum.ticket_no.name()).is(ticketNo);
        // 构建更新操作
        Update update = Update.update(HdsServiceTicketFieldEnum.overdue_flag.name(), SystemConstant.ONE);
        // 执行更新
        return r2dbcEntityTemplate.update(Query.query(criteria), update, HdsServiceTicketEntity.class);
    }

    public Mono<Long> updateTicketTagFlag(String ticketNo, Integer tagFlag) {
        Criteria criteria = Criteria.where(HdsServiceTicketFieldEnum.ticket_no.name()).is(ticketNo);

        Update update = Update.update(HdsServiceTicketFieldEnum.tag_flag.name(), tagFlag);

        // 执行更新
        return r2dbcEntityTemplate.update(Query.query(criteria), update, HdsServiceTicketEntity.class);
    }

    public Mono<List<HdsServiceTicketEntity>> getTopNFocusTickets(GetTopNTicketReq req) {
        // 直接使用DatabaseClient实现复杂SQL查询
        DatabaseClient databaseClient = r2dbcEntityTemplate.getDatabaseClient();

        // 构建基础SQL查询
        StringBuilder sqlBuilder = new StringBuilder("""
            SELECT
                t.*
            FROM
                hds_service_tickets t
            WHERE
                t.hotel_code = :hotelCode
                AND t.status = :status
                AND t.expired_flag = 0  AND t.row_status = 1
                AND (t.service_category in ('COMPLAINT' ,'EMERGENCY') or t.overdue_flag = 1 or t.reply_exception_type = 2 )
        """);

        if (ObjectUtil.equal(req.getStatus(), TicketStatus.COMPLETED.getCode())) {
            sqlBuilder.append(" ORDER BY t.completion_time desc");
        }

        if (ObjectUtil.equal(req.getStatus(), TicketStatus.PENDING.getCode())) {
            sqlBuilder.append("""
            ORDER BY
                case  when t.service_category = 'EMERGENCY' then 2
                when t.service_category = 'COMPLAINT' then 1
                else 0 end  DESC, reply_exception_type desc,
                overdue_flag desc,
                t.created_at ASC
            """);
        }
        if (req.getLimit() != null){
            // 添加排序和分页
            sqlBuilder.append(" LIMIT ").append(req.getLimit());
        }

        return databaseClient.sql(sqlBuilder.toString())
                .bind("hotelCode", req.getHotelCode())
                .bind("status", req.getStatus())
                .map((row, rowMetadata) -> new BeanPropertyRowMapper<>(HdsServiceTicketEntity.class).apply(row))
                .all()
                .collectList();
    }

    /**
     * 查询异常工单统计（紧急、客诉、带客诉预警）
     *
     * @param searchDate 查询日期
     * @return 酒店编码和异常工单数量的映射
     */
    public Mono<List<TicketStatisticsDTO>> getAbnormalTicketStatistics(LocalDate searchDate) {
        LocalDateTime startTime = searchDate.atStartOfDay();
        LocalDateTime endTime = searchDate.plusDays(1).atStartOfDay();

        // 构建查询条件：时间范围 + 异常工单条件
        Criteria criteria = Criteria.where(HdsServiceTicketFieldEnum.created_at.name()).greaterThanOrEquals(startTime)
                .and(HdsServiceTicketFieldEnum.created_at.name()).lessThan(endTime)
                .and(HdsServiceTicketFieldEnum.row_status.name()).is(1)
                .and(HdsServiceTicketFieldEnum.conversation_id.name()).isNotNull()
                .and(HdsServiceTicketFieldEnum.status.name()).is(0)
                .and(
                    // 紧急或客诉工单
                    Criteria.where(HdsServiceTicketFieldEnum.service_category.name()).in(Arrays.asList(ServiceCategory.EMERGENCY.getCode(), ServiceCategory.COMPLAINT.getCode()))
                    // 或者客诉预警工单（reply_exception_type != 0）
                    .or(HdsServiceTicketFieldEnum.reply_exception_type.name()).not(0)
                );

        Query query = Query.query(criteria);

        return r2dbcEntityTemplate.select(query, HdsServiceTicketEntity.class)
                .collectList()
                .map(tickets -> {
                    // 按酒店编码分组统计
                    Map<String, Long> countMap = tickets.stream()
                            .collect(Collectors.groupingBy(
                                    HdsServiceTicketEntity::getHotelCode,
                                    Collectors.counting()
                            ));

                    return countMap.entrySet().stream()
                            .map(entry -> {
                                TicketStatisticsDTO dto = new TicketStatisticsDTO();
                                dto.setHotelCode(entry.getKey());
                                dto.setCount(entry.getValue().intValue());
                                return dto;
                            })
                            .collect(Collectors.toList());
                });
    }

    /**
     * 查询正常工单统计（闭环级别为2，且不属于异常类别）
     *
     * @param searchDate 查询日期
     * @return 酒店编码和正常工单数量的映射
     */
    public Mono<List<TicketStatisticsDTO>> getNormalTicketStatistics(LocalDate searchDate) {
        LocalDateTime startTime = searchDate.atStartOfDay();
        LocalDateTime endTime = searchDate.plusDays(1).atStartOfDay();

        // 构建查询条件：时间范围 + 正常工单条件
        Criteria criteria = Criteria.where(HdsServiceTicketFieldEnum.created_at.name()).greaterThanOrEquals(startTime)
                .and(HdsServiceTicketFieldEnum.created_at.name()).lessThan(endTime)
                .and(HdsServiceTicketFieldEnum.row_status.name()).is(1)
                .and(HdsServiceTicketFieldEnum.service_category.name()).notIn(Arrays.asList(ServiceCategory.EMERGENCY.getCode(), ServiceCategory.COMPLAINT.getCode()))
                .and(HdsServiceTicketFieldEnum.reply_exception_type.name()).is(0)
                .and(HdsServiceTicketFieldEnum.status.name()).is(0)
                .and(HdsServiceTicketFieldEnum.conversation_id.name()).isNotNull()
                .and(HdsServiceTicketFieldEnum.closed_loop_level.name()).is(2);

        Query query = Query.query(criteria);

        return r2dbcEntityTemplate.select(query, HdsServiceTicketEntity.class)
                .collectList()
                .map(tickets -> {
                    // 按酒店编码分组统计
                    Map<String, Long> countMap = tickets.stream()
                            .collect(Collectors.groupingBy(
                                    HdsServiceTicketEntity::getHotelCode,
                                    Collectors.counting()
                            ));

                    return countMap.entrySet().stream()
                            .map(entry -> {
                                TicketStatisticsDTO dto = new TicketStatisticsDTO();
                                dto.setHotelCode(entry.getKey());
                                dto.setCount(entry.getValue().intValue());
                                return dto;
                            })
                            .collect(Collectors.toList());
                });
    }


    public Flux<String> findAbnormalTicketRooms(String hotelCode, LocalDate date) {
        LocalDateTime start = date.atStartOfDay();
        LocalDateTime end = start.plusDays(1);
        String sql = """
            SELECT DISTINCT position_code FROM hds_service_tickets
            WHERE created_at >= :start AND created_at < :end
              AND hotel_code = :hotelCode
              AND (service_category IN ('EMERGENCY', 'COMPLAINT') or reply_exception_type != 0)
              AND status = :status
              AND conversation_id is not null
        """;

        return r2dbcEntityTemplate.getDatabaseClient().sql(sql)
                .bind("start", start)
                .bind("end", end)
                .bind("hotelCode", hotelCode)
                .bind("status", 0)
                .map(row -> row.get("position_code", String.class))
                .all();
    }

    public Flux<String> findNormalTicketRooms(String hotelCode, LocalDate date) {
        LocalDateTime start = date.atStartOfDay();
        LocalDateTime end = start.plusDays(1);
        String sql = """
            SELECT DISTINCT position_code FROM hds_service_tickets
            WHERE created_at >= :start AND created_at < :end
              AND hotel_code = :hotelCode
              AND service_category NOT IN ('EMERGENCY', 'COMPLAINT')
              AND closed_loop_level = :closedLoopLevel
              AND status = :status              
              AND reply_exception_type = :replyExceptionType
              AND conversation_id is not null
        """;

        return r2dbcEntityTemplate.getDatabaseClient().sql(sql)
                .bind("start", start)
                .bind("end", end)
                .bind("hotelCode", hotelCode)
                .bind("closedLoopLevel", 2)
                .bind("status", 0)
                .bind("replyExceptionType", 0)
                .map(row -> row.get("position_code", String.class))
                .all();
    }

    public Mono<List<HdsServiceTicketEntity>> findTicketsByTicketNos(String hotelCode, List<String> ticketNo) {

        // 构建查询条件：时间范围 + 正常工单条件
        Criteria criteria = Criteria.where(HdsServiceTicketFieldEnum.hotel_code.name()).is(hotelCode)
                .and(HdsServiceTicketFieldEnum.ticket_no.name()).in(ticketNo)
                .and(HdsServiceTicketFieldEnum.row_status.name()).is(1);

        Query query = Query.query(criteria);

        return r2dbcEntityTemplate.select(query, HdsServiceTicketEntity.class)
                .collectList();
    }

}
