package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * WORMHOLE-批量创建不同位置同一需求工单请求
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchCreatePositionTicketReq implements Serializable {
    
    /**
     * 工单请求信息
     */
    private TicketRequestInfo ticketRequest;
    
    /**
     * 位置列表
     */
    private List<LocationInfo> locationList;
    
    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TicketRequestInfo implements Serializable {
        /**
         * 服务类型
         */
        private String serviceType;
        
        /**
         * 服务分类
         */
        private String serviceCategory;
        
        /**
         * 服务子分类
         */
        private String serviceSubcategory;
        
        /**
         * 服务关键词
         */
        private List<String> serviceKeywords;
        
        /**
         * 闭环级别
         */
        private Integer closedLoopLevel;
        
        /**
         * 回复异常类型
         */
        private Integer replyExceptionType;
        
        /**
         * 客人要求（中文）
         */
        private String guestRequest;
        
        /**
         * 客人要求（英文）
         */
        private String guestRequestEn;
        
        /**
         * 客人要求（印尼语）
         */
        private String guestRequestIdn;
        private String userId;
        private String userName;

        private Integer userType;
    }
    
    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LocationInfo implements Serializable {
        /**
         * 酒店编码
         */
        private String hotelCode;
        
        /**
         * 位置编码列表
         */
        private List<PositionInfo> positionCodes;
    }
    
    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PositionInfo implements Serializable {
        /**
         * 第三方房间号
         */
        private String thirdRoomNo;
        
        /**
         * 位置编码
         */
        private String positionCode;
        
        /**
         * 位置名称
         */
        private String positionName;
    }
}
