package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * WORMHOLE-批量房间映射请求
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchRoomMappingReq implements Serializable {

    /**
     * 映射类型：1-BDW到虫洞，2-虫洞到BDW
     */
    private Integer mappingType;

    /**
     * 第三方酒店编码
     */
    private String thirdHotelCode;

    /**
     * 虫洞系统酒店编码
     */
    private String hotelCode;

    /**
     * 位置列表
     */
    private List<PositionMappingData> positionList = new ArrayList<>();

    private String thirdRoomNo;

    private List<String> thirdRoomNoList = new ArrayList<>();

    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PositionMappingData implements Serializable {
        /**
         * 第三方房间号
         */
        private String thirdRoomNo;

        /**
         * 位置编码
         */
        private String positionCode;

        /**
         * 位置名称
         */
        private String positionName;

        /**
         * 客人要求
         */
        private String guestRequest;
    }
}




