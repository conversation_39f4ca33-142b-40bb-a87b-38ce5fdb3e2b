package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * WORMHOLE-批量房间映射请求（包含映射类型）
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchRoomMappingWithTypeReq implements Serializable {
    
    /**
     * 映射类型：1-BDW到虫洞，2-虫洞到BDW
     */
    private Integer mappingType;
    
    /**
     * 映射数据
     */
    private List<BatchRoomMappingReq> data;
}
