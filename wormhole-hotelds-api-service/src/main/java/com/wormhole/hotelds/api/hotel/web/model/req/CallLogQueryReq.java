package com.wormhole.hotelds.api.hotel.web.model.req;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CallLogQueryReq  implements Serializable {

    private String hotelCode;

    private String positionCode;

    private  String searchDate;

    private Integer pageSize;

    private Integer currentPage;

    //用于深度分页
    private String lastId;
}
