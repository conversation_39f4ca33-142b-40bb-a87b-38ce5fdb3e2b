package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：flx
 * @Date：2025/4/11 09:14
 * @Description：DevicePositionSaveReq
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DevicePositionSaveReq {

    /**
     * 主键
     */
    private Long id;

    /**
     * 门店code
     */
    private String hotelCode;

    /**
     * 设备类型
     */
    private String deviceAppType;

    /**
     * 楼栋
     */
    private String block;

    /**
     * 楼层
     */
//    private Integer floor;
    /**
     * 区域
     */
    private String area;

    /**
     * 位置名称
     */
    private String positionName;
}
