package com.wormhole.hotelds.api.hotel.web.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HdsDevicePositionQO implements Serializable {
    private String positionCode;
    private List<String> positionCodes;
    private String hotelCode;
    private String positionName;
    private String  deviceAppType;
    private String block;
    private String fullPositionName;
    private String area;
}
