package com.wormhole.hotelds.api.hotel.web.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2025/4/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HdsDeviceQO implements Serializable {
    private Collection<String> userIds;
    private Collection<String> positionCodes;
    private String userId;
    private Integer deviceStatus;
    private Collection<String> deviceAppType;
    private String hotelCode;
    private String deviceId;
    private String rtcUserId;
    private Collection<String> rtcUserIds;
}
