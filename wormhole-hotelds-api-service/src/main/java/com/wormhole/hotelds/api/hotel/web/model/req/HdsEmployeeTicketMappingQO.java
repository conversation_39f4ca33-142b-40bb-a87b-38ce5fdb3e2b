package com.wormhole.hotelds.api.hotel.web.model.req;

import com.wormhole.hotelds.core.enums.AccountTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2025/4/16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HdsEmployeeTicketMappingQO implements Serializable {
    private String hotelCode;
    private String serviceCategory;
    private Integer userType;
    private Integer acceptTicketStatus;
    private AccountTypeEnum accountTypeEnum;
    private String employId;
    private Collection<String> employIdList;
//    private String positionCode;
    private String areaCode;

    private boolean areaPublic = false; // 工单所属区域是否公区
}
