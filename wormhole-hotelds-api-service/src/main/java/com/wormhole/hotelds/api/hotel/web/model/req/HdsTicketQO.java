package com.wormhole.hotelds.api.hotel.web.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.*;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HdsTicketQO implements Serializable {
    private String hotelCode;
    private List<String> hotelCodes;
    private List<String> positionCodes;
    private String rtcRoomId;
    private Integer ticketStatus;
    private Integer expiredFlag;
    private Collection<String> ticketNos;
    private Collection<String> ticketIds;
    private Boolean hasEndOfCall;
    private Boolean hasFeedback;
    private String serviceCategory;
    private String serviceType;
    private List<String> serviceCategories;
    private String serviceSubcategory;
    private String positionCode;

    private String returnRoomId;
    /**
     * 是否有新增sos未完成订单
     */
    private boolean sosRemindFlag;

    private Integer closedLoopLevel;

    private LocalDateTime start;
    private LocalDateTime end;

    private Boolean isCreatedTicket;

    private Integer userType;
}
