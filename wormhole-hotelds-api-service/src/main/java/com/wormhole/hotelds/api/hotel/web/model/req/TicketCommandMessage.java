package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TicketCommandMessage implements Serializable {
    private Boolean refresh = false; //是否刷新
    private Boolean hasSound = false; //是否有声音

    // 是否有新增的sos未完成订单
    @Builder.Default
    private Boolean hasSos = false;
    // 刷新频率
    private Integer refreshIntervalSeconds = 5;

    /**
     * 触发本次刷新的工单摘要列表
     */
    private List<TicketSummary> tickets;

    private Integer employeeType;

    /**
     * 工单摘要信息 DTO
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class TicketSummary implements Serializable {
        private String ticketId;
        private String positionCode;
        private String positionName;
        private String guestRequest;
        private Boolean isCreatedTicket;

        private Integer userType;

        /**
         * 播放次数
         */
        private Integer playCount;
        /**
         * 新工单的多语言语音播报URL
         * Key: 语言代码 (zh, en, id)
         * Value: 音频URL
         */
        private Map<String, String> audioUrls;
    }
}
