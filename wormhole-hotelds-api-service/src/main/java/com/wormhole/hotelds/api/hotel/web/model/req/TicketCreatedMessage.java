package com.wormhole.hotelds.api.hotel.web.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TicketCreatedMessage implements Serializable {

    private TicketCommandMessage ticketCommandMessage;

    private Collection<String> userIds;

}
