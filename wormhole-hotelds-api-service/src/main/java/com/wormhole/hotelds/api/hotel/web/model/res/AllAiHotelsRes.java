package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * WORMHOLE-查询所有AI酒店响应
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AllAiHotelsRes implements Serializable {
    

    /**
     * 酒店映射关系
     */
    private List<HotelMapping> mappings;

    /**
     * BDW酒店编码列表
     */
    private List<String> bdwHotelList;

    /**
     * 虫洞系统酒店编码列表
     */
    private List<String> hotelList;

    
    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HotelMapping implements Serializable {
        /**
         * BDW酒店编码
         */
        private String bdwHotelCode;
        
        /**
         * 虫洞系统酒店编码
         */
        private String hotelCode;
    }
}
