package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * WORMHOLE-批量房间映射响应
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchRoomMappingRes implements Serializable {

    /**
     * 虫洞系统酒店编码
     */
    private String hotelCode;

    /**
     * 第三方酒店编码
     */
    private String thirdHotelCode;

    /**
     * 位置列表
     */
    private List<PositionMappingResult> positionList;

    
    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PositionMappingResult implements Serializable {
        /**
         * 第三方房间号
         */
        private String thirdRoomNo;
        
        /**
         * 位置编码
         */
        private String positionCode;
        
        /**
         * 位置名称
         */
        private String positionName;
    }
}
