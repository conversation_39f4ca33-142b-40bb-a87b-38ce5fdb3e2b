package com.wormhole.hotelds.api.hotel.web.model.res;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CallLogWithTicketsRes implements Serializable {

    private String id;

    private String conversationId;

    private String positionCode;

    private String content;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    private String relatedTicketNo;

    private String hotelCode;

    private String messageId;

    private String messageType;

    private Integer feedbackStatus;

    private String feedbackTypes;

    private String feedbackContent;

    private List<ServiceTicketRes> serviceTickets;


}
