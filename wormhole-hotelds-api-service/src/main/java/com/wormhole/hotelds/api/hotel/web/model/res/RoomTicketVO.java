package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RoomTicketVO implements Serializable {
    private PositionInfoVO positionInfoVO;
    private String roomDeceiveId;
    private String deviceUnavailabilityReason;
    private Boolean showNewNotificationTag = false;
    private List<TicketSimpleVO> notCompleteTickets;
    private List<TicketSimpleVO> completeTickets;

    private String timeZone;


    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class TicketSimpleVO implements Serializable {
        private String ticketId;

        /**
         * 用户诉求
         * 示例值: "帮我送一瓶水哦"
         */
        private String guestRequest;

        /**
         * 通话结束时间(客户端根据此计算剩余分钟)
         * 示例值: "xxx"
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime callEndTime;

        private Long callEndTimeStamp;

        /**
         * 工单创建时间
         * 示例值: "xxx"
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime ticketCreateTime;

        private Long ticketCreateTimeStamp;


        /**
         * 显示类别文案
         * 示例值: "诉"
         */
        private String displayCategoryText;

        private String displayCategoryTextColor;

        private String displayCategoryUrl;

        /**
         * 未完成状态的处理逻辑 (0-直接点击处理 1-回电)
         * 示例值: 0
         */
        private  Integer incompleteProcessType;

        /**
         * 服务类别
         * 示例值: "DELIVERY"
         */
        private String serviceCategory;

        private String serviceType;

        /**
         * 电话号码
         * 示例值: "xx"
         */
        private String mobile;

        private String areaCode;


        /**
         * 用户ID
         * 示例值: "USER123"
         */
        private String userId;

        /**
         * 会话ID
         * 示例值: "CONV789"
         */
        private String conversationId;

        /**
         * 显示状态(0-待处理 1-已完成 2-紧急、投诉)，影响颜色
         * 示例值: 0
         */
        private Integer displayStatus;

        /**
         * 状态(0-待处理 1-已完成)
         * 示例值: 0
         */
        private Integer status;

        /**
         * 是否小程序(0-不是 1-是)
         * 示例值: 0
         */
        private Boolean isMiniProgram;


        private Integer clientType;

        private Boolean allowHandle;

        private Integer replayExceptionType;

        private String replayExceptionText;

        private Boolean offlineDialing;

        private String deviceType;

        /**
         * 标注标记（0=无标注，1=已协助处理，2=需优先）
         */
        private Integer tagFlag;

    }

}
