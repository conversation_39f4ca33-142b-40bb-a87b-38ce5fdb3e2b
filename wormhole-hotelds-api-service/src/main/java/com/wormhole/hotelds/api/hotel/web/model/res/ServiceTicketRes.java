package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ServiceTicketRes implements Serializable {

    /**
     * 工单号
     */
    private String ticketNo;

    /**
     * 状态（0-待处理，1-已完成）
     */
    private String status;

    private String type;

    /**
     * 内容
     */
    private String content;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 客户诉求内容
     */
    private String guestRequest;

    /**
     * 酒店代码
     */
    private String hotelCode;

    /**
     * 位置编码
     */
    private String positionCode;

    /**
     * 完成人员工号
     */
    private String completedBy;

    /**
     * 完成人员姓名
     */
    private String completedByName;

    /**
     * 标注标记（0=无标注，1=已协助处理，2=需优先标记）
     */
    private Integer tagFlag;

    private String phone;
}
