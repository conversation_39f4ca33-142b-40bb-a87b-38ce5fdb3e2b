package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/18
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ServiceTicketSimpleVO implements Serializable {
    private String statusDesc;
    private String priorityDesc;
    private String serviceCategoryName;
    private String serviceSubcategoryName;
    private String guestRequest;
    private LocalDateTime createdAt;
}
