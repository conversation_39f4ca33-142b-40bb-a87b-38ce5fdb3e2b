package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 主题响应对象
 * 包含主题资源、主题信息和主题特性
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ThemeResp implements Serializable {
    private ThemeResources resources;
    private ThemeInfo theme;
    private ThemeFeatures features;

    @Data
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ThemeResources implements Serializable {
        private String defaultLogo;
        private String defaultBanner;
        private String hotelLogo;
    }

    @Data
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ThemeInfo implements Serializable {
        // 预留字段
    }

    @Data
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ThemeFeatures implements Serializable {
        // 预留字段
    }
}