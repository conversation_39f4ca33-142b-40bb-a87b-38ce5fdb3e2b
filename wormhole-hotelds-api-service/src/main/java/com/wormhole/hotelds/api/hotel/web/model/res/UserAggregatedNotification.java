package com.wormhole.hotelds.api.hotel.web.model.res;

import com.wormhole.hotelds.api.hotel.web.model.req.TicketCommandMessage;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class UserAggregatedNotification {

    private boolean hasSound = false;
    private Integer employeeType;
    private List<TicketCommandMessage.TicketSummary> tickets = new ArrayList<>();

    public void addNotification(UserNotificationInfo info) {
        // 只要有一个通知需要声音，最终就需要声音
        this.hasSound = this.hasSound || info.isHasSound();
        this.employeeType = info.getEmployeeType();
        // 添加工单摘要到列表
        this.tickets.add(info.toSummary());
    }


}
