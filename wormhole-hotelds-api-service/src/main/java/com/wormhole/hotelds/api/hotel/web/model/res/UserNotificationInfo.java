package com.wormhole.hotelds.api.hotel.web.model.res;

import cn.hutool.core.util.ObjectUtil;
import com.wormhole.hotelds.api.hotel.constant.SystemConstant;
import com.wormhole.hotelds.api.hotel.enums.EmployeeTypeEnum;
import com.wormhole.hotelds.api.hotel.util.TicketUtils;
import com.wormhole.hotelds.api.hotel.web.model.req.TicketCommandMessage;
import com.wormhole.hotelds.core.enums.ServiceSubcategory;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketEntity;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/30
 */
@Data
public class UserNotificationInfo {
    private List<String> userIds;
    private boolean hasSound = false;
    private Boolean isCreatedTicket;

    private String ticketId;

    private String positionCode;
    private String positionName;

    private String guestRequest;

    private Integer userType;

    /**
     * 播放次数
     */
    private Integer playCount;

    private Integer employeeType;

    private Map<String, String> audioUrls;

    public static UserNotificationInfo build(boolean defaultSoundEnabled, HdsServiceTicketEntity entity,
                                             List<String> userIds, Boolean isCreatedTicket, String positionName, Map<String, String> urls, Integer employeeType) {
        UserNotificationInfo userNotificationInfo = new UserNotificationInfo();
        userNotificationInfo.setUserIds(userIds);
        userNotificationInfo.setHasSound(defaultSoundEnabled && !TicketUtils.autoComplete(entity));
        userNotificationInfo.setIsCreatedTicket(isCreatedTicket);
        userNotificationInfo.setTicketId(String.valueOf(entity.getId()));
        userNotificationInfo.setPositionCode(entity.getPositionCode());
        userNotificationInfo.setPositionName(positionName);
        userNotificationInfo.setAudioUrls(urls);
        userNotificationInfo.setGuestRequest(entity.getGuestRequest());
        userNotificationInfo.setEmployeeType(employeeType);
        Integer playCount = null;
        if (ObjectUtil.equal(employeeType, EmployeeTypeEnum.MAIN_LINE.getCode())) {
            playCount = 1;
        } else if (ObjectUtil.equal(employeeType, EmployeeTypeEnum.EXTENSION.getCode())) {
            // 分机播报次数
            if (ObjectUtil.equal(entity.getServiceSubcategory(), ServiceSubcategory.CL_OCCUPIED.getCode())){
                playCount = 0;
            } else {
                playCount = 3;
            }
        }
        userNotificationInfo.setPlayCount(playCount);
        userNotificationInfo.setUserType(entity.getUserType());
        return userNotificationInfo;
    }

    /**
     * 将通知信息转换为工单摘要 DTO
     * @return TicketSummary
     */
    public TicketCommandMessage.TicketSummary toSummary() {
        return TicketCommandMessage.TicketSummary.builder()
                .ticketId(this.ticketId)
                .positionCode(this.positionCode)
                .positionName(this.positionName)
                .guestRequest(this.guestRequest)
                .isCreatedTicket(this.isCreatedTicket)
                .userType(this.userType)
                .audioUrls(this.audioUrls)
                .playCount(this.playCount)
                .build();
    }

}
