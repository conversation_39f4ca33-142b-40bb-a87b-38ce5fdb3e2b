package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/9 15:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WechatInitRes implements Serializable {

    /**
     * rtc token
     */
    private String rtcToken;

    /**
     * rtc 厂商
     */
    private String rtcManufacturer;

    /**
     * 酒店code
     */
    private String hotelCode;

    private String hotelName;

    private Integer sosSwitch;

    private String callCommand;

    private Integer dialogSwitch;


    /**
     * 设备code
     */
    private String deviceId;

    /**
     * 房间唯一编码
     */
    private String positionCode;

    private String positionName;

    /**
     * 设备类型 （前台设备 & 客房设备）
     * @see com.wormhole.hotelds.core.enums.DeviceTypeEnum
     */
    private String deviceType;

    /**
     * 语音房id
     */
    private String rtcRoomId;

    /**
     * rtc用户id
     */
    private String rtcUserId;

    /**
     * 服务端回调地址
     */
    private String serverCallBackUrl;

    /**
     * 加密
     */
    private String secret;

    /**
     * 超时时间
     */
    private Integer callTimeOutSeconds;

    //botcode
    private String botCode;

    private String rtcAppId;
}
