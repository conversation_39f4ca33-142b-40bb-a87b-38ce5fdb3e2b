package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.Result;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.config.WormholeConfiguration;
import com.wormhole.hotelds.api.hotel.constant.MappingTypeEnum;
import com.wormhole.hotelds.api.hotel.constant.YesNoEnum;
import com.wormhole.hotelds.api.hotel.util.TicketUtils;
import com.wormhole.hotelds.api.hotel.util.WebClientUtils;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDevicePositionDao;
import com.wormhole.hotelds.api.hotel.web.dao.OrderRoomRedisDao;
import com.wormhole.hotelds.api.hotel.constant.JumpTypeEnum;
import com.wormhole.hotelds.api.hotel.resp.MatchWormholeRoomsResp;
import com.wormhole.hotelds.api.hotel.resp.QueryAiHotelResp;
import com.wormhole.hotelds.api.hotel.web.model.req.BatchCreatePositionTicketReq;
import com.wormhole.hotelds.api.hotel.web.model.req.BatchRoomMappingReq;
import com.wormhole.hotelds.api.hotel.web.model.req.BatchRoomMappingWithTypeReq;
import com.wormhole.hotelds.api.hotel.web.model.req.CreateTicketReq;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsDevicePositionQO;
import com.wormhole.hotelds.api.hotel.req.MatchWormholeRoomsReq;
import com.wormhole.hotelds.api.hotel.req.QueryAiHotelReq;
import com.wormhole.hotelds.api.hotel.web.model.res.AllAiHotelsRes;
import com.wormhole.hotelds.api.hotel.web.model.res.BatchRoomMappingRes;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;
import com.wormhole.hotelds.core.enums.ExternalChannelEnum;
import com.wormhole.hotelds.core.model.entity.HdsHotelMappingEntity;
import com.wormhole.hotelds.plugin.dao.HdsHotelMappingDao;
import com.wormhole.hotelds.plugin.model.qo.HdsHotelMappingQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/12
 */

@Service
@Slf4j
public class BdwAiCustomerService {

    @Autowired
    private OrderRoomRedisDao orderRoomRedisDao;
    @Autowired
    private HdsDevicePositionDao hdsDevicePositionDao;
    @Autowired
    private HdsHotelMappingDao hdsHotelMappingDao;
    @Autowired
    private WormholeConfiguration wormholeConfiguration;

    @Autowired
    private ServiceTicketService serviceTicketService;

    @Resource
    private WebClientUtils webClientUtils;

    private static final String BDW_CHANNEL = "Bdw";
    private static final String PUBLIC_AREA_NAME = "其他公区";

    public Mono<MatchWormholeRoomsResp> matchWormholeRooms(MatchWormholeRoomsReq matchWormholeRoomsReq) {
        log.info("matchWormholeRooms request: {}", JacksonUtils.writeValueAsString(matchWormholeRoomsReq));

        String bdwHotelCode = matchWormholeRoomsReq.getBdwHotelCode();
        Preconditions.checkArgument(StringUtils.isNotBlank(bdwHotelCode), "bdwHotelCode不能为空");
        String childOrderNo = matchWormholeRoomsReq.getChildOrderNo();
        Preconditions.checkArgument(StringUtils.isNotBlank(childOrderNo), "childOrderNo不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(matchWormholeRoomsReq.getRoomNo()), "roomNo不能为空");

        return orderRoomRedisDao.get(bdwHotelCode, childOrderNo)
                .flatMap(hotelPositionSimpleDto -> {
                    String hotelCode = hotelPositionSimpleDto.getHotelCode();
                    String positionCode = hotelPositionSimpleDto.getPositionCode();
                    return Mono.just(MatchWormholeRoomsResp.of(JumpTypeEnum.H5_SESSION.getCode(),
                            hotelCode, positionCode,
                            getJumpUrl(JumpTypeEnum.H5_SESSION.getCode(), hotelCode, positionCode, bdwHotelCode, childOrderNo)));
                }).doOnNext(matchWormholeRoomsResp -> log.info("matchWormholeRooms response: {}", JacksonUtils.writeValueAsString(matchWormholeRoomsResp)))
                .switchIfEmpty(
                        Mono.defer(() -> hdsHotelMappingDao.findOne(getHotelMappingBuild(matchWormholeRoomsReq))
                                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "没有找到对应的BDW酒店")))
                                .flatMap(hdsHotelMappingEntity ->
                                        hdsDevicePositionDao.findOne(getPositionBuild(matchWormholeRoomsReq.getRoomNo(), hdsHotelMappingEntity.getHotelCode()))
                                                .map(hdsDevicePositionEntity ->
                                                        MatchWormholeRoomsResp.of(JumpTypeEnum.CONFIRM_DIALOG.getCode(),
                                                                hdsDevicePositionEntity.getHotelCode(),
                                                                hdsDevicePositionEntity.getPositionCode(),
                                                                getJumpUrl(JumpTypeEnum.CONFIRM_DIALOG.getCode(),
                                                                        hdsDevicePositionEntity.getHotelCode(),
                                                                        hdsDevicePositionEntity.getPositionCode(), bdwHotelCode, childOrderNo))
                                                )
                                                .switchIfEmpty(Mono.just(
                                                        MatchWormholeRoomsResp.of(JumpTypeEnum.SCAN_CODE.getCode(), null, null, null)
                                                )))
                        ).doOnNext(matchWormholeRoomsResp -> log.info("matchWormholeRooms response: {}", JacksonUtils.writeValueAsString(matchWormholeRoomsResp)))
                );

    }

    public String getJumpUrl(Integer jumpType, String hotelCode, String positionCode, String bdwHotelCode, String childOrderNo) {
        if (Objects.equals(JumpTypeEnum.H5_SESSION.getCode(), jumpType) || Objects.equals(JumpTypeEnum.CONFIRM_DIALOG.getCode(), jumpType)) {
            return String.format(wormholeConfiguration.getBdwAiChatH5Url(), hotelCode, positionCode, bdwHotelCode, childOrderNo);
        }
        return null;
    }


    public Mono<QueryAiHotelResp> queryAiHotels(QueryAiHotelReq queryAiHotelReq) {
        List<String> bdwHotelCodes = queryAiHotelReq.getBdwHotelCodes();
        Preconditions.checkArgument(CollUtil.isNotEmpty(bdwHotelCodes), " bdwHotelCodes不能为空");
        HdsHotelMappingQO hotelMappingBuild = getHotelMappingBuild(queryAiHotelReq);
        return hdsHotelMappingDao.findList(hotelMappingBuild).map(mappingEntities -> QueryAiHotelResp.of(getAiHotelStatusDtos(mappingEntities, bdwHotelCodes)))
                .switchIfEmpty(Mono.defer(() -> {
                    List<QueryAiHotelResp.AiHotelStatusDto> collect = bdwHotelCodes.stream().map(e -> QueryAiHotelResp.AiHotelStatusDto.of(e, false)).collect(Collectors.toList());
                    return Mono.just(QueryAiHotelResp.of(collect));
                }));

    }

    @NotNull
    private static List<QueryAiHotelResp.AiHotelStatusDto> getAiHotelStatusDtos(List<HdsHotelMappingEntity> mappingEntities, List<String> bdwHotelCodes) {
        Set<String> hasAiExternalIds = mappingEntities.stream()
                .map(HdsHotelMappingEntity::getExternalId)
                .collect(Collectors.toSet());
        List<QueryAiHotelResp.AiHotelStatusDto> aiHotelStatusList = bdwHotelCodes.stream()
                .map(bdwHotelCode -> QueryAiHotelResp.AiHotelStatusDto.of(
                        bdwHotelCode,
                        hasAiExternalIds.contains(bdwHotelCode)
                ))
                .collect(Collectors.toList());
        return aiHotelStatusList;
    }

    @NotNull
    private static HdsDevicePositionQO getPositionBuild(String roomNo, String hotelCode) {
        HdsDevicePositionQO hdsDevicePositionQO = new HdsDevicePositionQO();
        hdsDevicePositionQO.setFullPositionName(roomNo);
        hdsDevicePositionQO.setHotelCode(hotelCode);
        hdsDevicePositionQO.setDeviceAppType(DeviceTypeEnum.ROOM.getCode());
        return hdsDevicePositionQO;
    }

    private static HdsHotelMappingQO getHotelMappingBuild(MatchWormholeRoomsReq matchWormholeRoomsReq) {
        return HdsHotelMappingQO.builder()
                .externalId(matchWormholeRoomsReq.getBdwHotelCode())
                .channel(ExternalChannelEnum.BDW.getCode())
                .platform(ExternalChannelEnum.BDW.getCode())
                .build();
    }

    private static HdsHotelMappingQO getHotelMappingBuild(QueryAiHotelReq queryAiHotelReq) {
        return HdsHotelMappingQO.builder()
                .externalIds(queryAiHotelReq.getBdwHotelCodes())
                .channel(ExternalChannelEnum.BDW.getCode())
                .platform(ExternalChannelEnum.BDW.getCode())
                .build();
    }

    /**
     * 批量房间映射
     */
    public Mono<List<BatchRoomMappingRes>> batchRoomMapping(BatchRoomMappingWithTypeReq req) {
        Preconditions.checkArgument(req.getMappingType() != null, "映射类型不能为空");
        Preconditions.checkArgument(CollUtil.isNotEmpty(req.getData()), "数据不能为空");

        if (ObjectUtil.equal(req.getMappingType(), MappingTypeEnum.BDW_TO_WORMHOLE.getCode())) {
            // BDW到虫洞的映射
            return bdwToWormholeMapping(req.getData());
        } else if (ObjectUtil.equal(req.getMappingType(), MappingTypeEnum.WORMHOLE_TO_BDW.getCode())) {
            // 虫洞到BDW的映射（反向查询）
            return wormholeToBdwMapping(req.getData());
        }

        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "不支持的映射类型"));
    }

    /**
     * BDW到虫洞的映射 - 批量查询优化版本
     */
    private Mono<List<BatchRoomMappingRes>> bdwToWormholeMapping(List<BatchRoomMappingReq> data) {
        // 提取所有需要查询的third_hotel_code
        List<String> thirdHotelCodes = data.stream()
                .map(BatchRoomMappingReq::getThirdHotelCode)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询酒店映射关系
        return batchValidateHotelMappings(thirdHotelCodes)
                .flatMap(hotelMappings -> {
                    // 创建映射关系Map
                    Map<String, String> thirdToWormholeMap = hotelMappings.stream()
                            .collect(Collectors.toMap(
                                    HdsHotelMappingEntity::getExternalId,
                                    HdsHotelMappingEntity::getHotelCode
                            ));

                    // 处理每个酒店的数据
                    return Flux.fromIterable(data)
                            .flatMap(hotelData -> processHotelMappingBatch(hotelData, thirdToWormholeMap))
                            .collectList();
                });
    }

    /**
     * 虫洞到BDW的映射（反向查询）- 批量查询优化版本
     */
    private Mono<List<BatchRoomMappingRes>> wormholeToBdwMapping(List<BatchRoomMappingReq> data) {
        // 提取所有需要查询的hotel_code
        List<String> hotelCodes = data.stream()
                .map(BatchRoomMappingReq::getHotelCode)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询酒店映射关系（反向）
        return batchValidateReverseHotelMappings(hotelCodes)
                .flatMap(hotelMappings -> {
                    // 创建映射关系Map
                    Map<String, String> wormholeToBdwMap = hotelMappings.stream()
                            .collect(Collectors.toMap(
                                    HdsHotelMappingEntity::getHotelCode,
                                    HdsHotelMappingEntity::getExternalId
                            ));

                    // 处理每个酒店的数据
                    return Flux.fromIterable(data)
                            .flatMap(hotelData -> processReverseHotelMappingBatch(hotelData, wormholeToBdwMap))
                            .collectList();
                });
    }

    /**
     * 批量验证酒店映射关系（BDW到虫洞）
     */
    private Mono<List<HdsHotelMappingEntity>> batchValidateHotelMappings(List<String> thirdHotelCodes) {
        if (CollUtil.isEmpty(thirdHotelCodes)) {
            return Mono.just(new ArrayList<>());
        }

        HdsHotelMappingQO qo = HdsHotelMappingQO.builder()
                .externalIds(new HashSet<>(thirdHotelCodes))
                .channel(ExternalChannelEnum.BDW.getCode())
                .build();

        return hdsHotelMappingDao.findList(qo);
    }

    /**
     * 批量验证酒店映射关系（虫洞到BDW）
     */
    private Mono<List<HdsHotelMappingEntity>> batchValidateReverseHotelMappings(List<String> hotelCodes) {
        if (CollUtil.isEmpty(hotelCodes)) {
            return Mono.just(new ArrayList<>());
        }

        HdsHotelMappingQO qo = HdsHotelMappingQO.builder()
                .hotelCodeList(new HashSet<>(hotelCodes))
                .channel(ExternalChannelEnum.BDW.getCode())
                .build();

        return hdsHotelMappingDao.findList(qo);
    }

    /**
     * 处理单个酒店的房间映射（BDW到虫洞）- 批量版本
     */
    private Mono<BatchRoomMappingRes> processHotelMappingBatch(
            BatchRoomMappingReq hotelData, Map<String, String> thirdToWormholeMap) {

        String validHotelCode = thirdToWormholeMap.get(hotelData.getThirdHotelCode());

        if (StringUtils.isBlank(validHotelCode)) {
            // 没查询到映射关系，返回除了third_hotel_code都为空的结果
            return Mono.just(BatchRoomMappingRes.builder()
                    .hotelCode("")
                    .thirdHotelCode(hotelData.getThirdHotelCode())
                    .positionList(new ArrayList<>())
                    .build());
        }

        // 处理位置映射
        return processPositionMappings(validHotelCode, hotelData.getPositionList())
                .map(positionResults -> BatchRoomMappingRes.builder()
                        .hotelCode(validHotelCode)
                        .thirdHotelCode(hotelData.getThirdHotelCode())
                        .positionList(positionResults)
                        .build());
    }

    /**
     * 处理单个酒店的房间映射（虫洞到BDW）- 批量版本
     */
    private Mono<BatchRoomMappingRes> processReverseHotelMappingBatch(
            BatchRoomMappingReq hotelData, Map<String, String> wormholeToBdwMap) {

        String thirdHotelCode = wormholeToBdwMap.get(hotelData.getHotelCode());

        if (StringUtils.isBlank(thirdHotelCode)) {
            // 没查询到映射关系，返回除了hotel_code都为空的结果
            return Mono.just(BatchRoomMappingRes.builder()
                    .hotelCode(hotelData.getHotelCode())
                    .thirdHotelCode("")
                    .positionList(new ArrayList<>())
                    .build());
        }

        // 处理位置映射（反向）
        return processReversePositionMappings(hotelData.getHotelCode(), hotelData.getPositionList())
                .map(positionResults -> BatchRoomMappingRes.builder()
                        .hotelCode(hotelData.getHotelCode())
                        .thirdHotelCode(thirdHotelCode)
                        .positionList(positionResults)
                        .build());
    }


    /**
     * 处理位置映射列表（BDW到虫洞）- 批量查询优化版本
     */
    private Mono<List<BatchRoomMappingRes.PositionMappingResult>> processPositionMappings(
            String hotelCode, List<BatchRoomMappingReq.PositionMappingData> positionList) {

        if (CollUtil.isEmpty(positionList)) {
            return Mono.just(new ArrayList<>());
        }

        // 提取所有需要查询的房间号
        List<String> thirdRoomNos = positionList.stream()
                .map(BatchRoomMappingReq.PositionMappingData::getThirdRoomNo)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询已存在的位置
        return batchQueryPositionsByNames(hotelCode, thirdRoomNos)
                .flatMap(existingPositions -> {
                    // 找出未匹配的房间号
                    Set<String> matchedRoomNos = existingPositions.stream()
                            .map(HdsDevicePositionEntity::getPositionName)
                            .collect(Collectors.toSet());

                    Set<String> unmatchedRoomNos = Sets.difference(Sets.newHashSet(thirdRoomNos),matchedRoomNos);

                    // 获取或创建公区
                    return findOrCreatePublicArea(hotelCode)
                            .map(publicArea -> {
                                List<BatchRoomMappingRes.PositionMappingResult> results = new ArrayList<>();

                                // 处理匹配到的位置
                                for (HdsDevicePositionEntity position : existingPositions) {
                                    results.add(BatchRoomMappingRes.PositionMappingResult.builder()
                                            .thirdRoomNo(position.getPositionName())
                                            .positionCode(position.getPositionCode())
                                            .positionName(position.getPositionName())
                                            .build());
                                }

                                // 处理未匹配的位置，返回公区信息
                                for (String unmatchedRoomNo : unmatchedRoomNos) {
                                    results.add(BatchRoomMappingRes.PositionMappingResult.builder()
                                            .thirdRoomNo(unmatchedRoomNo)
                                            .positionCode(publicArea.getPositionCode())
                                            .positionName(publicArea.getPositionName())
                                            .build());
                                }

                                return results;
                            });
                });
    }

    /**
     * 批量查询位置信息根据位置名称
     */
    private Mono<List<HdsDevicePositionEntity>> batchQueryPositionsByNames(String hotelCode, List<String> positionNames) {
        if (CollUtil.isEmpty(positionNames)) {
            return Mono.just(new ArrayList<>());
        }

        // 使用新的高效批量查询方法
        return hdsDevicePositionDao.findByHotelCodeAndPositionNames(hotelCode, positionNames);
    }

    /**
     * 处理位置映射列表（虫洞到BDW，反向查询）
     */
    private Mono<List<BatchRoomMappingRes.PositionMappingResult>> processReversePositionMappings(
            String hotelCode, List<BatchRoomMappingReq.PositionMappingData> positionList) {

        if (CollUtil.isEmpty(positionList)) {
            return Mono.just(new ArrayList<>());
        }

        return Flux.fromIterable(positionList)
                .flatMap(position -> processReversePositionMapping(hotelCode, position))
                .collectList();
    }


    /**
     * 处理单个位置映射（虫洞到BDW，反向查询）
     */
    private Mono<BatchRoomMappingRes.PositionMappingResult> processReversePositionMapping(
            String hotelCode, BatchRoomMappingReq.PositionMappingData position) {

        // 根据position_code查找position_name
        if (StringUtils.isNotBlank(position.getPositionCode())) {
            HdsDevicePositionQO qo = new HdsDevicePositionQO();
            qo.setHotelCode(hotelCode);
            qo.setPositionCode(position.getPositionCode());

            return hdsDevicePositionDao.findOne(qo)
                    .flatMap(devicePosition -> {
                        String thirdRoomNo;

                        // 如果positionName不为空且不是"公区"，则thirdRoomNo = positionName
                        if (StringUtils.isNotBlank(devicePosition.getPositionName())
                                && !PUBLIC_AREA_NAME.equals(devicePosition.getPositionName())) {
                            thirdRoomNo = devicePosition.getPositionName();
                        } else {
                            // 否则从guestRequest里解析thirdRoomNo
                            thirdRoomNo = TicketUtils.parseThirdRoomNoFromGuestRequest(position.getGuestRequest());
                        }

                        return Mono.just(BatchRoomMappingRes.PositionMappingResult.builder()
                                .thirdRoomNo(thirdRoomNo)
                                .positionCode(devicePosition.getPositionCode())
                                .positionName(devicePosition.getPositionName())
                                .build());
                    })
                    .switchIfEmpty(Mono.just(BatchRoomMappingRes.PositionMappingResult.builder()
                            .thirdRoomNo("")
                            .positionCode(position.getPositionCode())
                            .positionName("")
                            .build()));
        }

        return Mono.just(BatchRoomMappingRes.PositionMappingResult.builder()
                .thirdRoomNo("")
                .positionCode("")
                .positionName("")
                .build());
    }

    /**
     * 处理未匹配的位置
     */
    private Mono<BatchRoomMappingRes.PositionMappingResult> handleUnmatchedPosition(
            String hotelCode, BatchRoomMappingReq.PositionMappingData position) {

        // 查找公区位置，如果不存在则创建
        return findOrCreatePublicArea(hotelCode)
                .map(publicArea -> BatchRoomMappingRes.PositionMappingResult.builder()
                        .thirdRoomNo(position.getThirdRoomNo())
                        .positionCode(publicArea.getPositionCode())
                        .positionName(publicArea.getPositionName())
                        .build());
    }

    /**
     * 查找或创建公区（返回实体）
     */
    private Mono<HdsDevicePositionEntity> findOrCreatePublicArea(String hotelCode) {
        HdsDevicePositionQO qo = new HdsDevicePositionQO();
        qo.setHotelCode(hotelCode);
        qo.setFullPositionName(PUBLIC_AREA_NAME);

        return hdsDevicePositionDao.findOne(qo)
                .switchIfEmpty(createPublicArea(hotelCode));
    }

    /**
     * 创建公区
     */
    private Mono<HdsDevicePositionEntity> createPublicArea(String hotelCode) {
        String url = String.format("%s/device_positions/create", "http://wormhole-hotelds.delonix.dev");
        HdsDevicePositionEntity req = new HdsDevicePositionEntity();
        req.setHotelCode(hotelCode);
        req.setDeviceAppType(DeviceTypeEnum.FRONT.getCode());
        req.setBlock("主楼");
        req.setArea(PUBLIC_AREA_NAME);
        req.setPositionName(PUBLIC_AREA_NAME);
        return webClientUtils.sendPost(url, null, req, null, new TypeReference<Object>() {})
                .flatMap(ele-> hdsDevicePositionDao.findOne(HdsDevicePositionQO.builder().hotelCode(hotelCode).fullPositionName(PUBLIC_AREA_NAME).build()));
    }

    /**
     * 查询所有AI酒店
     */
    public Mono<AllAiHotelsRes> getAllAiHotels() {
        HdsHotelMappingQO qo = HdsHotelMappingQO.builder()
                .channel(ExternalChannelEnum.BDW.getCode())
                .build();

        return hdsHotelMappingDao.findList(qo)
                .map(mappings -> {
                    // 过滤external_id不为空的记录
                    List<HdsHotelMappingEntity> validMappings = mappings.stream()
                            .filter(mapping -> StringUtils.isNotBlank(mapping.getExternalId()))
                            .toList();

                    List<AllAiHotelsRes.HotelMapping> hotelMappings = validMappings.stream()
                            .map(mapping -> AllAiHotelsRes.HotelMapping.builder()
                                    .bdwHotelCode(mapping.getExternalId())
                                    .hotelCode(mapping.getHotelCode())
                                    .build())
                            .collect(Collectors.toList());

                    List<String> bdwHotelList = validMappings.stream()
                            .map(HdsHotelMappingEntity::getExternalId)
                            .collect(Collectors.toList());

                    List<String> hotelList = validMappings.stream()
                            .map(HdsHotelMappingEntity::getHotelCode)
                            .collect(Collectors.toList());

                    return AllAiHotelsRes.builder()
                            .mappings(hotelMappings)
                            .bdwHotelList(bdwHotelList)
                            .hotelList(hotelList)
                            .build();
                });
    }

    /**
     * 批量创建不同位置同一需求工单
     */
    public Mono<String> batchCreatePositionTicket(BatchCreatePositionTicketReq req) {
        Preconditions.checkArgument(req.getTicketRequest() != null, "工单请求信息不能为空");
        Preconditions.checkArgument(CollUtil.isNotEmpty(req.getLocationList()), "位置列表不能为空");

        return Flux.fromIterable(req.getLocationList())
                .flatMap(location -> processLocationTickets(req.getTicketRequest(), location))
                .then(Mono.just("批量创建工单成功"));
    }

    /**
     * 处理单个位置的工单创建
     */
    private Mono<Void> processLocationTickets(BatchCreatePositionTicketReq.TicketRequestInfo ticketRequest,
                                              BatchCreatePositionTicketReq.LocationInfo location) {

        if (CollUtil.isEmpty(location.getPositionCodes())) {
            return Mono.empty();
        }

        return Flux.fromIterable(location.getPositionCodes())
                .flatMap(position -> createSingleTicket(ticketRequest, location.getHotelCode(), position))
                .then();
    }

    /**
     * 创建单个工单
     */
    private Mono<String> createSingleTicket(BatchCreatePositionTicketReq.TicketRequestInfo ticketRequest,
                                            String hotelCode, BatchCreatePositionTicketReq.PositionInfo position) {

        return resolvePositionCode(hotelCode, position)
                .flatMap(resolvedPosition -> {
                    CreateTicketReq createReq = buildCreateTicketReq(ticketRequest, hotelCode, resolvedPosition, position);
                    return serviceTicketService.createTicket(createReq);
                });
    }

    /**
     * 解析位置编码，如果为空则使用公区
     */
    private Mono<String> resolvePositionCode(String hotelCode, BatchCreatePositionTicketReq.PositionInfo position) {
        if (StringUtils.isNotBlank(position.getPositionCode())) {
            return Mono.just(position.getPositionCode());
        }

        // 查找或创建公区，返回位置编码
        return findOrCreatePublicArea(hotelCode)
                .map(HdsDevicePositionEntity::getPositionCode);
    }

    /**
     * 构建创建工单请求
     */
    private CreateTicketReq buildCreateTicketReq(BatchCreatePositionTicketReq.TicketRequestInfo ticketRequest,
                                                 String hotelCode, String positionCode,
                                                 BatchCreatePositionTicketReq.PositionInfo position) {

        String guestRequest;

        // 如果position_code为空（使用了公区），使用拼接逻辑添加房间号
        if (StringUtils.isBlank(position.getPositionCode()) && StringUtils.isNotBlank(position.getThirdRoomNo())) {
            guestRequest = TicketUtils.buildGuestRequestWithRoomNo(position.getThirdRoomNo(), ticketRequest.getGuestRequest());
        } else {
            // 房间匹配的情况，使用固定文案
            guestRequest = ticketRequest.getGuestRequest();
        }

        return CreateTicketReq.builder()
                .serviceType(ticketRequest.getServiceType())
                .serviceCategory(ticketRequest.getServiceCategory())
                .serviceSubcategory(ticketRequest.getServiceSubcategory())
                .serviceKeywords(CollUtil.isNotEmpty(ticketRequest.getServiceKeywords()) ?
                        String.join(",", ticketRequest.getServiceKeywords()) : null)
                .closedLoopLevel(ticketRequest.getClosedLoopLevel())
                .replyExceptionType(ticketRequest.getReplyExceptionType())
                .guestRequest(guestRequest)
                .guestRequestEn(ticketRequest.getGuestRequestEn())
                .guestRequestInd(ticketRequest.getGuestRequestIdn())
                .hotelCode(hotelCode)
                .positionCode(positionCode)
                .userType(ticketRequest.getUserType())
                .userId(ticketRequest.getUserId())
                .userName(ticketRequest.getUserName())
                .createType(1) // 手动创建
                .build();
    }


}
