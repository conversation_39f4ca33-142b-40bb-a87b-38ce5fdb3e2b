package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.config.WormholeConfiguration;
import com.wormhole.hotelds.api.hotel.constant.MappingTypeEnum;
import com.wormhole.hotelds.api.hotel.constant.SystemConstant;
import com.wormhole.hotelds.api.hotel.util.TicketUtils;
import com.wormhole.hotelds.api.hotel.util.WebClientUtils;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDevicePositionDao;
import com.wormhole.hotelds.api.hotel.web.dao.OrderRoomRedisDao;
import com.wormhole.hotelds.api.hotel.constant.JumpTypeEnum;
import com.wormhole.hotelds.api.hotel.resp.MatchWormholeRoomsResp;
import com.wormhole.hotelds.api.hotel.resp.QueryAiHotelResp;
import com.wormhole.hotelds.api.hotel.web.model.req.*;
import com.wormhole.hotelds.api.hotel.req.MatchWormholeRoomsReq;
import com.wormhole.hotelds.api.hotel.req.QueryAiHotelReq;
import com.wormhole.hotelds.api.hotel.web.model.res.AllAiHotelsRes;
import com.wormhole.hotelds.api.hotel.web.model.res.BatchCreateTicketResp;
import com.wormhole.hotelds.api.hotel.web.model.res.BatchRoomMappingRes;
import com.wormhole.hotelds.core.enums.CreateType;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;
import com.wormhole.hotelds.core.enums.ExternalChannelEnum;
import com.wormhole.hotelds.core.model.entity.HdsHotelMappingEntity;
import com.wormhole.hotelds.plugin.dao.HdsHotelMappingDao;
import com.wormhole.hotelds.plugin.model.qo.HdsHotelMappingQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.wormhole.hotelds.api.hotel.constant.SystemConstant.OTHER_PUBLIC_POSITION_NAME;
import static com.wormhole.hotelds.api.hotel.constant.SystemConstant.PUBLIC_AREA_NAME;

/**
 * <AUTHOR>
 * @date 2025/6/12
 */

@Service
@Slf4j
public class BdwAiCustomerService {

    @Autowired
    private OrderRoomRedisDao orderRoomRedisDao;
    @Autowired
    private HdsDevicePositionDao hdsDevicePositionDao;
    @Autowired
    private HdsHotelMappingDao hdsHotelMappingDao;
    @Autowired
    private WormholeConfiguration wormholeConfiguration;

    @Autowired
    private ServiceTicketService serviceTicketService;

    @Resource
    private WebClientUtils webClientUtils;


    public Mono<MatchWormholeRoomsResp> matchWormholeRooms(MatchWormholeRoomsReq matchWormholeRoomsReq) {
        log.info("matchWormholeRooms request: {}", JacksonUtils.writeValueAsString(matchWormholeRoomsReq));

        String bdwHotelCode = matchWormholeRoomsReq.getBdwHotelCode();
        Preconditions.checkArgument(StringUtils.isNotBlank(bdwHotelCode), "bdwHotelCode不能为空");
        String childOrderNo = matchWormholeRoomsReq.getChildOrderNo();
        Preconditions.checkArgument(StringUtils.isNotBlank(childOrderNo), "childOrderNo不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(matchWormholeRoomsReq.getRoomNo()), "roomNo不能为空");

        return orderRoomRedisDao.get(bdwHotelCode, childOrderNo)
                .flatMap(hotelPositionSimpleDto -> {
                    String hotelCode = hotelPositionSimpleDto.getHotelCode();
                    String positionCode = hotelPositionSimpleDto.getPositionCode();
                    return Mono.just(MatchWormholeRoomsResp.of(JumpTypeEnum.H5_SESSION.getCode(),
                            hotelCode, positionCode,
                            getJumpUrl(JumpTypeEnum.H5_SESSION.getCode(), hotelCode, positionCode, bdwHotelCode, childOrderNo)));
                }).doOnNext(matchWormholeRoomsResp -> log.info("matchWormholeRooms response: {}", JacksonUtils.writeValueAsString(matchWormholeRoomsResp)))
                .switchIfEmpty(
                        Mono.defer(() -> hdsHotelMappingDao.findOne(getHotelMappingBuild(matchWormholeRoomsReq))
                                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "没有找到对应的BDW酒店")))
                                .flatMap(hdsHotelMappingEntity ->
                                        hdsDevicePositionDao.findOne(getPositionBuild(matchWormholeRoomsReq.getRoomNo(), hdsHotelMappingEntity.getHotelCode()))
                                                .map(hdsDevicePositionEntity ->
                                                        MatchWormholeRoomsResp.of(JumpTypeEnum.CONFIRM_DIALOG.getCode(),
                                                                hdsDevicePositionEntity.getHotelCode(),
                                                                hdsDevicePositionEntity.getPositionCode(),
                                                                getJumpUrl(JumpTypeEnum.CONFIRM_DIALOG.getCode(),
                                                                        hdsDevicePositionEntity.getHotelCode(),
                                                                        hdsDevicePositionEntity.getPositionCode(), bdwHotelCode, childOrderNo))
                                                )
                                                .switchIfEmpty(Mono.just(
                                                        MatchWormholeRoomsResp.of(JumpTypeEnum.SCAN_CODE.getCode(), null, null, null)
                                                )))
                        ).doOnNext(matchWormholeRoomsResp -> log.info("matchWormholeRooms response: {}", JacksonUtils.writeValueAsString(matchWormholeRoomsResp)))
                );

    }

    public String getJumpUrl(Integer jumpType, String hotelCode, String positionCode, String bdwHotelCode, String childOrderNo) {
        if (Objects.equals(JumpTypeEnum.H5_SESSION.getCode(), jumpType) || Objects.equals(JumpTypeEnum.CONFIRM_DIALOG.getCode(), jumpType)) {
            return String.format(wormholeConfiguration.getBdwAiChatH5Url(), hotelCode, positionCode, bdwHotelCode, childOrderNo);
        }
        return null;
    }


    public Mono<QueryAiHotelResp> queryAiHotels(QueryAiHotelReq queryAiHotelReq) {
        List<String> bdwHotelCodes = queryAiHotelReq.getBdwHotelCodes();
        Preconditions.checkArgument(CollUtil.isNotEmpty(bdwHotelCodes), " bdwHotelCodes不能为空");
        HdsHotelMappingQO hotelMappingBuild = getHotelMappingBuild(queryAiHotelReq);
        return hdsHotelMappingDao.findList(hotelMappingBuild).map(mappingEntities -> QueryAiHotelResp.of(getAiHotelStatusDtos(mappingEntities, bdwHotelCodes)))
                .switchIfEmpty(Mono.defer(() -> {
                    List<QueryAiHotelResp.AiHotelStatusDto> collect = bdwHotelCodes.stream().map(e -> QueryAiHotelResp.AiHotelStatusDto.of(e, false)).collect(Collectors.toList());
                    return Mono.just(QueryAiHotelResp.of(collect));
                }));

    }

    @NotNull
    private static List<QueryAiHotelResp.AiHotelStatusDto> getAiHotelStatusDtos(List<HdsHotelMappingEntity> mappingEntities, List<String> bdwHotelCodes) {
        Set<String> hasAiExternalIds = mappingEntities.stream()
                .map(HdsHotelMappingEntity::getExternalId)
                .collect(Collectors.toSet());
        List<QueryAiHotelResp.AiHotelStatusDto> aiHotelStatusList = bdwHotelCodes.stream()
                .map(bdwHotelCode -> QueryAiHotelResp.AiHotelStatusDto.of(
                        bdwHotelCode,
                        hasAiExternalIds.contains(bdwHotelCode)
                ))
                .collect(Collectors.toList());
        return aiHotelStatusList;
    }

    @NotNull
    private static HdsDevicePositionQO getPositionBuild(String roomNo, String hotelCode) {
        HdsDevicePositionQO hdsDevicePositionQO = new HdsDevicePositionQO();
        hdsDevicePositionQO.setFullPositionName(roomNo);
        hdsDevicePositionQO.setHotelCode(hotelCode);
        hdsDevicePositionQO.setDeviceAppType(DeviceTypeEnum.ROOM.getCode());
        return hdsDevicePositionQO;
    }

    private static HdsHotelMappingQO getHotelMappingBuild(MatchWormholeRoomsReq matchWormholeRoomsReq) {
        return HdsHotelMappingQO.builder()
                .externalId(matchWormholeRoomsReq.getBdwHotelCode())
                .channel(ExternalChannelEnum.BDW.getCode())
                .platform(ExternalChannelEnum.BDW.getCode())
                .build();
    }

    private static HdsHotelMappingQO getHotelMappingBuild(QueryAiHotelReq queryAiHotelReq) {
        return HdsHotelMappingQO.builder()
                .externalIds(queryAiHotelReq.getBdwHotelCodes())
                .channel(ExternalChannelEnum.BDW.getCode())
                .platform(ExternalChannelEnum.BDW.getCode())
                .build();
    }

    /**
     * 批量房间映射
     */
    public Mono<BatchRoomMappingRes> batchRoomMapping(BatchRoomMappingReq req) {
        Preconditions.checkArgument(req.getMappingType() != null, "映射类型不能为空");

        if (ObjectUtil.equal(req.getMappingType(), MappingTypeEnum.BDW_TO_WORMHOLE.getCode())) {
            // BDW到虫洞的映射
            return bdwToWormholeMapping(req);
        } else if (ObjectUtil.equal(req.getMappingType(), MappingTypeEnum.WORMHOLE_TO_BDW.getCode())) {
            // 虫洞到BDW的映射（反向查询）
            return wormholeToBdwMapping(req);
        }

        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "不支持的映射类型"));
    }

    /**
     * BDW到虫洞的映射 - 批量查询优化版本
     */
    private Mono<BatchRoomMappingRes> bdwToWormholeMapping(BatchRoomMappingReq data) {
        // 提取所有需要查询的third_hotel_code
        List<String> thirdHotelCodes = Lists.newArrayList(data.getThirdHotelCode());

        // 批量查询酒店映射关系
        return batchValidateHotelMappings(thirdHotelCodes)
                .defaultIfEmpty(Collections.emptyList())
                .flatMap(hotelMappings -> {
                    if (CollUtil.isEmpty(hotelMappings)){
                        log.info("bdwToWormholeMapping: 未找到映射关系 {}", JacksonUtils.writeValueAsString( data));
                        return Mono.just(BatchRoomMappingRes.builder()
                                .hotelCode("")
                                .thirdHotelCode(data.getThirdHotelCode())
                                .positionList(Collections.emptyList()).build());
                    }

                    // 处理每个酒店的数据
                    return processHotelMappingBatch(data, hotelMappings.get(0));
                });
    }

    /**
     * 虫洞到BDW的映射（反向查询）- 批量查询优化版本
     */
    private Mono<BatchRoomMappingRes> wormholeToBdwMapping(BatchRoomMappingReq data) {
        // 提取所有需要查询的hotel_code
        List<String> hotelCodes = Collections.singletonList(data.getHotelCode());
        // 批量查询酒店映射关系（反向）
        return batchValidateReverseHotelMappings(hotelCodes)
                .defaultIfEmpty(Collections.emptyList())
                .flatMap(hotelMappings -> {
                    if (CollUtil.isEmpty(hotelMappings)){
                        return Mono.just(BatchRoomMappingRes.builder()
                                .hotelCode(data.getHotelCode())
                                .thirdHotelCode(data.getThirdHotelCode())
                                .positionList(Collections.emptyList()).build());
                    }
                    // 处理每个酒店的数据
                    return processReverseHotelMappingBatch(data, hotelMappings.get(0));
                });
    }

    /**
     * 批量验证酒店映射关系（BDW到虫洞）
     */
    private Mono<List<HdsHotelMappingEntity>> batchValidateHotelMappings(List<String> thirdHotelCodes) {
        if (CollUtil.isEmpty(thirdHotelCodes)) {
            return Mono.just(new ArrayList<>());
        }

        HdsHotelMappingQO qo = HdsHotelMappingQO.builder()
                .externalIds(new HashSet<>(thirdHotelCodes))
                .channel(ExternalChannelEnum.BDW.getCode())
                .build();

        return hdsHotelMappingDao.findList(qo);
    }

    /**
     * 批量验证酒店映射关系（虫洞到BDW）
     */
    private Mono<List<HdsHotelMappingEntity>> batchValidateReverseHotelMappings(List<String> hotelCodes) {
        if (CollUtil.isEmpty(hotelCodes)) {
            return Mono.just(new ArrayList<>());
        }

        HdsHotelMappingQO qo = HdsHotelMappingQO.builder()
                .hotelCodeList(new HashSet<>(hotelCodes))
                .channel(ExternalChannelEnum.BDW.getCode())
                .build();

        return hdsHotelMappingDao.findList(qo);
    }

    /**
     * 处理单个酒店的房间映射（BDW到虫洞）- 批量版本
     */
    private Mono<BatchRoomMappingRes> processHotelMappingBatch(
            BatchRoomMappingReq hotelData, HdsHotelMappingEntity thirdToWormhole) {

        String validHotelCode = thirdToWormhole.getHotelCode();

        if (StringUtils.isBlank(validHotelCode)) {
            // 没查询到映射关系，返回除了third_hotel_code都为空的结果
            log.info("bdwToWormholeMapping 查询关联的虫洞酒店编码为空 {}", JacksonUtils.writeValueAsString( hotelData));
            return Mono.just(BatchRoomMappingRes.builder()
                    .hotelCode("")
                    .thirdHotelCode(hotelData.getThirdHotelCode())
                    .positionList(new ArrayList<>())
                    .build());
        }

        // 处理位置映射
        return processPositionMappings(validHotelCode, hotelData.getPositionList())
                .map(positionResults -> BatchRoomMappingRes.builder()
                        .hotelCode(validHotelCode)
                        .thirdHotelCode(hotelData.getThirdHotelCode())
                        .positionList(positionResults)
                        .build());
    }

    /**
     * 处理单个酒店的房间映射（虫洞到BDW）- 批量版本
     */
    private Mono<BatchRoomMappingRes> processReverseHotelMappingBatch(
            BatchRoomMappingReq hotelData, HdsHotelMappingEntity wormholeToBdw) {

        String thirdHotelCode = wormholeToBdw.getExternalId();

        if (StringUtils.isBlank(thirdHotelCode)) {
            // 没查询到映射关系，返回除了hotel_code都为空的结果
            return Mono.just(BatchRoomMappingRes.builder()
                    .hotelCode(hotelData.getHotelCode())
                    .thirdHotelCode("")
                    .positionList(new ArrayList<>())
                    .build());
        }

        // 处理位置映射（反向）
        return processReversePositionMappings(hotelData.getHotelCode(), hotelData.getPositionList())
                .map(positionResults -> BatchRoomMappingRes.builder()
                        .hotelCode(hotelData.getHotelCode())
                        .thirdHotelCode(thirdHotelCode)
                        .positionList(positionResults)
                        .build());
    }


    /**
     * 处理位置映射列表（BDW到虫洞）- 批量查询优化版本
     */
    private Mono<List<BatchRoomMappingRes.PositionMappingResult>> processPositionMappings(
            String hotelCode, List<BatchRoomMappingReq.PositionMappingData> positionList) {

        if (CollUtil.isEmpty(positionList)) {
            return Mono.just(new ArrayList<>());
        }

        // 提取所有需要查询的房间号
        List<String> thirdRoomNos = positionList.stream()
                .map(BatchRoomMappingReq.PositionMappingData::getThirdRoomNo)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询已存在的位置
        return batchQueryPositionsByNames(hotelCode, thirdRoomNos)
                .flatMap(existingPositions -> {
                    // 找出未匹配的房间号
                    Set<String> matchedRoomNos = existingPositions.stream()
                            .map(HdsDevicePositionEntity::getPositionName)
                            .collect(Collectors.toSet());

                    Set<String> unmatchedRoomNos = Sets.difference(Sets.newHashSet(thirdRoomNos),matchedRoomNos);
                    log.info("processPositionMappings matchedRoomNos {} unmatchedRoomNos {}",JacksonUtils.writeValueAsString(matchedRoomNos),JacksonUtils.writeValueAsString(unmatchedRoomNos));
                    // 获取或创建公区
                    return findOrCreatePublicArea(hotelCode,unmatchedRoomNos)
                            .map(publicArea -> {
                                List<BatchRoomMappingRes.PositionMappingResult> results = new ArrayList<>();

                                // 处理匹配到的位置
                                for (HdsDevicePositionEntity position : existingPositions) {
                                    results.add(BatchRoomMappingRes.PositionMappingResult.builder()
                                            .thirdRoomNo(position.getPositionName())
                                            .positionCode(position.getPositionCode())
                                            .positionName(position.getPositionName())
                                            .build());
                                }

                                // 处理未匹配的位置，返回公区信息
                                for (String unmatchedRoomNo : unmatchedRoomNos) {
                                    results.add(BatchRoomMappingRes.PositionMappingResult.builder()
                                            .thirdRoomNo(unmatchedRoomNo)
                                            .positionCode(publicArea.getPositionCode())
                                            .positionName(publicArea.getPositionName())
                                            .build());
                                }

                                return results;
                            });
                });
    }

    /**
     * 批量查询位置信息根据位置名称
     */
    private Mono<List<HdsDevicePositionEntity>> batchQueryPositionsByNames(String hotelCode, List<String> positionNames) {
        if (CollUtil.isEmpty(positionNames)) {
            return Mono.just(new ArrayList<>());
        }

        // 使用新的高效批量查询方法
        return hdsDevicePositionDao.findByHotelCodeAndPositionNames(hotelCode, positionNames);
    }

    /**
     * 处理位置映射列表（虫洞到BDW，反向查询）
     */
    private Mono<List<BatchRoomMappingRes.PositionMappingResult>> processReversePositionMappings(
            String hotelCode, List<BatchRoomMappingReq.PositionMappingData> positionList) {

        if (CollUtil.isEmpty(positionList)) {
            return Mono.just(new ArrayList<>());
        }

        return Flux.fromIterable(positionList)
                .flatMap(position -> processReversePositionMapping(hotelCode, position))
                .collectList();
    }


    /**
     * 处理单个位置映射（虫洞到BDW，反向查询）
     */
    private Mono<BatchRoomMappingRes.PositionMappingResult> processReversePositionMapping(
            String hotelCode, BatchRoomMappingReq.PositionMappingData position) {

        // 根据position_code查找position_name
        if (StringUtils.isNotBlank(position.getPositionCode())) {
            HdsDevicePositionQO qo = new HdsDevicePositionQO();
            qo.setHotelCode(hotelCode);
            qo.setPositionCode(position.getPositionCode());

            return hdsDevicePositionDao.findOne(qo)
                    .flatMap(devicePosition -> {
                        String thirdRoomNo;

                        // 如果positionName不为空且不是"公区"，则thirdRoomNo = positionName
                        if (StringUtils.isNotBlank(devicePosition.getPositionName())
                                && !OTHER_PUBLIC_POSITION_NAME.equals(devicePosition.getPositionName())) {
                            thirdRoomNo = devicePosition.getPositionName();
                        } else {
                            // 否则从guestRequest里解析thirdRoomNo
                            thirdRoomNo = TicketUtils.parseThirdRoomNoFromGuestRequest(position.getGuestRequest());
                        }

                        return Mono.just(BatchRoomMappingRes.PositionMappingResult.builder()
                                .thirdRoomNo(thirdRoomNo)
                                .positionCode(devicePosition.getPositionCode())
                                .positionName(devicePosition.getPositionName())
                                .build());
                    })
                    .switchIfEmpty(Mono.just(BatchRoomMappingRes.PositionMappingResult.builder()
                            .thirdRoomNo("")
                            .positionCode(position.getPositionCode())
                            .positionName("")
                            .build()));
        }

        return Mono.just(BatchRoomMappingRes.PositionMappingResult.builder()
                .thirdRoomNo("")
                .positionCode("")
                .positionName("")
                .build());
    }


    /**
     * 查找或创建公区（返回实体）
     */
    private Mono<HdsDevicePositionEntity> findOrCreatePublicArea(String hotelCode, Set<String> unmatchedRoomNos) {
        if (CollUtil.isEmpty(unmatchedRoomNos)){
            // 不匹配为空时返回一个空实体
            return Mono.just(new HdsDevicePositionEntity());
        }
        HdsDevicePositionQO qo = new HdsDevicePositionQO();
        qo.setHotelCode(hotelCode);
        qo.setFullPositionName(OTHER_PUBLIC_POSITION_NAME);

        return hdsDevicePositionDao.findOne(qo)
                .switchIfEmpty(createPublicArea(hotelCode));
    }

    /**
     * 创建公区
     */
    private Mono<HdsDevicePositionEntity> createPublicArea(String hotelCode) {
        String url = String.format("%s/device_positions/create", wormholeConfiguration.getHoteldsHost());
        DevicePositionSaveReq req = new DevicePositionSaveReq();
        req.setHotelCode(hotelCode);
        req.setDeviceAppType(DeviceTypeEnum.FRONT.getCode());
        req.setBlock("主楼");
        req.setArea(PUBLIC_AREA_NAME);
        req.setPositionName(OTHER_PUBLIC_POSITION_NAME);
        return webClientUtils.sendPost(url, null, req, null, new TypeReference<Object>() {})
                .flatMap(ele-> hdsDevicePositionDao.findOne(HdsDevicePositionQO.builder().hotelCode(hotelCode).fullPositionName(OTHER_PUBLIC_POSITION_NAME).build()));
    }

    /**
     * 查询所有AI酒店
     */
    public Mono<AllAiHotelsRes> getAllAiHotels() {
        HdsHotelMappingQO qo = HdsHotelMappingQO.builder()
                .channel(ExternalChannelEnum.BDW.getCode())
                .build();
        log.info("查询所有AI酒店 {}",JacksonUtils.writeValueAsString(qo));
        return hdsHotelMappingDao.findList(qo)
                .map(mappings -> {
                    // 过滤external_id不为空的记录
                    List<HdsHotelMappingEntity> validMappings = mappings.stream()
                            .filter(mapping -> StringUtils.isNotBlank(mapping.getExternalId()))
                            .toList();

                    List<AllAiHotelsRes.HotelMapping> hotelMappings = validMappings.stream()
                            .map(mapping -> AllAiHotelsRes.HotelMapping.builder()
                                    .bdwHotelCode(mapping.getExternalId())
                                    .hotelCode(mapping.getHotelCode())
                                    .build())
                            .collect(Collectors.toList());

                    List<String> bdwHotelList = validMappings.stream()
                            .map(HdsHotelMappingEntity::getExternalId)
                            .collect(Collectors.toList());

                    List<String> hotelList = validMappings.stream()
                            .map(HdsHotelMappingEntity::getHotelCode)
                            .collect(Collectors.toList());
                    log.info("getAllAiHotels bdwHotelList{} hotelList{}", JacksonUtils.writeValueAsString(bdwHotelList), JacksonUtils.writeValueAsString(hotelList));
                    return AllAiHotelsRes.builder()
                            .mappings(hotelMappings)
                            .bdwHotelList(bdwHotelList)
                            .hotelList(hotelList)
                            .build();
                });
    }

    /**
     * 批量创建不同位置同一需求工单
     */
    public Mono<BatchCreateTicketResp> batchCreatePositionTicket(BatchCreatePositionTicketReq req) {
        if (req.getLocation() == null || CollUtil.isEmpty(req.getLocation().getPositionList()) || StringUtils.isBlank(req.getLocation().getHotelCode())){
            // 正常返回
            log.info("batchCreatePositionTicket location empty {}",JacksonUtils.writeValueAsString(req.getLocation()));
            return Mono.just(BatchCreateTicketResp.builder().single(false).build());
        }
        return processLocationTickets(req);
    }

    /**
     * 处理单个位置的工单创建
     */
    private Mono<BatchCreateTicketResp> processLocationTickets(BatchCreatePositionTicketReq req) {
        BatchCreatePositionTicketReq.LocationInfo location = req.getLocation();
        if (CollUtil.isEmpty(location.getPositionList())) {
            return Mono.empty();
        }
        BatchCreateTicketResp resp = BatchCreateTicketResp.builder()
                .single(ObjectUtil.equal(CollUtil.size(location.getPositionList()),1))
                .build();
        return Flux.fromIterable(location.getPositionList())
                .flatMap(position -> createSingleTicket(req, location.getHotelCode(), position)
                        .map(ticketId-> {
                            if (resp.isSingle()){
                                resp.setTicketId(ticketId);
                            }
                            return resp;
                        }))
                .collectList()
                .thenReturn( resp);
    }

    /**
     * 创建单个工单
     */
    private Mono<String> createSingleTicket(BatchCreatePositionTicketReq ticketRequest,
                                            String hotelCode, BatchCreatePositionTicketReq.PositionInfo position) {

        return resolvePositionCode(hotelCode, position)
                .flatMap(resolvedPosition -> {
                    CreateTicketReq createReq = buildCreateTicketReq(ticketRequest, hotelCode, resolvedPosition, position);
                    return serviceTicketService.createTicket(createReq);
                });
    }

    /**
     * 解析位置编码，如果为空则使用公区
     */
    private Mono<String> resolvePositionCode(String hotelCode, BatchCreatePositionTicketReq.PositionInfo position) {
        if (StringUtils.isNotBlank(position.getPositionCode())) {
            return hdsDevicePositionDao.findOne(HdsDevicePositionQO.builder().positionCode(position.getPositionCode()).build())
                    .switchIfEmpty(Mono.defer(() -> findOrCreatePublicArea(hotelCode, Sets.newHashSet(position.getPositionCode()))))
                    .map(HdsDevicePositionEntity::getPositionCode);
        }

        // 查找或创建公区，返回位置编码
        return findOrCreatePublicArea(hotelCode, Sets.newHashSet(position.getPositionCode()))
                .map(HdsDevicePositionEntity::getPositionCode);
    }

    /**
     * 构建创建工单请求
     */
    private CreateTicketReq buildCreateTicketReq(BatchCreatePositionTicketReq  ticketRequest,
                                                 String hotelCode, String positionCode,
                                                 BatchCreatePositionTicketReq.PositionInfo position) {

        String guestRequest;
        String guestRequestEn;
        String guestRequestIdn;

        // 使用拼接逻辑添加房间号
        if (StringUtils.equals(position.getPositionName(), OTHER_PUBLIC_POSITION_NAME) && StringUtils.isNotBlank(position.getThirdRoomNo())) {
            guestRequest = TicketUtils.buildGuestRequestWithRoomNo(position.getThirdRoomNo(), ticketRequest.getGuestRequest());
            guestRequestEn = TicketUtils.buildGuestRequestWithRoomNo(position.getThirdRoomNo(), ticketRequest.getGuestRequestEn());
            guestRequestIdn = TicketUtils.buildGuestRequestWithRoomNo(position.getThirdRoomNo(), ticketRequest.getGuestRequestIdn());
        } else {
            // 房间匹配的情况，使用固定文案
            guestRequest = ticketRequest.getGuestRequest();
            guestRequestEn = ticketRequest.getGuestRequestEn();
            guestRequestIdn = ticketRequest.getGuestRequestIdn();

        }
        return CreateTicketReq.builder()
                .serviceType(ticketRequest.getServiceType())
                .serviceCategory(ticketRequest.getServiceCategory())
                .serviceSubcategory(ticketRequest.getServiceSubcategory())
                .serviceKeywords(CollUtil.isNotEmpty(ticketRequest.getServiceKeywords()) ?
                        String.join(",", ticketRequest.getServiceKeywords()) : null)
                .closedLoopLevel(ticketRequest.getClosedLoopLevel())
                .replyExceptionType(ticketRequest.getReplyExceptionType())
                .guestRequest(guestRequest)
                .guestRequestEn(guestRequestEn)
                .guestRequestInd(guestRequestIdn)
                .hotelCode(hotelCode)
                .positionCode(positionCode)
                .userType(ticketRequest.getUserType())
                .userId(ticketRequest.getUserId())
                .userName(ticketRequest.getUserName())
                .createType(CreateType.AUTO.getCode())
                .build();
    }


}
