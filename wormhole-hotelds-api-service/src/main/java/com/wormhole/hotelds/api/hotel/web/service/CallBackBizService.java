package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Preconditions;
import com.wormhole.channel.consts.enums.RtcCallStatusEnum;
import com.wormhole.channel.consts.message.CallInfoMessage;
import com.wormhole.common.constant.ClientType;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.client.HotelDsApiClient;
import com.wormhole.hotelds.api.hotel.config.TicketProperties;
import com.wormhole.hotelds.api.hotel.constant.LanguageEnum;
import com.wormhole.hotelds.api.hotel.enums.EmployeeTypeEnum;
import com.wormhole.hotelds.api.hotel.req.CreateTicketSimpleReq;
import com.wormhole.hotelds.api.hotel.util.TicketUtils;
import com.wormhole.hotelds.api.hotel.web.dao.HdsCallLogDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDeviceDao;
import com.wormhole.hotelds.api.hotel.web.dao.CallbackInfoRedisDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDevicePositionDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsEmployeeTicketMappingDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsServiceTicketDao;
import com.wormhole.hotelds.api.hotel.constant.ExpiredFlagEnum;
import com.wormhole.hotelds.api.hotel.web.model.req.*;
import com.wormhole.hotelds.api.hotel.web.model.res.UserAggregatedNotification;
import com.wormhole.hotelds.api.hotel.web.model.res.UserNotificationInfo;
import com.wormhole.hotelds.core.enums.*;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.core.utils.DeviceInitRtcUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/9
 */
@Service
@Slf4j
public class CallBackBizService {

    @Autowired
    private RtcService rtcService;

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    @Autowired
    private ServiceTicketService serviceTicketService;

    @Resource
    private DeviceInRoomService roomDeviceService;

    @Autowired
    private HdsServiceTicketDao hdsServiceTicketDao;

    @Autowired
    private EmployeeTicketMappingService employeeTicketMappingService;
    @Autowired
    private HdsDeviceDao hdsDeviceDao;
    @Autowired
    private HdsEmployeeTicketMappingDao hdsEmployeeTicketMappingDao;
    @Autowired
    private CallLogService callLogService;
    @Autowired
    private CallbackInfoRedisDao callbackInfoRedisDao;
    @Autowired
    private HotelDsApiClient hotelDsApiClient;
    @Autowired
    private HdsCallLogDao hdsCallLogDao;
    @Autowired
    private HdsDevicePositionDao hdsDevicePositionDao;

    @Autowired
    private TicketProperties ticketProperties;

    @Autowired
    private TtsService ttsService;

    @Autowired
    private TicketUtils ticketUtils;

    public Mono<Void> fillCallEndTimeAndPushFeedback(CallInfoMessage request) {

        return hdsServiceTicketDao.findList(getHdsTicketQO(request)).flatMap(entities -> {
            if (CollUtil.isEmpty(entities)) {
                return Mono.empty();
            }
            Set<String> ticketNos = entities.stream().map(HdsServiceTicketEntity::getTicketNo).collect(Collectors.toSet());
            HdsTicketQO updateTicketQO = HdsTicketQO
                    .builder()
                    .ticketNos(ticketNos)
                    .build();
            boolean sosRemindFlag = ObjectUtil.contains(entities.stream().map(HdsServiceTicketEntity::getServiceType).collect(Collectors.toSet()),ServiceType.EMERGENCY.name());
            return hdsServiceTicketDao.fillCallEndTime(updateTicketQO, request.getCallEndTime())
                    .then(Mono.defer(() -> sendTicketCommandRefresh(buildHdsTicketQO(request, ticketNos,sosRemindFlag), true)))
                    .then(Mono.defer(() -> callbackInfoRedisDao.saveCallInfoMsg(request.getRtcRoomId(), RtcCallStatusEnum.FINISHED, request)))
                    .then(handleInquiryTicket(request))
//                    .then(roomDeviceService.pushTicketFeedbackNote(request))
                    .then(Mono.defer(() -> {
                        callLogService.callLogBindServiceTicket(request.getRtcRoomId())
                                .subscribeOn(Schedulers.boundedElastic())
                                .doOnSubscribe(sub -> log.info("并行执行附加异步逻辑，roomId={}", request.getRtcRoomId()))
                                .doOnError(e -> log.error("附加异步逻辑失败", e))
                                .onErrorResume(e -> Mono.empty()) // 吃掉错误
                                .subscribe();
                        return Mono.empty();
                    }));
        });
    }

    private static HdsTicketQO buildHdsTicketQO(CallInfoMessage request, Set<String> ticketNos, boolean sosRemindFlag) {
        HdsTicketQO sendRefreshTicketQO = HdsTicketQO
                .builder()
                .ticketStatus(TicketStatus.PENDING.getCode())
                .rtcRoomId(request.getRtcRoomId())
                .expiredFlag(ExpiredFlagEnum.NORMAL.getCode())
                .hasEndOfCall(true)
                .ticketNos(ticketNos)
                .sosRemindFlag(sosRemindFlag)
                .isCreatedTicket(true)
                .build();
        return sendRefreshTicketQO;
    }

    private static HdsTicketQO getHdsTicketQO(CallInfoMessage request) {
        HdsTicketQO hdsTicketQO = HdsTicketQO
                .builder()
                .rtcRoomId(request.getRtcRoomId())
                .hasEndOfCall(false)
                .build();
        return hdsTicketQO;
    }





    public Mono<Void> sendTicketCommandRefresh(HdsTicketQO ticketQO, boolean defaultSoundEnabled ) {
        return hdsServiceTicketDao.findList(ticketQO)
                .doOnNext(entities -> log.info("sendTicketCommandRefresh,查询到的工单:{},qo:{}", JacksonUtils.writeValueAsString(entities), JacksonUtils.writeValueAsString(ticketQO)))
                .flatMapMany(ticketList -> {
                    if (CollUtil.isEmpty(ticketList)) {
                        return Mono.empty();
                    }
                    return Flux.fromIterable(ticketList);
                })
                .parallel()
                .runOn(Schedulers.boundedElastic())
                .flatMap(entity -> buildNotificationInfoMono(ticketQO, defaultSoundEnabled, entity))
                .sequential()
                .collectList()
                .flatMap(userNotificationInfoList -> {
                    if (userNotificationInfoList.isEmpty()) {
                        return Mono.empty();
                    }

                    // 1. 按用户ID聚合通知，为每个用户建立一个专属的通知包
                    Map<String, UserAggregatedNotification> userNotifications = new HashMap<>();
                    userNotificationInfoList.forEach(info ->
                            info.getUserIds().forEach(userId ->
                                    userNotifications.computeIfAbsent(userId, k -> new UserAggregatedNotification())
                                            .addNotification(info)
                            )
                    );
                    if (userNotifications.isEmpty()) {
                        log.info("sendTicketCommandRefresh, 没有找到需要通知的用户, qo:{}", JacksonUtils.writeValueAsString(ticketQO));
                        return Mono.empty();
                    }
                    // 2. 为每个用户创建并提交一个独立的发送任务
                    List<Mono<Void>> sendTasks = userNotifications.entrySet().stream()
                            .map(entry -> {
                                String userId = entry.getKey();
                                UserAggregatedNotification aggregatedInfo = entry.getValue();

                                TicketCommandMessage message = TicketCommandMessage.builder()
//                                        .refresh(true)
                                        .hasSound(aggregatedInfo.isHasSound())
                                        .hasSos(ticketQO.isSosRemindFlag())
                                        .refreshIntervalSeconds(ticketProperties.getListRefreshSecond())
                                        .tickets(aggregatedInfo.getTickets())
                                        .employeeType(aggregatedInfo.getEmployeeType())
                                        .build();

                                // 向单个用户发送为其定制的消息
                                return rtcService.sendTicketCommandMessage(Collections.singletonList(userId), message);
                            })
                            .toList();

                    // 3. 并发执行所有独立的发送任务
                    return Flux.merge(sendTasks).then();
                })
                .doOnSuccess(v -> {
                    log.info("发送工单刷新消息成功");
                })
                .doOnError(e -> {
                    log.error("发送工单刷新消息失败: {}", e.getMessage());
                })
                .onErrorResume(e -> {
                    log.error("发送工单刷新消息失败: {}", e.getMessage());
                    return Mono.empty();
                })
                .subscribeOn(Schedulers.boundedElastic());
    }


    private Flux<UserNotificationInfo> buildNotificationInfoMono(HdsTicketQO ticketQO, boolean defaultSoundEnabled, HdsServiceTicketEntity entity) {
        // 步骤 1: 异步获取位置名称 (分机播报需要)
        Mono<String> positionNameMono = Mono.just(StringUtils.EMPTY);
        if (Boolean.TRUE.equals(ticketQO.getIsCreatedTicket())) {
            HdsDevicePositionQO positionQO = HdsDevicePositionQO.builder()
                    .positionCode(entity.getPositionCode())
                    .hotelCode(entity.getHotelCode())
                    .build();
            positionNameMono = hdsDevicePositionDao.findOne(positionQO)
                    .map(HdsDevicePositionEntity::getPositionName)
                    .defaultIfEmpty(StringUtils.EMPTY);
        }

        // 步骤 2: 异步获取需要通知的用户及其角色 (employeeType)
        Mono<List<UserWithRoleReq>> usersWithRoleMono = getUsersWithRole(entity);

        // 步骤 3: 组合异步结果，并为不同角色的用户组生成不同的通知
        return Mono.zip(positionNameMono, usersWithRoleMono)
                .flatMapMany(tuple -> {
                    String positionName = tuple.getT1();
                    List<UserWithRoleReq> users = tuple.getT2();

                    if (users.isEmpty()) {
                        return Flux.empty();
                    }

                    // 按角色 (employeeType) 对用户进行分组
                    Map<Integer, List<String>> usersGroupedByType = users.stream()
                            .collect(Collectors.groupingBy(
                                    UserWithRoleReq::getEmployeeType,
                                    Collectors.mapping(UserWithRoleReq::getRtcUserId, Collectors.toList())
                            ));

                    // 步骤 4: 为每个用户组并发生成专属的语音和通知对象
                    return Flux.fromIterable(usersGroupedByType.entrySet())
                            .flatMap(entry -> {
                                Integer employeeType = entry.getKey();
                                List<String> userIdsInGroup = entry.getValue();

                                // 根据角色类型，生成对应的多语言语音URL
                                Mono<Map<String, String>> audioUrlsMono = generateAudioUrlsForGroup(employeeType, positionName, entity, ticketQO);

                                // 为这个用户组创建专属的UserNotificationInfo
                                return audioUrlsMono.map(audioUrls ->
                                        UserNotificationInfo.build(
                                                defaultSoundEnabled,
                                                entity,
                                                userIdsInGroup,
                                                ticketQO.getIsCreatedTicket(),
                                                positionName,
                                                audioUrls,
                                                employeeType
                                        )
                                );
                            });
                });
    }
    /**
     * 获取一个工单需要通知的所有用户，并附带他们的角色类型 (employeeType)。
     * @param entity 工单实体
     * @return 包含用户角色信息的Mono
     */
    private Mono<List<UserWithRoleReq>> getUsersWithRole(HdsServiceTicketEntity entity) {
        // 查找所有需要通知的 rtcUserId
        return employeeTicketMappingService.showTicketRtcUserIds(entity)
                .flatMap(rtcUserIds -> {
                    if (CollUtil.isEmpty(rtcUserIds)) {
                        return Mono.just(Collections.emptyList());
                    }

                    // 异步：通过 rtcUserIds 找到对应的设备信息，建立 rtcUserId -> employeeId 的映射
                    Mono<Map<String, String>> rtcToEmployeeIdMapMono = hdsDeviceDao.findList(HdsDeviceQO.builder().rtcUserIds(rtcUserIds).build())
                            .map(devices -> devices.stream().collect(Collectors.toMap(HdsDeviceEntity::getRtcUserId, HdsDeviceEntity::getUserId, (a, b) -> a)));

                    // 异步：通过 employeeId 找到其角色配置 (employeeType)
                    Mono<Map<String, Integer>> employeeToTypeMapMono = rtcToEmployeeIdMapMono.flatMap(map -> {
                        if (map.isEmpty()) return Mono.just(Collections.emptyMap());
                        List<String> employeeIds = new ArrayList<>(map.values());
                        return hdsEmployeeTicketMappingDao.findList(HdsEmployeeTicketMappingQO.builder().employIdList(employeeIds).hotelCode(entity.getHotelCode()).build())
                                .map(mappings -> mappings.stream().collect(Collectors.toMap(HdsEmployeeTicketMappingEntity::getEmployeeId, HdsEmployeeTicketMappingEntity::getEmployeeType, (a, b) -> a)));
                    });

                    // 组合两次查询的结果，构建最终的 UserWithRole 列表
                    return Mono.zip(rtcToEmployeeIdMapMono, employeeToTypeMapMono)
                            .map(tuple -> {
                                Map<String, String> rtcToEmployeeIdMap = tuple.getT1();
                                Map<String, Integer> employeeToTypeMap = tuple.getT2();

                                return rtcUserIds.stream()
                                        .map(rtcId -> {
                                            String employeeId = rtcToEmployeeIdMap.get(rtcId);
                                            // 如果找不到角色，默认为分机(2)，以提供更详细的信息
                                            Integer employeeType = (employeeId != null) ? employeeToTypeMap.getOrDefault(employeeId, 2) : 2;
                                            return new UserWithRoleReq(rtcId, employeeType);
                                        })
                                        .collect(Collectors.toList());
                            });
                });
    }

    /**
     * 根据员工角色类型，为新工单生成不同的语音内容和URL。
     */
    private Mono<Map<String, String>> generateAudioUrlsForGroup(Integer employeeType, String positionName, HdsServiceTicketEntity entity, HdsTicketQO ticketQO) {
        // 只为新创建的工单生成语音
        // 住脏清洁不生成
        if (!Boolean.TRUE.equals(ticketQO.getIsCreatedTicket()) || StringUtils.isBlank(positionName) || StringUtils.equals(entity.getServiceSubcategory(),ServiceSubcategory.CL_OCCUPIED.getCode())) {
            return Mono.just(Collections.emptyMap());
        }

        if (Objects.equals(employeeType, EmployeeTypeEnum.MAIN_LINE.getCode())) {
            // 对于总机，直接从Nacos配置中获取预设的URL Map
            return Mono.just(ticketProperties.getAudioUrls());
        }

        return Flux.fromIterable(LanguageEnum.getAllCodes())
                .flatMap(lang -> {
                    // 为分机生成详细播报文本
                    String guestRequestForLang = getGuestRequestForLanguage(entity, lang);
                    if (StringUtils.isBlank(guestRequestForLang)) {
                        return Mono.empty(); // 如果该语言的工单内容为空，则跳过
                    }
                    String textForTts = ticketUtils.ttsPositionFullName(positionName) + " " + guestRequestForLang;

                    // 调用TTS服务，并将返回的URL与语言代码配对
                    return ttsService.generateAndUploadAudio(textForTts, lang)
                            .filter(StringUtils::isNotBlank)
                            .map(url -> Tuples.of(lang, url));
                })
                .collectMap(Tuple2::getT1, Tuple2::getT2);
    }

    /**
     * 根据语言代码从工单实体中获取对应的客户请求文本
     */
    private String getGuestRequestForLanguage(HdsServiceTicketEntity entity, String language) {
        return switch (language) {
            case "en" -> entity.getGuestRequestEn();
            case "id" -> entity.getGuestRequestInd();
            default -> entity.getGuestRequest(); // 默认返回中文
        };
    }


    public Mono<Void> sosCallCancelHandleTicket(CallInfoMessage request) {
        HdsDevicePositionQO hdsDevicePositionQO = HdsDevicePositionQO.builder().positionCode(request.getPositionCode()).build();
        return hdsDevicePositionDao.findOne(hdsDevicePositionQO)
                .flatMap(hdsDevicePositionEntity -> createSosTicket(request, hdsDevicePositionEntity,TicketClosedLoopLevel.L2.getCode(),null))
                .then(Mono.defer(() -> hdsServiceTicketDao.fillCallEndTime( getHdsTicketQO(request), request.getCallEndTime())))
                .then(Mono.defer(() -> {
                    HdsTicketQO hdsTicketQO = HdsTicketQO
                            .builder()
                            .ticketStatus(TicketStatus.PENDING.getCode())
                            .rtcRoomId(request.getRtcRoomId())
                            .expiredFlag(ExpiredFlagEnum.NORMAL.getCode())
                            .serviceSubcategory(ServiceSubcategory.EM_SOS.getCode())
                            .hasEndOfCall(true)
                            .sosRemindFlag(true)
                            .isCreatedTicket(true)
                            .build();
                    return sendTicketCommandRefresh(hdsTicketQO, true);
                }))
//                .then(roomDeviceService.pushTicketFeedbackNote(request))
                .then();
    }

    public Mono<Void> sosCallFinishHandleTicket(CallInfoMessage request) {
        log.info("sosCallFinishHandleTicket,request:{}", JacksonUtils.writeValueAsString(request));
        HdsDevicePositionQO hdsDevicePositionQO = HdsDevicePositionQO.builder().positionCode(request.getPositionCode()).build();
        return Mono.zip(hdsDevicePositionDao.findOne(hdsDevicePositionQO),hdsDeviceDao.findOne(HdsDeviceQO.builder().rtcUserId(request.getReceiverUserId()).build()).defaultIfEmpty( new HdsDeviceEntity()))
                .flatMap(tuple2 -> createSosTicket(request, tuple2.getT1(),TicketClosedLoopLevel.L4.getCode(), tuple2.getT2()))
                .then(Mono.defer(() -> hdsServiceTicketDao.fillCallEndTime(getHdsTicketQO(request), request.getCallEndTime())))
//                .then(handleSosTicket(request))
//                .then(roomDeviceService.pushTicketFeedbackNote(request))
                .then(Mono.defer(() -> {
                    callLogService.callLogBindServiceTicket(request.getRtcRoomId())
                            .subscribeOn(Schedulers.boundedElastic())
                            .doOnSubscribe(sub -> log.info("并行执行附加异步逻辑，roomId={}", request.getRtcRoomId()))
                            .doOnError(e -> log.error("附加异步逻辑失败", e))
                            .onErrorResume(e -> Mono.empty()) // 吃掉错误
                            .subscribe();
                    return Mono.empty();
                }));
    }

    private Mono<Void> createSosTicket(CallInfoMessage request, HdsDevicePositionEntity  positionEntity, Integer closedLoopLevel, HdsDeviceEntity device) {
        log.info("createSosTicket hotelCode{} positionCode {} closeLoop {} ",positionEntity.getHotelCode(), positionEntity.getPositionCode(),closedLoopLevel);
        CreateTicketSimpleReq createTicketSimpleReq = new CreateTicketSimpleReq();
        createTicketSimpleReq.setRtcRoomId(request.getRtcRoomId());
        // todo glf
        DeviceTypeEnum deviceTypeEnum = getRoomDeviceTypeByClientType(request.getInitiatorClientType());
        createTicketSimpleReq.setDeviceId(DeviceInitRtcUtil.getDeviceIdFromRtcUserId(request.getInitiatorUserId(),deviceTypeEnum.getCode()));
        createTicketSimpleReq.setClientType(Objects.nonNull(request.getInitiatorClientType()) ? String.valueOf(request.getInitiatorClientType()) : null);
        createTicketSimpleReq.setPositionCode(request.getPositionCode());
        createTicketSimpleReq.setHotelCode(positionEntity.getHotelCode());
        createTicketSimpleReq.setRtcUserId(request.getInitiatorUserId());
        createTicketSimpleReq.setClosedLoopLevel(closedLoopLevel);
        // sos直接接通
        if (device != null && StringUtils.isNotBlank(device.getDeviceId()) && ObjectUtil.equal(device.getDeviceStatus(),DeviceStatusEnum.ONLINE.getCode())){
            createTicketSimpleReq.setCompletedBy(device.getUserId());
            createTicketSimpleReq.setCompletedByName(device.getUserName());
        }
        return serviceTicketService.createSosTicket(createTicketSimpleReq)
                .doOnSuccess(ticketId -> {
                    log.info("创建SOS工单成功,工单ID: {}", ticketId);
                })
                .onErrorResume(e -> {
                    log.error("创建SOS工单失败: {}", e.getMessage());
                    return Mono.empty();
                })
                .then();
    }


    public DeviceTypeEnum getRoomDeviceTypeByClientType(Integer clientType) {
        ClientType clientTypeEnum = ClientType.getByCode(clientType);
        if (Objects.equals(clientTypeEnum, ClientType.MINI_PROGRAM)) {
            return DeviceTypeEnum.WECHAT_MINI_APP;
        } else if (Objects.equals(clientTypeEnum, ClientType.BDW_APP)) {
            return DeviceTypeEnum.BDW_APP;
        } else {
            return DeviceTypeEnum.ROOM;
        }
    }

    public Mono<Void> handleSosTicket(CallInfoMessage request) {
        // 查找符合条件的未完成工单
        HdsTicketQO hdsTicketQO = HdsTicketQO
                .builder()
                .rtcRoomId(request.getRtcRoomId())
                .ticketStatus(TicketStatus.PENDING.getCode())
                .serviceSubcategory(ServiceSubcategory.EM_SOS.getCode())
                .hasEndOfCall(true)
                .build(); //todo  not initialize class
        return hdsServiceTicketDao.findList(hdsTicketQO)
                .flatMapMany(Flux::fromIterable)
                .flatMap(e -> handleTicket(e, new HdsDeviceEntity(), request.getRtcRoomId()))
                .then();
    }

    public Mono<Void> handleInquiryTicket(CallInfoMessage request) {
        // 查找符合条件的未完成工单
        HdsTicketQO hdsTicketQO = HdsTicketQO
                .builder()
                .rtcRoomId(request.getRtcRoomId())
                .ticketStatus(TicketStatus.PENDING.getCode())
//                .serviceType(ServiceType.INQUIRY.name())
                .closedLoopLevel(TicketClosedLoopLevel.L1.getCode())
                .hasEndOfCall(true)
                .build();
        // 只处理L1级工单，自动完成
        return hdsServiceTicketDao.findList(hdsTicketQO)
                .flatMapMany(Flux::fromIterable)
                .flatMap(e -> handleTicket(e, new HdsDeviceEntity(), request.getRtcRoomId()))
                .then();
    }


//    public Mono<Boolean> handleFrontDeskMiniProgramCallback(FrontDeskCallBackTicketReq frontDeskCallBackTicketReq) {
//        String deviceId = frontDeskCallBackTicketReq.getDeviceId();
//        String targetPositionCode = frontDeskCallBackTicketReq.getTargetPositionCode();
//        Preconditions.checkArgument(StringUtils.isNotBlank(deviceId), "设备ID不能为空");
//        Preconditions.checkArgument(StringUtils.isNotBlank(targetPositionCode), "目标位置编码不能为空");
//
//        return hdsDeviceDao.findOne(HdsDeviceQO.builder()
//                        .deviceId(deviceId)
//                        .deviceStatus(DeviceStatusEnum.ONLINE.getCode()).build())
//                .flatMap(originDevice -> {
//                    if (Objects.isNull(originDevice)) {
//                        log.error("设备不存在或不在线,deviceId:{}", deviceId);
//                        return Mono.empty();
//                    }
//
//
//                    // 查找账户工单映射
//                    HdsEmployeeTicketMappingQO build = HdsEmployeeTicketMappingQO
//                            .builder()
//                            .hotelCode(originDevice.getHotelCode())
//                            .employId(originDevice.getUserId())
//                            .acceptTicketStatus(AcceptTicketStatusEnum.ALLOW_ACCEPT.getCode())
//                            .build();
//
//                    return hdsEmployeeTicketMappingDao.findOne(build)
//                            .switchIfEmpty(Mono.defer(() -> {
//                                log.error("hdsEmployeeTicketMapping is Empty,qo:{}", JacksonUtils.writeValueAsString(build));
//                                return Mono.empty();
//                            }))
//                            .flatMap(mapping -> handleCallingTicket(originDevice, mapping, null, targetPositionCode));
//                }).then(Mono.just(true));
//
//    }

//    public Mono<Void> handleFrontDeskCallback(CallInfoMessage request) {
//        String rtcRoomId = request.getRtcRoomId();
//
//        String receiverUserId = request.getReceiverUserId();
//
//        String targetDeviceId = DeviceInitRtcUtil
//                .getDeviceIdFromRtcUserId(receiverUserId, DeviceTypeEnum.ROOM.getCode());
//
//        if (StringUtils.isBlank(targetDeviceId)) {
//            log.error("targetDeviceId is Empty,request:{}", JacksonUtils.writeValueAsString(request));
//            return Mono.empty();
//        }
//
//
//        // 查找发起通话的设备并处理相关工单
//        return Mono.zip(hdsDeviceDao.findOne(buildOriginDeviceQO(request))
//                        , hdsDeviceDao.findOne((buildRoomDeviceQO(targetDeviceId))))
//                .flatMap(tuple -> {
//                    HdsDeviceEntity originDevice = tuple.getT1();
//                    HdsDeviceEntity targetDevice = tuple.getT2();
//                    String positionCode = targetDevice.getPositionCode();
//
//                    if (Objects.isNull(originDevice.getId()) || Objects.isNull(targetDevice.getId())) {
//                        log.info("originDevice or targetDevice is Empty,request:{}", JacksonUtils.writeValueAsString(request));
//                        return Mono.empty();
//                    }
//
//                    // 查找账户工单映射
//                    HdsEmployeeTicketMappingQO build = HdsEmployeeTicketMappingQO
//                            .builder()
//                            .hotelCode(originDevice.getHotelCode())
//                            .employId(originDevice.getUserId())
//                            .acceptTicketStatus(AcceptTicketStatusEnum.ALLOW_ACCEPT.getCode())
//                            .build();
//
//                    return hdsEmployeeTicketMappingDao.findOne(build)
//                            .switchIfEmpty(Mono.defer(() -> {
//                                log.error("hdsEmployeeTicketMapping is Empty,qo:{}", JacksonUtils.writeValueAsString(build));
//                                return Mono.empty();
//                            }))
//                            .flatMap(mapping -> handleCallingTicket(originDevice, mapping, rtcRoomId,positionCode))
//                            .then();
//                })
//                .subscribeOn(Schedulers.boundedElastic())
////                .then(roomDeviceService.pushTicketFeedbackNote(request))
//                .then();
//    }

//    private static HdsDeviceQO buildRoomDeviceQO(String targetDeviceId) {
//        return HdsDeviceQO
//                .builder()
//                .deviceId(targetDeviceId)
//                .deviceStatus(DeviceStatusEnum.ONLINE.getCode())
////                .deviceAppType(DeviceTypeEnum.ROOM.getCode())
//                .build();
//    }
////    private static HdsDeviceQO buildFrontDeviceQO(String targetDeviceId) {
////        return HdsDeviceQO
////                .builder()
////                .deviceId(targetDeviceId)
////                .deviceStatus(DeviceStatusEnum.ONLINE.getCode())
////                .deviceAppType(DeviceTypeEnum.FRONT.getCode())
////                .build();
////    }
//
//    private static HdsDeviceQO buildOriginDeviceQO(CallInfoMessage request) {
//        HdsDeviceQO originDeviceQO = HdsDeviceQO
//                .builder()
//                .rtcUserId(request.getInitiatorUserId())
//                .deviceStatus(DeviceStatusEnum.ONLINE.getCode())
////                .deviceAppType(DeviceTypeEnum.FRONT.getCode())
//                .build();
//        return originDeviceQO;
//    }

    /**
     * 根据映射关系处理工单
     */
//    private Mono<Void> handleCallingTicket(HdsDeviceEntity originDevice, HdsEmployeeTicketMappingEntity accountTicketMapping, String rtcRoomId,String targetDevicePositionCode) {
//        // 根据账户类型处理工单
//        if (Objects.equals(accountTicketMapping.getEmployeeType(), 1)) {
//            // 总机模式处理
//            return processSwitchboardTickets(originDevice, rtcRoomId,targetDevicePositionCode);
//        } else {
//            // 分机模式处理
//            return processExtensionTickets(originDevice, accountTicketMapping, rtcRoomId,targetDevicePositionCode);
//        }
//    }

    /**
     * 处理总机模式的工单
     */
//    private Mono<Void> processSwitchboardTickets(HdsDeviceEntity originDevice, String rtcRoomId,String targetDevicePositionCode) {
//
//        HdsEmployeeTicketMappingQO build = HdsEmployeeTicketMappingQO
//                .builder()
//                .hotelCode(originDevice.getHotelCode())
//                .accountTypeEnum(AccountTypeEnum.EXTENSION)
//                .acceptTicketStatus(AcceptTicketStatusEnum.ALLOW_ACCEPT.getCode()).build();
//        return employeeTicketMappingService.findOnlineEmployeeTicketMapping(build)
//                .flatMap(mappings -> {
//                    List<String> allowProcessServiceCategoryCodes = Collections.emptyList();
//                    if (CollUtil.isEmpty(mappings)) {
//                        allowProcessServiceCategoryCodes = Arrays.stream(ServiceCategory.values())
//                                .filter(e -> Objects.equals(e.getIncompleteProcessType(), 1))
//                                .map(ServiceCategory::getCode)
//                                .toList();
//                    } else {
//                        // 获取所有分机已处理的工单分类
//                        List<String> extensionAccountHandleCategories = mappings.stream()
//                                .map(HdsEmployeeTicketMappingEntity::getTicketCategories)
//                                .filter(StringUtils::isNotBlank)
//                                .flatMap(e1 -> Arrays.stream(e1.split(",")))
//                                .distinct()
//                                .toList();
//
//                        // 过滤出分机已处理的接听类工单分类
//                        List<String> extensionAccountCallProcessCategories = extensionAccountHandleCategories.stream()
//                                .map(e -> ServiceCategory.getByCode(e, false))
//                                .filter(e -> Objects.equals(e.getIncompleteProcessType(), 1))
//                                .map(ServiceCategory::getCode)
//                                .toList();
//
//                        // 获取允许处理的工单分类（接听类工单 - 分机已处理的接听类工单）
//                        // 总机权限改为能处理所有 0520
//                        allowProcessServiceCategoryCodes = Arrays.stream(ServiceCategory.values())
//                                .filter(e -> Objects.equals(e.getIncompleteProcessType(), 1)
////                                        && !extensionAccountCallProcessCategories.contains(e.getCode())
//                                        )
//                                .map(ServiceCategory::getCode)
//                                .toList();
//
//                    }
//                    log.info("accountTicketMapping:{},能处理的工单类型有:{}", JacksonUtils.writeValueAsString(mappings)
//                            , JacksonUtils.writeValueAsString(allowProcessServiceCategoryCodes));
//
//                    HdsTicketQO hdsTicketQO = HdsTicketQO
//                            .builder()
//                            .hotelCode(originDevice.getHotelCode())
//                            .ticketStatus(TicketStatus.PENDING.getCode())
//                            .serviceCategories(allowProcessServiceCategoryCodes)
//                            .positionCode(targetDevicePositionCode)
//                            .expiredFlag(ExpiredFlagEnum.NORMAL.getCode())
//                            .hasEndOfCall(true)
//                            .build();
//                    return hdsServiceTicketDao.findList(hdsTicketQO).doOnNext(list -> log.info("processSwitchboardTickets,qo{},查询到的工单:{}",JacksonUtils.writeValueAsString(hdsTicketQO),JacksonUtils.writeValueAsString(list)))
//                            .flatMapMany(list -> Flux.fromIterable(list))
//                            .flatMap(e -> handleTicket(e, originDevice, rtcRoomId))
//                            .then();
//                });
//    }

    /**
     * 处理分机模式的工单
     */
//    private Mono<Void> processExtensionTickets(HdsDeviceEntity originDevice, HdsEmployeeTicketMappingEntity accountTicketMapping, String rtcRoomId,String targetDevicePositionCode) {
//        String ticketCategories = accountTicketMapping.getTicketCategories();
//        if (StringUtils.isBlank(ticketCategories)) {
//            return Mono.empty();
//        }
//
//        // 获取允许处理的接听类工单分类
//        List<String> allowHandleCategories = Arrays.asList(ticketCategories.split(","));
//        List<String> allowProcessServiceCategoryCodes = allowHandleCategories.stream()
//                .map(e -> ServiceCategory.getByCode(e, false))
//                .filter(Objects::nonNull)
//                .filter(e -> Objects.equals(e.getIncompleteProcessType(), 1))
//                .map(ServiceCategory::getCode)
//                .toList();
//        log.info("accountTicketMapping:{},能处理的工单类型有:{}", JacksonUtils.writeValueAsString(accountTicketMapping)
//                , JacksonUtils.writeValueAsString(allowProcessServiceCategoryCodes));
//
//
//        HdsTicketQO hdsTicketQO = HdsTicketQO
//                .builder()
//                .hotelCode(originDevice.getHotelCode())
//                .ticketStatus(TicketStatus.PENDING.getCode())
//                .serviceCategories(allowProcessServiceCategoryCodes)
//                .hasEndOfCall(true)
//                .positionCode(targetDevicePositionCode)
//                .build();
//        return hdsServiceTicketDao.findList(hdsTicketQO).doOnNext(list -> log.info("processSwitchboardTickets,qo{},查询到的工单:{}",JacksonUtils.writeValueAsString(hdsTicketQO),JacksonUtils.writeValueAsString(list)))
//                .flatMapMany(list -> Flux.fromIterable(list))
//                .flatMap(e -> handleTicket(e, originDevice, rtcRoomId))
//                .then();
//    }


    /**
     * 处理单个工单
     */
    private Mono<Void> handleTicket(HdsServiceTicketEntity ticket, HdsDeviceEntity hdsDeviceEntity, String rtcRoomId) {
        return serviceTicketService.handle(HandleTicketReq.builder()
                        .ticketId(String.valueOf(ticket.getId()))
                        .userId(hdsDeviceEntity.getUserId())
                        .userName(hdsDeviceEntity.getUserName())
                        .rtcRoomId(rtcRoomId)
                        .verifyPermission(false)
                        .isPush(false)
                        .build())
                .subscribeOn(Schedulers.boundedElastic())
                .then();
    }


}
