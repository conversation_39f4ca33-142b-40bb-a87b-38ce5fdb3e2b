package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Preconditions;
import com.wormhole.channel.consts.enums.RtcCallStatusEnum;
import com.wormhole.channel.consts.message.CallInfoMessage;
import com.wormhole.common.constant.ClientType;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.client.HotelDsApiClient;
import com.wormhole.hotelds.api.hotel.config.TicketProperties;
import com.wormhole.hotelds.api.hotel.req.CreateTicketSimpleReq;
import com.wormhole.hotelds.api.hotel.web.dao.HdsCallLogDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDeviceDao;
import com.wormhole.hotelds.api.hotel.web.dao.CallbackInfoRedisDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDevicePositionDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsEmployeeTicketMappingDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsServiceTicketDao;
import com.wormhole.hotelds.api.hotel.constant.ExpiredFlagEnum;
import com.wormhole.hotelds.api.hotel.web.model.res.UserNotificationInfo;
import com.wormhole.hotelds.api.hotel.web.model.req.TicketCommandMessage;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsDevicePositionQO;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsDeviceQO;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsEmployeeTicketMappingQO;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsTicketQO;
import com.wormhole.hotelds.api.hotel.web.model.req.FrontDeskCallBackTicketReq;
import com.wormhole.hotelds.api.hotel.web.model.req.HandleTicketReq;
import com.wormhole.hotelds.core.enums.*;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.core.utils.DeviceInitRtcUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/9
 */
@Service
@Slf4j
public class CallBackBizService {

    @Autowired
    private RtcService rtcService;

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    @Autowired
    private ServiceTicketService serviceTicketService;

    @Resource
    private DeviceInRoomService roomDeviceService;

    @Autowired
    private HdsServiceTicketDao hdsServiceTicketDao;

    @Autowired
    private EmployeeTicketMappingService employeeTicketMappingService;
    @Autowired
    private HdsDeviceDao hdsDeviceDao;
    @Autowired
    private HdsEmployeeTicketMappingDao hdsEmployeeTicketMappingDao;
    @Autowired
    private CallLogService callLogService;
    @Autowired
    private CallbackInfoRedisDao callbackInfoRedisDao;
    @Autowired
    private HotelDsApiClient hotelDsApiClient;
    @Autowired
    private HdsCallLogDao hdsCallLogDao;
    @Autowired
    private HdsDevicePositionDao hdsDevicePositionDao;
    @Autowired
    private TicketProperties ticketProperties;

    public Mono<Void> fillCallEndTimeAndPushFeedback(CallInfoMessage request) {

        return hdsServiceTicketDao.findList(getHdsTicketQO(request)).flatMap(entities -> {
            if (CollUtil.isEmpty(entities)) {
                return Mono.empty();
            }
            Set<String> ticketNos = entities.stream().map(HdsServiceTicketEntity::getTicketNo).collect(Collectors.toSet());
            HdsTicketQO updateTicketQO = HdsTicketQO
                    .builder()
                    .ticketNos(ticketNos)
                    .build();
            boolean sosRemindFlag = ObjectUtil.contains(entities.stream().map(HdsServiceTicketEntity::getServiceType).collect(Collectors.toSet()),ServiceType.EMERGENCY.name());
            return hdsServiceTicketDao.fillCallEndTime(updateTicketQO, request.getCallEndTime())
                    .then(Mono.defer(() -> sendTicketCommandRefresh(buildHdsTicketQO(request, ticketNos,sosRemindFlag), true)))
                    .then(Mono.defer(() -> callbackInfoRedisDao.saveCallInfoMsg(request.getRtcRoomId(), RtcCallStatusEnum.FINISHED, request)))
                    .then(handleInquiryTicket(request))
//                    .then(roomDeviceService.pushTicketFeedbackNote(request))
                    .then(Mono.defer(() -> {
                        callLogService.callLogBindServiceTicket(request.getRtcRoomId())
                                .subscribeOn(Schedulers.boundedElastic())
                                .doOnSubscribe(sub -> log.info("并行执行附加异步逻辑，roomId={}", request.getRtcRoomId()))
                                .doOnError(e -> log.error("附加异步逻辑失败", e))
                                .onErrorResume(e -> Mono.empty()) // 吃掉错误
                                .subscribe();
                        return Mono.empty();
                    }));
        });
    }

    private static HdsTicketQO buildHdsTicketQO(CallInfoMessage request, Set<String> ticketNos, boolean sosRemindFlag) {
        HdsTicketQO sendRefreshTicketQO = HdsTicketQO
                .builder()
                .ticketStatus(TicketStatus.PENDING.getCode())
                .rtcRoomId(request.getRtcRoomId())
                .expiredFlag(ExpiredFlagEnum.NORMAL.getCode())
                .hasEndOfCall(true)
                .ticketNos(ticketNos)
                .sosRemindFlag(sosRemindFlag)
                .build();
        return sendRefreshTicketQO;
    }

    private static HdsTicketQO getHdsTicketQO(CallInfoMessage request) {
        HdsTicketQO hdsTicketQO = HdsTicketQO
                .builder()
                .rtcRoomId(request.getRtcRoomId())
                .hasEndOfCall(false)
                .build();
        return hdsTicketQO;
    }





    public Mono<Void> sendTicketCommandRefresh(HdsTicketQO ticketQO, boolean defaultSoundEnabled ) {
        return hdsServiceTicketDao.findList(ticketQO)
                .doOnNext(entities -> log.info("sendTicketCommandRefresh,查询到的工单:{},qo:{}", JacksonUtils.writeValueAsString(entities), JacksonUtils.writeValueAsString(ticketQO)))
                .flatMapMany(ticketList -> {
                    if (CollUtil.isEmpty(ticketList)) {
                        return Mono.empty();
                    }
                    return Flux.fromIterable(ticketList);
                })
                .parallel()
                .runOn(Schedulers.boundedElastic())
                .flatMap(entity ->
                        employeeTicketMappingService.showTicketRtcUserIds(entity)
                                .map(userIds -> UserNotificationInfo.build(defaultSoundEnabled, entity, userIds))
                )
                .sequential()
                .collectList()
                .flatMap(userNotificationInfoList -> {
                    Map<String, Boolean> userSoundMap = new HashMap<>();

                    userNotificationInfoList.forEach(info ->
                            info.getUserIds().forEach(userId ->
                                    // 如果用户已存在且当前需要声音，或者用户还不存在且当前需要声音，则设置为需要声音
                                    userSoundMap.merge(userId, info.isHasSound(), (existingSound, newSound) -> existingSound || newSound)
                            )
                    );

                    // 如果没有找到用户，则返回空
                    if (userSoundMap.isEmpty()) {
                        log.info("sendTicketCommandRefresh,没有找到用户,qo:{}", JacksonUtils.writeValueAsString(ticketQO));
                        return Mono.empty();
                    }

                    log.info("sendTicketCommandRefresh,查询到的工单:{},对应的用户声音设置:{}",
                            JacksonUtils.writeValueAsString(ticketQO),
                            JacksonUtils.writeValueAsString(userSoundMap));

                    // 创建消息发送任务列表
                    List<Mono<Void>> sendTasks = new ArrayList<>();

                    // 将拥有相同声音设置的用户分组
                    Map<Boolean, List<String>> soundGroupedUsers = userSoundMap.entrySet().stream()
                            .collect(Collectors.groupingBy(
                                    Map.Entry::getValue,
                                    Collectors.mapping(Map.Entry::getKey, Collectors.toList())
                            ));

                    log.info("sendTicketCommandRefresh,,qo:{},分组后的用户声音设置:{}",JacksonUtils.writeValueAsString(ticketQO), JacksonUtils.writeValueAsString(soundGroupedUsers));

                    // 对每个分组创建并发送相应的消息
                    soundGroupedUsers.forEach((needSound, users) -> {
                        TicketCommandMessage build = TicketCommandMessage.builder()
                                .refresh(true)
                                .hasSound(needSound)
                                .hasSos(ticketQO.isSosRemindFlag())
                                .refreshIntervalSeconds(ticketProperties.getListRefreshSecond())
                                .build();

                        sendTasks.add(rtcService.sendTicketCommandMessage(users, build));
                    });

                    // 并行执行所有发送任务
                    return Flux.merge(sendTasks).then();
                })
                .doOnSuccess(v -> {
                    log.info("发送工单刷新消息成功");
                })
                .doOnError(e -> {
                    log.error("发送工单刷新消息失败: {}", e.getMessage());
                })
                .onErrorResume(e -> {
                    log.error("发送工单刷新消息失败: {}", e.getMessage());
                    return Mono.empty();
                })
                .subscribeOn(Schedulers.boundedElastic());
    }
    public Mono<Void> sosCallCancelHandleTicket(CallInfoMessage request) {
        HdsDevicePositionQO hdsDevicePositionQO = HdsDevicePositionQO.builder().positionCode(request.getPositionCode()).build();
        return hdsDevicePositionDao.findOne(hdsDevicePositionQO)
                .flatMap(hdsDevicePositionEntity -> createSosTicket(request, hdsDevicePositionEntity,TicketClosedLoopLevel.L2.getCode(),null))
                .then(Mono.defer(() -> hdsServiceTicketDao.fillCallEndTime( getHdsTicketQO(request), request.getCallEndTime())))
                .then(Mono.defer(() -> {
                    HdsTicketQO hdsTicketQO = HdsTicketQO
                            .builder()
                            .ticketStatus(TicketStatus.PENDING.getCode())
                            .rtcRoomId(request.getRtcRoomId())
                            .expiredFlag(ExpiredFlagEnum.NORMAL.getCode())
                            .serviceSubcategory(ServiceSubcategory.EM_SOS.getCode())
                            .hasEndOfCall(true)
                            .sosRemindFlag(true)
                            .build();
                    return sendTicketCommandRefresh(hdsTicketQO, true);
                }))
//                .then(roomDeviceService.pushTicketFeedbackNote(request))
                .then();
    }

    public Mono<Void> sosCallFinishHandleTicket(CallInfoMessage request) {
        log.info("sosCallFinishHandleTicket,request:{}", JacksonUtils.writeValueAsString(request));
        HdsDevicePositionQO hdsDevicePositionQO = HdsDevicePositionQO.builder().positionCode(request.getPositionCode()).build();
        return Mono.zip(hdsDevicePositionDao.findOne(hdsDevicePositionQO),hdsDeviceDao.findOne(HdsDeviceQO.builder().rtcUserId(request.getReceiverUserId()).build()).defaultIfEmpty( new HdsDeviceEntity()))
                .flatMap(tuple2 -> createSosTicket(request, tuple2.getT1(),TicketClosedLoopLevel.L4.getCode(), tuple2.getT2()))
                .then(Mono.defer(() -> hdsServiceTicketDao.fillCallEndTime(getHdsTicketQO(request), request.getCallEndTime())))
//                .then(handleSosTicket(request))
//                .then(roomDeviceService.pushTicketFeedbackNote(request))
                .then(Mono.defer(() -> {
                    callLogService.callLogBindServiceTicket(request.getRtcRoomId())
                            .subscribeOn(Schedulers.boundedElastic())
                            .doOnSubscribe(sub -> log.info("并行执行附加异步逻辑，roomId={}", request.getRtcRoomId()))
                            .doOnError(e -> log.error("附加异步逻辑失败", e))
                            .onErrorResume(e -> Mono.empty()) // 吃掉错误
                            .subscribe();
                    return Mono.empty();
                }));
    }

    private Mono<Void> createSosTicket(CallInfoMessage request, HdsDevicePositionEntity  positionEntity, Integer closedLoopLevel, HdsDeviceEntity device) {
        log.info("createSosTicket hotelCode{} positionCode {} closeLoop {} ",positionEntity.getHotelCode(), positionEntity.getPositionCode(),closedLoopLevel);
        CreateTicketSimpleReq createTicketSimpleReq = new CreateTicketSimpleReq();
        createTicketSimpleReq.setRtcRoomId(request.getRtcRoomId());
        // todo glf
        DeviceTypeEnum deviceTypeEnum = getRoomDeviceTypeByClientType(request.getInitiatorClientType());
        createTicketSimpleReq.setDeviceId(DeviceInitRtcUtil.getDeviceIdFromRtcUserId(request.getInitiatorUserId(),deviceTypeEnum.getCode()));
        createTicketSimpleReq.setClientType(Objects.nonNull(request.getInitiatorClientType()) ? String.valueOf(request.getInitiatorClientType()) : null);
        createTicketSimpleReq.setPositionCode(request.getPositionCode());
        createTicketSimpleReq.setHotelCode(positionEntity.getHotelCode());
        createTicketSimpleReq.setRtcUserId(request.getInitiatorUserId());
        createTicketSimpleReq.setClosedLoopLevel(closedLoopLevel);
        // sos直接接通
        if (device != null && StringUtils.isNotBlank(device.getDeviceId()) && ObjectUtil.equal(device.getDeviceStatus(),DeviceStatusEnum.ONLINE.getCode())){
            createTicketSimpleReq.setCompletedBy(device.getUserId());
            createTicketSimpleReq.setCompletedByName(device.getUserName());
        }
        return serviceTicketService.createSosTicket(createTicketSimpleReq)
                .doOnSuccess(ticketId -> {
                    log.info("创建SOS工单成功,工单ID: {}", ticketId);
                })
                .onErrorResume(e -> {
                    log.error("创建SOS工单失败: {}", e.getMessage());
                    return Mono.empty();
                })
                .then();
    }


    public DeviceTypeEnum getRoomDeviceTypeByClientType(Integer clientType) {
        ClientType clientTypeEnum = ClientType.getByCode(clientType);
        if (Objects.equals(clientTypeEnum, ClientType.MINI_PROGRAM)) {
            return DeviceTypeEnum.WECHAT_MINI_APP;
        } else if (Objects.equals(clientTypeEnum, ClientType.BDW_APP)) {
            return DeviceTypeEnum.BDW_APP;
        } else {
            return DeviceTypeEnum.ROOM;
        }
    }

    public Mono<Void> handleSosTicket(CallInfoMessage request) {
        // 查找符合条件的未完成工单
        HdsTicketQO hdsTicketQO = HdsTicketQO
                .builder()
                .rtcRoomId(request.getRtcRoomId())
                .ticketStatus(TicketStatus.PENDING.getCode())
                .serviceSubcategory(ServiceSubcategory.EM_SOS.getCode())
                .hasEndOfCall(true)
                .build(); //todo  not initialize class
        return hdsServiceTicketDao.findList(hdsTicketQO)
                .flatMapMany(Flux::fromIterable)
                .flatMap(e -> handleTicket(e, new HdsDeviceEntity(), request.getRtcRoomId()))
                .then();
    }

    public Mono<Void> handleInquiryTicket(CallInfoMessage request) {
        // 查找符合条件的未完成工单
        HdsTicketQO hdsTicketQO = HdsTicketQO
                .builder()
                .rtcRoomId(request.getRtcRoomId())
                .ticketStatus(TicketStatus.PENDING.getCode())
//                .serviceType(ServiceType.INQUIRY.name())
                .closedLoopLevel(TicketClosedLoopLevel.L1.getCode())
                .hasEndOfCall(true)
                .build();
        // 只处理L1级工单，自动完成
        return hdsServiceTicketDao.findList(hdsTicketQO)
                .flatMapMany(Flux::fromIterable)
                .flatMap(e -> handleTicket(e, new HdsDeviceEntity(), request.getRtcRoomId()))
                .then();
    }


    public Mono<Boolean> handleFrontDeskMiniProgramCallback(FrontDeskCallBackTicketReq frontDeskCallBackTicketReq) {
        String deviceId = frontDeskCallBackTicketReq.getDeviceId();
        String targetPositionCode = frontDeskCallBackTicketReq.getTargetPositionCode();
        Preconditions.checkArgument(StringUtils.isNotBlank(deviceId), "设备ID不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(targetPositionCode), "目标位置编码不能为空");

        return hdsDeviceDao.findOne(HdsDeviceQO.builder()
                        .deviceId(deviceId)
                        .deviceStatus(DeviceStatusEnum.ONLINE.getCode()).build())
                .flatMap(originDevice -> {
                    if (Objects.isNull(originDevice)) {
                        log.error("设备不存在或不在线,deviceId:{}", deviceId);
                        return Mono.empty();
                    }


                    // 查找账户工单映射
                    HdsEmployeeTicketMappingQO build = HdsEmployeeTicketMappingQO
                            .builder()
                            .hotelCode(originDevice.getHotelCode())
                            .employId(originDevice.getUserId())
                            .acceptTicketStatus(AcceptTicketStatusEnum.ALLOW_ACCEPT.getCode())
                            .build();

                    return hdsEmployeeTicketMappingDao.findOne(build)
                            .switchIfEmpty(Mono.defer(() -> {
                                log.error("hdsEmployeeTicketMapping is Empty,qo:{}", JacksonUtils.writeValueAsString(build));
                                return Mono.empty();
                            }))
                            .flatMap(mapping -> handleCallingTicket(originDevice, mapping, null, targetPositionCode));
                }).then(Mono.just(true));

    }

    public Mono<Void> handleFrontDeskCallback(CallInfoMessage request) {
        String rtcRoomId = request.getRtcRoomId();

        String receiverUserId = request.getReceiverUserId();

        String targetDeviceId = DeviceInitRtcUtil
                .getDeviceIdFromRtcUserId(receiverUserId, DeviceTypeEnum.ROOM.getCode());

        if (StringUtils.isBlank(targetDeviceId)) {
            log.error("targetDeviceId is Empty,request:{}", JacksonUtils.writeValueAsString(request));
            return Mono.empty();
        }


        // 查找发起通话的设备并处理相关工单
        return Mono.zip(hdsDeviceDao.findOne(buildOriginDeviceQO(request))
                        , hdsDeviceDao.findOne((buildRoomDeviceQO(targetDeviceId))))
                .flatMap(tuple -> {
                    HdsDeviceEntity originDevice = tuple.getT1();
                    HdsDeviceEntity targetDevice = tuple.getT2();
                    String positionCode = targetDevice.getPositionCode();

                    if (Objects.isNull(originDevice.getId()) || Objects.isNull(targetDevice.getId())) {
                        log.info("originDevice or targetDevice is Empty,request:{}", JacksonUtils.writeValueAsString(request));
                        return Mono.empty();
                    }

                    // 查找账户工单映射
                    HdsEmployeeTicketMappingQO build = HdsEmployeeTicketMappingQO
                            .builder()
                            .hotelCode(originDevice.getHotelCode())
                            .employId(originDevice.getUserId())
                            .acceptTicketStatus(AcceptTicketStatusEnum.ALLOW_ACCEPT.getCode())
                            .build();

                    return hdsEmployeeTicketMappingDao.findOne(build)
                            .switchIfEmpty(Mono.defer(() -> {
                                log.error("hdsEmployeeTicketMapping is Empty,qo:{}", JacksonUtils.writeValueAsString(build));
                                return Mono.empty();
                            }))
                            .flatMap(mapping -> handleCallingTicket(originDevice, mapping, rtcRoomId,positionCode))
                            .then();
                })
                .subscribeOn(Schedulers.boundedElastic())
//                .then(roomDeviceService.pushTicketFeedbackNote(request))
                .then();
    }

    private static HdsDeviceQO buildRoomDeviceQO(String targetDeviceId) {
        return HdsDeviceQO
                .builder()
                .deviceId(targetDeviceId)
                .deviceStatus(DeviceStatusEnum.ONLINE.getCode())
//                .deviceAppType(DeviceTypeEnum.ROOM.getCode())
                .build();
    }
//    private static HdsDeviceQO buildFrontDeviceQO(String targetDeviceId) {
//        return HdsDeviceQO
//                .builder()
//                .deviceId(targetDeviceId)
//                .deviceStatus(DeviceStatusEnum.ONLINE.getCode())
//                .deviceAppType(DeviceTypeEnum.FRONT.getCode())
//                .build();
//    }

    private static HdsDeviceQO buildOriginDeviceQO(CallInfoMessage request) {
        HdsDeviceQO originDeviceQO = HdsDeviceQO
                .builder()
                .rtcUserId(request.getInitiatorUserId())
                .deviceStatus(DeviceStatusEnum.ONLINE.getCode())
//                .deviceAppType(DeviceTypeEnum.FRONT.getCode())
                .build();
        return originDeviceQO;
    }

    /**
     * 根据映射关系处理工单
     */
    private Mono<Void> handleCallingTicket(HdsDeviceEntity originDevice, HdsEmployeeTicketMappingEntity accountTicketMapping, String rtcRoomId,String targetDevicePositionCode) {
        // 根据账户类型处理工单
        if (Objects.equals(accountTicketMapping.getEmployeeType(), 1)) {
            // 总机模式处理
            return processSwitchboardTickets(originDevice, rtcRoomId,targetDevicePositionCode);
        } else {
            // 分机模式处理
            return processExtensionTickets(originDevice, accountTicketMapping, rtcRoomId,targetDevicePositionCode);
        }
    }

    /**
     * 处理总机模式的工单
     */
    private Mono<Void> processSwitchboardTickets(HdsDeviceEntity originDevice, String rtcRoomId,String targetDevicePositionCode) {

        HdsEmployeeTicketMappingQO build = HdsEmployeeTicketMappingQO
                .builder()
                .hotelCode(originDevice.getHotelCode())
                .accountTypeEnum(AccountTypeEnum.EXTENSION)
                .acceptTicketStatus(AcceptTicketStatusEnum.ALLOW_ACCEPT.getCode()).build();
        return employeeTicketMappingService.findOnlineEmployeeTicketMapping(build)
                .flatMap(mappings -> {
                    List<String> allowProcessServiceCategoryCodes = Collections.emptyList();
                    if (CollUtil.isEmpty(mappings)) {
                        allowProcessServiceCategoryCodes = Arrays.stream(ServiceCategory.values())
                                .filter(e -> Objects.equals(e.getIncompleteProcessType(), 1))
                                .map(ServiceCategory::getCode)
                                .toList();
                    } else {
                        // 获取所有分机已处理的工单分类
                        List<String> extensionAccountHandleCategories = mappings.stream()
                                .map(HdsEmployeeTicketMappingEntity::getTicketCategories)
                                .filter(StringUtils::isNotBlank)
                                .flatMap(e1 -> Arrays.stream(e1.split(",")))
                                .distinct()
                                .toList();

                        // 过滤出分机已处理的接听类工单分类
                        List<String> extensionAccountCallProcessCategories = extensionAccountHandleCategories.stream()
                                .map(e -> ServiceCategory.getByCode(e, false))
                                .filter(e -> Objects.equals(e.getIncompleteProcessType(), 1))
                                .map(ServiceCategory::getCode)
                                .toList();

                        // 获取允许处理的工单分类（接听类工单 - 分机已处理的接听类工单）
                        // 总机权限改为能处理所有 0520
                        allowProcessServiceCategoryCodes = Arrays.stream(ServiceCategory.values())
                                .filter(e -> Objects.equals(e.getIncompleteProcessType(), 1)
//                                        && !extensionAccountCallProcessCategories.contains(e.getCode())
                                        )
                                .map(ServiceCategory::getCode)
                                .toList();

                    }
                    log.info("accountTicketMapping:{},能处理的工单类型有:{}", JacksonUtils.writeValueAsString(mappings)
                            , JacksonUtils.writeValueAsString(allowProcessServiceCategoryCodes));

                    HdsTicketQO hdsTicketQO = HdsTicketQO
                            .builder()
                            .hotelCode(originDevice.getHotelCode())
                            .ticketStatus(TicketStatus.PENDING.getCode())
                            .serviceCategories(allowProcessServiceCategoryCodes)
                            .positionCode(targetDevicePositionCode)
                            .expiredFlag(ExpiredFlagEnum.NORMAL.getCode())
                            .hasEndOfCall(true)
                            .build();
                    return hdsServiceTicketDao.findList(hdsTicketQO).doOnNext(list -> log.info("processSwitchboardTickets,qo{},查询到的工单:{}",JacksonUtils.writeValueAsString(hdsTicketQO),JacksonUtils.writeValueAsString(list)))
                            .flatMapMany(list -> Flux.fromIterable(list))
                            .flatMap(e -> handleTicket(e, originDevice, rtcRoomId))
                            .then();
                });
    }

    /**
     * 处理分机模式的工单
     */
    private Mono<Void> processExtensionTickets(HdsDeviceEntity originDevice, HdsEmployeeTicketMappingEntity accountTicketMapping, String rtcRoomId,String targetDevicePositionCode) {
        String ticketCategories = accountTicketMapping.getTicketCategories();
        if (StringUtils.isBlank(ticketCategories)) {
            return Mono.empty();
        }

        // 获取允许处理的接听类工单分类
        List<String> allowHandleCategories = Arrays.asList(ticketCategories.split(","));
        List<String> allowProcessServiceCategoryCodes = allowHandleCategories.stream()
                .map(e -> ServiceCategory.getByCode(e, false))
                .filter(Objects::nonNull)
                .filter(e -> Objects.equals(e.getIncompleteProcessType(), 1))
                .map(ServiceCategory::getCode)
                .toList();
        log.info("accountTicketMapping:{},能处理的工单类型有:{}", JacksonUtils.writeValueAsString(accountTicketMapping)
                , JacksonUtils.writeValueAsString(allowProcessServiceCategoryCodes));


        HdsTicketQO hdsTicketQO = HdsTicketQO
                .builder()
                .hotelCode(originDevice.getHotelCode())
                .ticketStatus(TicketStatus.PENDING.getCode())
                .serviceCategories(allowProcessServiceCategoryCodes)
                .hasEndOfCall(true)
                .positionCode(targetDevicePositionCode)
                .build();
        return hdsServiceTicketDao.findList(hdsTicketQO).doOnNext(list -> log.info("processSwitchboardTickets,qo{},查询到的工单:{}",JacksonUtils.writeValueAsString(hdsTicketQO),JacksonUtils.writeValueAsString(list)))
                .flatMapMany(list -> Flux.fromIterable(list))
                .flatMap(e -> handleTicket(e, originDevice, rtcRoomId))
                .then();
    }


    /**
     * 处理单个工单
     */
    private Mono<Void> handleTicket(HdsServiceTicketEntity ticket, HdsDeviceEntity hdsDeviceEntity, String rtcRoomId) {
        return serviceTicketService.handle(HandleTicketReq.builder()
                        .ticketId(String.valueOf(ticket.getId()))
                        .userId(hdsDeviceEntity.getUserId())
                        .userName(hdsDeviceEntity.getUserName())
                        .rtcRoomId(rtcRoomId)
                        .verifyPermission(false)
                        .isPush(false)
                        .build())
                .subscribeOn(Schedulers.boundedElastic())
                .then();
    }


}
