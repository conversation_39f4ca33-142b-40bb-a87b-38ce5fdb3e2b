package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Preconditions;
import com.wormhole.agent.core.model.entity.UserEntity;
import com.wormhole.common.constant.ClientType;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.constant.RoomStatusEnum;
import com.wormhole.hotelds.api.hotel.web.dao.*;
import com.wormhole.hotelds.api.hotel.web.model.req.CallLogQueryReq;
import com.wormhole.hotelds.api.hotel.web.model.req.RoomStatusQueryReq;
import com.wormhole.hotelds.api.hotel.web.model.res.CallLogWithTicketsRes;
import com.wormhole.hotelds.api.hotel.web.model.res.RoomStatusRes;
import com.wormhole.hotelds.api.hotel.web.model.res.ServiceTicketRes;
import com.wormhole.hotelds.core.enums.TicketClosedLoopLevel;
import com.wormhole.hotelds.core.model.entity.HdsCallLogEntity;
import com.wormhole.hotelds.core.model.entity.HdsChatUserEntity;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class CustomerAdminService {

    @Autowired
    private HdsCallLogDao hdsCallLogDao;

    @Autowired
    private HdsServiceTicketDao hdsServiceTicketDao;

    @Autowired
    private HdsDevicePositionDao hdsDevicePositionDao;

    @Autowired
    private HdsChatUserDao hdsChatUserDao;

    @Autowired
    private UserDao userDao;


    public Mono<List<RoomStatusRes>> queryRoomStatus(RoomStatusQueryReq request) {
        log.info("queryRoomStatus RoomStatusQueryReq：{}", JacksonUtils.writeValueAsString(request));
        LocalDate date;
        try {
            date = LocalDate.parse(request.getSearchDate()); // 要求格式为 yyyy-MM-dd
        } catch (DateTimeParseException e) {
            return Mono.error(new IllegalArgumentException("非法日期格式，应为 yyyy-MM-dd"));
        }
        String hotelCode = request.getHotelCode();

        Mono<Set<String>> abnormalSetMono = hdsServiceTicketDao.findAbnormalTicketRooms(hotelCode, date)
                .collect(Collectors.toSet());

        Mono<Set<String>> normalSetMono = hdsServiceTicketDao.findNormalTicketRooms(hotelCode, date)
                .collect(Collectors.toSet());

        Mono<Set<String>> callLogSetMono = hdsCallLogDao.findCallLogRooms(hotelCode, date)
                .collect(Collectors.toSet());

        return Mono.zip(abnormalSetMono, normalSetMono, callLogSetMono)
                .flatMap(tuple -> {
                    Set<String> abnormalSet = tuple.getT1();
                    Set<String> normalSet = tuple.getT2();
                    Set<String> callLogSet = tuple.getT3();


                    Set<String> merged = new HashSet<>();
                    merged.addAll(abnormalSet);
                    merged.addAll(normalSet);
                    merged.addAll(callLogSet);

                    if (merged.isEmpty()) {
                        return Mono.just(Collections.emptyList());
                    }

                    // 构建优先级映射：异常=0，正常=1，其他=2
                    Map<String, Integer> priorityMap = new HashMap<>();
                    for (String code : merged) {
                        if (abnormalSet.contains(code)) {
                            priorityMap.put(code, 0);
                        } else if (normalSet.contains(code)) {
                            priorityMap.put(code, 1);
                        } else {
                            priorityMap.put(code, 2);
                        }
                    }

                    return hdsDevicePositionDao.findRoomInfos(hotelCode, merged)
                            .filter(room -> {
                                String code = room.getPositionCode();
                                RoomStatusEnum roomStatusEnum = RoomStatusEnum.getEnumByCode(request.getAbnormal());
                                if (Objects.equals(RoomStatusEnum.NORMAL, roomStatusEnum)) {
                                    return !abnormalSet.contains(code);
                                } else if (Objects.equals(RoomStatusEnum.ABNORMAL, roomStatusEnum)) {
                                    return abnormalSet.contains(code);
                                }
                                return true;
                            })
                            .map(room -> {
                                room.setAbnormal(abnormalSet.contains(room.getPositionCode()) ?
                                        RoomStatusEnum.ABNORMAL.getCode() : RoomStatusEnum.NORMAL.getCode());
                                room.setHotelCode(hotelCode);
                                return room;
                            })
                            .sort(Comparator
                                    .comparing((RoomStatusRes r) -> priorityMap.getOrDefault(r.getPositionCode(), 2))
                                    .thenComparing(RoomStatusRes::getCreateAt, Comparator.nullsLast(Comparator.naturalOrder()))
                            )
                            .collectList();
                });
    }




    public Mono<PageResult<CallLogWithTicketsRes>> getCallLogDetailPage(CallLogQueryReq req) {
        log.info("getCallLogDetailPage CallLogQueryReq：{}", JacksonUtils.writeValueAsString(req));


        Preconditions.checkArgument(StringUtils.isNotBlank(req.getHotelCode()), "酒店code不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getPositionCode()), "房间code不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getSearchDate()), "日期不能为空");

        return hdsCallLogDao.findConversationId(req.getHotelCode(), req.getPositionCode(), req.getSearchDate())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "conversation not found")))
                .flatMap(conversationId -> {
                    log.info("getCallLogDetailPage conversationId {}", conversationId);
                    // 查询分页数据
                    Mono<List<HdsCallLogEntity>> logsMono =
                            hdsCallLogDao.findCallLogsByConversationId(conversationId, req.getLastId(), req.getPageSize());

                    // 查询总数
                    Mono<Long> countMono = hdsCallLogDao.countByConversationId(conversationId);

                    return Mono.zip(logsMono, countMono)
                            .flatMap(tuple -> {
                                List<HdsCallLogEntity> logs = tuple.getT1();
                                Long total = tuple.getT2();

                                if (CollectionUtil.isEmpty(logs)) {
                                    log.info("logs is null，conversationId: {}", conversationId);
                                    return Mono.just(PageResult.empty(req.getCurrentPage(), req.getPageSize()));
                                }

                                List<String> ticketNos = logs.stream()
                                        .map(HdsCallLogEntity::getRelatedTicketNo)
                                        .filter(Objects::nonNull)
                                        .flatMap(ticketStr -> Arrays.stream(ticketStr.split(",")))
                                        .map(String::trim)
                                        .filter(s -> !s.isEmpty())
                                        .distinct()
                                        .collect(Collectors.toList());

                                return hdsServiceTicketDao.findTicketsByTicketNos(req.getHotelCode(), ticketNos)
                                        .flatMap(tickets -> {

                                            // 提取创建者ID并查询用户信息
                                            Mono<Map<String, HdsChatUserEntity>> wechatUserMono = getWechatUserInfoMap(tickets);
                                            Mono<Map<String, UserEntity>> appUserMono = getAppUserInfoMap(tickets);

                                            Map<String, HdsServiceTicketEntity> ticketMap = tickets.stream()
                                                    .collect(Collectors.toMap(HdsServiceTicketEntity::getTicketNo, Function.identity()));

                                            return Mono.zip(wechatUserMono, appUserMono)
                                                    .map(userTuple -> {
                                                        Map<String, HdsChatUserEntity> wechatUserMap = userTuple.getT1();
                                                        Map<String, UserEntity> appUserMap = userTuple.getT2();

                                                        List<CallLogWithTicketsRes> result = logs.stream().map(log -> {
                                                            CallLogWithTicketsRes res = BeanUtil.copyProperties(log, CallLogWithTicketsRes.class);
                                                            if (StrUtil.isNotBlank(log.getRelatedTicketNo())) {
                                                                List<String> ticketNoList = Arrays.stream(log.getRelatedTicketNo().split(","))
                                                                        .map(String::trim)
                                                                        .filter(StrUtil::isNotBlank)
                                                                        .toList();

                                                                List<ServiceTicketRes> serviceTickets = ticketNoList.stream()
                                                                        .map(ticketMap::get)
                                                                        .filter(Objects::nonNull)
                                                                        .flatMap(ticket -> buildServiceTicketRes(ticket, wechatUserMap, appUserMap).stream())
                                                                        .collect(Collectors.toList());

                                                                res.setServiceTickets(serviceTickets);
                                                            }
                                                            return res;
                                                        }).collect(Collectors.toList());

                                                        String nextLastId = result.isEmpty() ? null : result.get(result.size() - 1).getId();

                                                        log.info("getCallLogDetailPage 构建分页结果完成，当前页: {}, 每页数量: {}, 总数: {}", req.getCurrentPage(), req.getPageSize(), total);
                                                        return PageResult.create(total, result, req.getCurrentPage(), req.getPageSize(), Collections.singletonList(nextLastId));
                                                    });
                                        });
                            });
                });
    }


    private List<ServiceTicketRes> buildServiceTicketRes(HdsServiceTicketEntity entity,
                                                         Map<String, HdsChatUserEntity> wechatUserMap,
                                                         Map<String, UserEntity> appUserMap) {
        if (entity == null) {
            return Collections.emptyList();
        }

        ServiceTicketRes res = BeanUtil.copyProperties(entity, ServiceTicketRes.class);

        if (Objects.equals(entity.getClientType(), ClientType.MINI_PROGRAM.getCode())) {
            HdsChatUserEntity user = wechatUserMap.get(entity.getDeviceId());
            if (user != null) {
                res.setPhone(user.getMobile());
            }
        } else if (Objects.equals(entity.getClientType(), ClientType.BDW_APP.getCode())) {
            UserEntity user = appUserMap.get(entity.getDeviceId());
            if (user != null) {
                res.setPhone(user.getPhone());
            }
        }

        if (ObjectUtil.equal(entity.getClosedLoopLevel(), TicketClosedLoopLevel.L1.getCode())){
            res.setCompletedByName("AI");
        }

        return Collections.singletonList(res);
    }

    public Mono<Map<String, UserEntity>> getAppUserInfoMap(List<HdsServiceTicketEntity> tickets) {
        Set<String> appUserIds = extractBdwAppCreatorIds(tickets);
        return CollUtil.isEmpty(appUserIds)
                ? Mono.just(Collections.emptyMap())
                :  userDao.fetchBdwUserInfo(appUserIds);
    }

    private Set<String> extractBdwAppCreatorIds(List<HdsServiceTicketEntity> tickets) {
        return tickets.stream()
                .filter(e -> Objects.equals(e.getClientType(), ClientType.BDW_APP.getCode()))
                .map(HdsServiceTicketEntity::getDeviceId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }


    public Mono<Map<String, HdsChatUserEntity>> getWechatUserInfoMap(List<HdsServiceTicketEntity> tickets) {
        Set<String> wechatUserIds = extractWechatCreatorIds(tickets);
        return CollUtil.isEmpty(wechatUserIds)
                ? Mono.just(Collections.emptyMap())
                :  hdsChatUserDao.fetchWechatUserInfo(wechatUserIds);
    }

    private Set<String> extractWechatCreatorIds(List<HdsServiceTicketEntity> tickets) {
        return tickets.stream()
                .filter(e -> Objects.equals(e.getClientType(), ClientType.MINI_PROGRAM.getCode()))
                .map(HdsServiceTicketEntity::getDeviceId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

}
