package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.collection.*;
import cn.hutool.core.date.*;
import cn.hutool.core.util.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import com.wormhole.agent.core.enums.UserStatus;
import com.wormhole.agent.core.model.entity.UserEntity;
import com.wormhole.channel.consts.*;
import com.wormhole.channel.consts.helper.*;
import com.wormhole.channel.consts.message.*;
import com.wormhole.channel.consts.vo.*;
import com.wormhole.common.config.WormholeTranslationConfig;
import com.wormhole.common.constant.*;
import com.wormhole.common.enums.*;
import com.wormhole.common.exception.*;
import com.wormhole.common.result.*;
import com.wormhole.common.util.*;
import com.wormhole.hotelds.api.hotel.constant.*;
import com.wormhole.hotelds.api.hotel.req.*;
import com.wormhole.hotelds.api.hotel.util.*;
import com.wormhole.hotelds.api.hotel.web.dao.*;
import com.wormhole.hotelds.api.hotel.web.model.req.*;
import com.wormhole.hotelds.api.hotel.web.model.res.*;
import com.wormhole.hotelds.api.hotel.web.model.entity.UserLoginDeviceRecord;
import com.wormhole.hotelds.api.hotel.web.model.req.UserReq;
import com.wormhole.hotelds.api.hotel.config.WechatProperties;
import org.redisson.api.RedissonReactiveClient;
import static com.wormhole.hotelds.api.hotel.constant.RedisConstant.DeviceRedisKeyConstant;
import com.wormhole.hotelds.core.enums.*;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.core.model.req.*;
import com.wormhole.hotelds.core.model.resp.*;
import com.wormhole.hotelds.core.utils.*;
import com.wormhole.mq.producer.*;
import jakarta.annotation.*;
import lombok.*;
import lombok.extern.slf4j.*;
import org.apache.commons.lang3.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.*;
import org.springframework.data.r2dbc.core.*;
import org.springframework.data.redis.core.*;
import org.springframework.data.relational.core.query.*;
import org.springframework.http.*;
import org.springframework.stereotype.*;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.*;
import reactor.util.function.*;

import java.time.*;
import java.util.Objects;
import java.util.Optional;
import java.util.*;
import java.util.function.Function;
import java.util.stream.*;

import static com.wormhole.common.config.WormholeTranslationConfig.TranslationKeys.TOAST_DEVICE_NOT_ACTIVE;

@Component
@Slf4j
@RequiredArgsConstructor
public class DeviceBindService {

    private final R2dbcEntityTemplate r2dbcEntityTemplate;

    @Autowired
    private RtcHttpHelper rtcHttpHelper;

    @Autowired
    private RtcHelper rtcHelper;

    @Autowired
    private HdsDeviceDao hdsDeviceDao;

    @Autowired
    private HdsDeviceLatestDao hdsDeviceLatestDao;

    @Autowired
    private PositionWithDeviceDao hdsDevicePositionDao;

    @Autowired
    private TicketUtils ticketUtils;

    @Autowired
    private WormholeTranslationConfig translationConfig;

    @Autowired
    private HdsEmployeeTicketMappingDao hdsEmployeeTicketMappingDao;

    @Autowired
    private ServiceTicketService serviceTicketService;

    @Autowired
    private HdsServiceTicketDao hdsServiceTicketDao;

    @Autowired
    private CallBackBizService callBackBizService;

    @Autowired
    private WechatUserService wechatUserService;

    @Autowired
    private EmployeeService employeeService;

    @Resource
    private ReactiveMessageSender reactiveMessageSender;

    @Autowired
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    @Autowired
    private OrderRoomRedisDao orderRoomRedisDao;

    @Resource
    private WechatProperties wechatProperties;

    @Autowired
    private UserLoginDeviceRecordDao userLoginDeviceRecordDao;
    @Autowired
    private UserDao userDao;

    @Autowired
    private LockUtils lockUtil;

    public Mono<HotelRoomListVo> getRoomsByHotel(GetRoomsReq req, String deviceType) {
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getHotelCode()) || StringUtils.isNotBlank(req.getHotelName()), "hotel must not be blank");
        return Mono.zip(checkAndGetHotel(req.getHotelCode()),
                        hdsDevicePositionDao.getHdsDevicePositions(req.getHotelCode(), req.getPositionKey(), deviceType),
                        hdsDeviceDao.getBindPositionCodes(req.getHotelCode()))
                .flatMap(tuple3->{
                    HdsHotelInfoEntity hotelInfo = tuple3.getT1();
                    List<HdsDevicePositionEntity> allPositions = tuple3.getT2();
                    Set<String> alreadyBinds = tuple3.getT3();
                    List<HdsDevicePositionEntity> notBindPositions = allPositions.stream().filter(ele -> !alreadyBinds.contains(ele.getPositionCode())).toList();
                    HotelRoomListVo roomListVo = new HotelRoomListVo();
                    roomListVo.setHotelCode(hotelInfo.getHotelCode());
                    roomListVo.setHotelName(hotelInfo.getHotelName());
                    return buildHotelRoomListVo(notBindPositions, roomListVo);
                });
    }


    private Mono<HotelRoomListVo> buildHotelRoomListVo(List<HdsDevicePositionEntity> roomList, HotelRoomListVo roomListVo) {
        Map<String, List<HdsDevicePositionEntity>> listMap = roomList.stream().collect(Collectors.groupingBy(DeviceUtils::getBlockFloorName, Collectors.toList()));
        List<RoomDetailVo> roomDetailVos = listMap.entrySet().stream().map(values -> {
            RoomDetailVo roomDetailVo = new RoomDetailVo();
            List<RoomInfoBaseVo> rooms = values.getValue().stream().map(ele -> {
                RoomInfoBaseVo vo = new RoomInfoBaseVo();
                vo.setPositionCode(ele.getPositionCode());
                vo.setRoomName(ele.getPositionName());
                vo.setSortOrder(ele.getPositionSortOrder());
                return vo;
            }).sorted(Comparator.comparing(RoomInfoBaseVo::getSortOrder,Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
            roomDetailVo.setBlockFloorName(values.getKey());
            roomDetailVo.setRooms(rooms);
            roomDetailVo.setSortOrder(values.getValue().get(0).getBlockAreaSortOrder());
            return roomDetailVo;
        }).sorted(Comparator.comparing(RoomDetailVo::getSortOrder,  Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
        roomListVo.setRoomList(roomDetailVos);
        return Mono.just(roomListVo);
    }

    public Mono<DeviceBindResp> bindDevice(BindDeviceReq req) {
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getDeviceSn()) || StringUtils.isNotBlank(req.getImei()), "device must not be blank");
        return Mono.zip(checkDeviceToBind(req.getDeviceSn(), req.getDeviceId(), req.getImei()), checkAndGetHotel(req.getHotelCode()), checkAndGetRoom(req.getHotelCode(),req.getPositionCode()))
                .flatMap(tuple -> {
                    HdsDeviceEntity device = tuple.getT1();
                    HdsHotelInfoEntity hotelInfo = tuple.getT2();
                    HdsDevicePositionEntity roomEntity = tuple.getT3();
                    // 构建查询条件
                    Criteria criteria = Criteria.where(HdsDeviceFieldEnum.id.name()).is(device.getId())
                            .and(HdsDeviceFieldEnum.device_status.name()).is(DeviceStatusEnum.PENDING_ACTIVATION.getCode());
                    // 构建更新操作
                    Update update = Update.update(HdsDeviceFieldEnum.device_id.name(), req.getDeviceId())
                            .set(HdsDeviceFieldEnum.device_status.name(), DeviceStatusEnum.ONLINE.getCode())
                            .set(HdsDeviceFieldEnum.hotel_code.name(), req.getHotelCode())
                            .set(HdsDeviceFieldEnum.position_code.name(), roomEntity.getPositionCode())
                            .set(HdsDeviceFieldEnum.device_app_type.name(), req.getDeviceType())
                            .set(HdsDeviceFieldEnum.active_time.name(), LocalDateTime.now())
                            .set(HdsDeviceFieldEnum.updated_at.name(), LocalDateTime.now());
                    DeviceBindResp resp = new DeviceBindResp();
                    BeanUtils.copyProperties(req,resp);
                    resp.setHotelName(hotelInfo.getHotelName());
                    resp.setPositionFullName(DeviceUtils.getPositionFullName(roomEntity));
                    resp.setBindTime(LocalDateTime.now());
                    // 执行更新
                    return r2dbcEntityTemplate.update(Query.query(criteria), update, HdsDeviceEntity.class)
                            .then(insertDeviceBindLog(req))
                            .then(Mono.just(resp))
                            .doOnError(e -> log.error("bind device error {}", JacksonUtils.writeValueAsString(req), e));

                });

    }

    private Mono<Boolean> insertDeviceBindLog(BindDeviceReq req) {
        HdsDeviceLogEntity logEntity = new HdsDeviceLogEntity();
        org.springframework.beans.BeanUtils.copyProperties(req, logEntity);
        logEntity.setEventType(DeviceLogEventTypeEnum.activation.getCode());
        logEntity.setEventTime(LocalDateTime.now());
        return r2dbcEntityTemplate.insert(logEntity).flatMap(ele -> Mono.just(true));
    }

    private Mono<HdsDeviceEntity> checkDeviceToBind(String deviceSn, String deviceId, String imei) {

        // 检验deviceSn是否落库
        return getDeviceEntity(deviceSn, null, imei).flatMap(device -> {
            if (ObjectUtil.notEqual(device.getDeviceStatus(), 0)) {
                return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "device must be initial"));
            }
            return Mono.just(device);
        });
    }

    private Mono<HdsDeviceEntity> getDeviceEntity(String deviceSn, String deviceId, String imei) {
        // 设备入库存在
        Criteria criteria = Criteria.empty();
        if (StringUtils.isNotBlank(deviceSn)) {
            criteria = criteria.and(HdsDeviceFieldEnum.device_sn.name()).is(deviceSn);
        }
        if (StringUtils.isNotBlank(imei)) {
            criteria = criteria.and(HdsDeviceFieldEnum.imei.name()).is(imei);
        }
        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.select(query, HdsDeviceEntity.class)
                .collectList()
                .flatMap(deviceList -> {
                    if ( CollectionUtil.isEmpty(deviceList)) {
                        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "设备未入库"));
                    }
                    return Mono.justOrEmpty(deviceList
                            .stream()
                            .filter(e -> e.getDeviceStatus() == DeviceStatusEnum.PENDING_ACTIVATION.getCode())
                            .findFirst()
                    ).switchIfEmpty(Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "设备已绑定")));
                });
    }

    private Mono<HdsDevicePositionEntity> checkAndGetRoom(String hotelCode,String positionCode) {
        Criteria roomCr = Criteria.where(HdsDevicePositionFieldEnum.hotel_code.name()).is(hotelCode)
                .and(HdsDevicePositionFieldEnum.position_code.name()).is(positionCode);
        Query roomQuery = Query.query(roomCr).sort(Sort.by(Sort.Direction.DESC, HdsDevicePositionFieldEnum.id.name())).limit(1);
        return r2dbcEntityTemplate.selectOne(roomQuery, HdsDevicePositionEntity.class)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "room is not exist")));
    }

    public Mono<HdsHotelInfoEntity> checkAndGetHotel(String hotelCode) {
        Criteria hotelCr = Criteria.where(HdsHotelInfoFieldEnum.hotel_code.name()).is(hotelCode);
        Query hotelQuery = Query.query(hotelCr).sort(Sort.by(Sort.Direction.DESC, HdsHotelInfoFieldEnum.id.name())).limit(1);
        return r2dbcEntityTemplate.selectOne(hotelQuery, HdsHotelInfoEntity.class)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "hotel is not exist")));

    }

    private Mono<DevicePositionVo> checkAndGetRoomOrDevice(GetHotelPositionReq req) {
        // 确定如何获取位置信息的Mono流
        Mono<DevicePositionVo> positionMono;
        if (StringUtils.isNotBlank(req.getPositionCode())) {
            // 如果请求中包含positionCode，直接使用它和hotelCode获取位置信息
            positionMono = getDevicePositionVoMono(req.getHotelCode(), req.getPositionCode());
        } else {
            // 否则，先通过deviceId获取设备实体，再使用其hotelCode和positionCode获取位置信息
            positionMono = hdsDeviceDao.findOne(HdsDeviceQO.builder().deviceId(req.getDeviceId()).build())
                    .flatMap(device -> getDevicePositionVoMono(device.getHotelCode(), device.getPositionCode()));
        }
        // 为位置信息设置deviceId并返回
        return positionMono
                .map(devicePositionVo -> {
                    devicePositionVo.setDeviceId(req.getDeviceId());
                    return devicePositionVo;
                });
    }

    private Mono<DevicePositionVo> getDevicePositionVoMono(String hotelCode, String positionCode) {
        BindDeviceReq deviceReq = new BindDeviceReq();
        deviceReq.setPositionCode(positionCode);
        deviceReq.setHotelCode(hotelCode);
        return checkAndGetRoom(deviceReq.getHotelCode(),deviceReq.getPositionCode()).flatMap(roomEntity -> {
            DevicePositionVo vo = new DevicePositionVo();
            vo.setPositionCode(roomEntity.getPositionCode());
            vo.setPositionName(roomEntity.getPositionName());
            return Mono.just(vo);
        });
    }

    public Mono<DeviceInitResp> getInitDeviceInfo(DeviceInitReq req) {
        String deviceId = req.getDeviceId();
        String language = req.getLanguage();
        return hdsDeviceDao.findOne(HdsDeviceQO.builder().deviceId(deviceId).build())
                .switchIfEmpty(Mono.error(new BusinessException("AUTH-DEVICE-INVALID", "无效的设备码")))
                .flatMap(deviceBaseResp -> {
                    // 设备必须是在线状态
                    if (ObjectUtil.equal(deviceBaseResp.getDeviceStatus(), DeviceStatusEnum.PENDING_ACTIVATION.getCode())) {
                        log.info("language : {}  {} ",language,translationConfig.translateSync(TOAST_DEVICE_NOT_ACTIVE,language));
                        return Mono.error(new BusinessException(String.valueOf(HttpStatus.PRECONDITION_FAILED.value()), translationConfig.translateSync(TOAST_DEVICE_NOT_ACTIVE,language)));
                    }
                    if (ObjectUtil.equal(deviceBaseResp.getDeviceStatus(), DeviceStatusEnum.DEACTIVATED.getCode())) {
                        return Mono.error(new BusinessException("AUTH-DEVICE-DEACTIVATED", "设备已停用"));
                    }
                    if (ObjectUtil.isNull(deviceBaseResp.getPositionCode())|| Objects.isNull(deviceBaseResp.getHotelCode())) {
                        return Mono.error(new BusinessException("AUTH-DEVICE-INVALID", "设备归属位置为空"));
                    }
                    String rtcUserId = DeviceInitRtcUtil.getRtcUserId(deviceBaseResp.getDeviceAppType(), deviceBaseResp.getDeviceId());

                    Mono<Tuple2<String, String>> qrCodeMono = DeviceTypeEnum.ROOM.getCode().equals(deviceBaseResp.getDeviceAppType())
                            ? wechatUserService.generateQRCodeAndUpload(deviceBaseResp.getHotelCode(), deviceBaseResp.getPositionCode())
                            : Mono.just(Tuples.of("", ""));
                    // 获取rtc 信息
                    return Mono.zip(getRtcTokenInfo(rtcUserId), checkAndGetHotel(deviceBaseResp.getHotelCode()),
                                    checkAndGetRoom(deviceBaseResp.getHotelCode(),deviceBaseResp.getPositionCode()), qrCodeMono)
                            .flatMap(tuple -> {
                                RtcCallbackInfoVO rtcInfo = tuple.getT1();
                                HdsHotelInfoEntity hotelInfo = tuple.getT2();
                                HdsDevicePositionEntity room = tuple.getT3();
                                Tuple2<String, String> qrCodeTuple = tuple.getT4();
                                return Mono.just(DeviceInitResp.builder()
                                        .rtcToken(rtcInfo.getToken())
                                        .rtcManufacturer("volcengine")// 默认先写死成火山引擎
                                        .serverCallBackUrl(rtcInfo.getCallBackUrl())
                                        .secret(rtcInfo.getSecret())
                                        .rtcAppId(rtcInfo.getRtcAppId())
                                        .callTimeOutSeconds(30)
                                        .hotelCode(hotelInfo.getHotelCode())
                                        .hotelName(hotelInfo.getHotelName())
                                        .sosSwitch(hotelInfo.getSosSwitch())
                                        .dialogSwitch(hotelInfo.getDialogSwitch())
                                        .callCommand(hotelInfo.getCallCommand())
                                        .positionCode(deviceBaseResp.getPositionCode())
                                        .deviceId(deviceBaseResp.getDeviceId())
                                        .deviceType(deviceBaseResp.getDeviceAppType())
                                        .userId(rtcUserId)
                                        .deviceQrCodeUrl(qrCodeTuple.getT1())
                                        .normalQrCodeUrl(qrCodeTuple.getT2())
                                        .positionName(room.getPositionName())
                                        .positionFullName(DeviceUtils.getPositionFullName(room))
                                        .build());

                            })
                            .flatMap(device -> hdsDeviceLatestDao.insertOrUpdate(req).thenReturn(device));

                });
    }

    public Mono<DeviceInitResp> initDeviceInfo(DeviceInitReq req) {
        log.info("initDeviceInfo request: {}", JacksonUtils.writeValueAsString(req));
        // 参数验证
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getDeviceId()), "设备ID不能为空");
        if (Objects.equals(req.getDeviceAppType(), DeviceTypeEnum.FRONT_APP.getCode())) {
            return initAppDeviceInfo(req);
        }
        return getInitDeviceInfo(req);
    }


    /**
     * 设备初始化信息(对象入参版本)
     * @param req 设备初始化请求对象
     * @return 设备初始化响应
     */
    public Mono<DeviceInitResp> initAppDeviceInfo(DeviceInitReq req) {
        log.info("getInitDeviceInfoByObject request: {}", JacksonUtils.writeValueAsString(req));

        // 参数验证
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getDeviceId()), "设备ID不能为空");

        // 使用分布式锁，防止并发初始化同一设备
        String lockKey = DeviceRedisKeyConstant.getInitAppDeviceInfoLockKey(req.getDeviceId());
        return lockUtil.tryLock(
                lockKey,
                6000L, // 锁持有时间6秒
                6000,
                () -> doInitAppDeviceInfo(req)
        );
    }

    /**
     * 执行设备初始化的核心逻辑
     */
    private Mono<DeviceInitResp> doInitAppDeviceInfo(DeviceInitReq req) {
        HdsDeviceQO build = HdsDeviceQO.builder()
                .deviceId(req.getDeviceId())
//                .hotelCode(req.getHotelCode())
                .deviceAppType(Collections.singletonList(req.getDeviceAppType()))
                .build();
        return hdsDeviceDao.findOne(build)
                .flatMap(existingDevice -> updateExistingDevice(existingDevice, req))
                .switchIfEmpty(createNewDevice(req))
                .flatMap(device -> hdsDeviceLatestDao.insertOrUpdate(req).thenReturn(device))
                .flatMap(this::processDeviceInit)
                .doOnSuccess(response -> log.info("getInitDeviceInfoByObject success for deviceId: {}", req.getDeviceId()))
                .doOnError(e -> log.error("getInitDeviceInfoByObject failed for deviceId: {}", req.getDeviceId(), e));
    }

    /**
     * 更新现有设备记录
     */
    private Mono<HdsDeviceEntity> updateExistingDevice(HdsDeviceEntity existingDevice, DeviceInitReq req) {
        log.info("Device found, updating device: {} req {}", JacksonUtils.writeValueAsString(existingDevice), JacksonUtils.writeValueAsString(req));
        existingDevice.setHotelCode(req.getHotelCode());
        existingDevice.setUserId(req.getUserId());
        existingDevice.setUserName(req.getUserName());
        existingDevice.setActiveTime(LocalDateTime.now());
        existingDevice.setDeviceStatus(DeviceStatusEnum.ONLINE.getCode());
        return r2dbcEntityTemplate.update(existingDevice)
                .thenReturn(existingDevice)
                .doOnSuccess(device -> log.info("Device updated successfully: {}", JacksonUtils.writeValueAsString(device)))
                .doOnError(e -> log.error("Failed to update device: {}", req.getDeviceId(), e));
    }

    /**
     * 创建新设备记录
     */
    private Mono<HdsDeviceEntity> createNewDevice(DeviceInitReq req) {
        log.info("Device not found, creating new device: {}", req.getDeviceId());
        String rtcUserId = DeviceInitRtcUtil.getRtcUserId(req.getDeviceAppType(), req.getDeviceId());

        HdsDeviceEntity newDevice = new HdsDeviceEntity();
        newDevice.setDeviceId(req.getDeviceId());
        newDevice.setHotelCode(req.getHotelCode());
        newDevice.setPositionCode("-");
        newDevice.setDeviceStatus(DeviceStatusEnum.ONLINE.getCode());
        newDevice.setDeviceAppType(req.getDeviceAppType());
        newDevice.setActiveTime(LocalDateTime.now());
        newDevice.setModelId(0L);
        newDevice.setCreatedBy(req.getUserId());
        newDevice.setCreatedByName(req.getUserName());
        newDevice.setCreatedAt(LocalDateTime.now());
        newDevice.setRowStatus(RowStatusEnum.VALID.getId());
        newDevice.setRtcUserId(rtcUserId);
        newDevice.setUserId(req.getUserId());
        newDevice.setUserName(req.getUserName());

        return r2dbcEntityTemplate.insert(newDevice)
                .flatMap(this::saveDeviceLog)
                .doOnSuccess(device -> log.info("New device and log created successfully: {}", JacksonUtils.writeValueAsString(device)))
                .doOnError(e -> log.error("Failed to create new device: {}", req.getDeviceId(), e));
    }

    /**
     * 保存设备日志
     */
    private Mono<HdsDeviceEntity> saveDeviceLog(HdsDeviceEntity device) {
        HdsDeviceLogEntity hdsDeviceLogEntity = new HdsDeviceLogEntity();
        hdsDeviceLogEntity.setDeviceId(device.getDeviceId());
        hdsDeviceLogEntity.setEventType(DeviceLogEventTypeEnum.activation.getCode()); // 使用激活事件类型
        hdsDeviceLogEntity.setEventTime(LocalDateTime.now());
        hdsDeviceLogEntity.setCreatedAt(LocalDateTime.now());
        hdsDeviceLogEntity.setCreatedBy(device.getUserId());
        hdsDeviceLogEntity.setCreatedByName(device.getUserName());
        hdsDeviceLogEntity.setRowStatus(RowStatusEnum.VALID.getId());

        return r2dbcEntityTemplate.insert(hdsDeviceLogEntity)
                .thenReturn(device)
                .doOnSuccess(savedDevice -> log.info("Device log saved successfully for deviceId: {}", device.getDeviceId()))
                .doOnError(e -> log.error("Failed to save device log for deviceId: {}", device.getDeviceId(), e));
    }

    /**
     * 处理设备初始化
     */
    private Mono<DeviceInitResp> processDeviceInit(HdsDeviceEntity deviceBaseResp) {
        // 获取rtc信息
        return Mono.zip(getRtcTokenInfo(deviceBaseResp.getRtcUserId()),
                        checkAndGetHotel(deviceBaseResp.getHotelCode()))
                .map(tuple -> {
                    RtcCallbackInfoVO rtcInfo = tuple.getT1();
                    HdsHotelInfoEntity hotelInfo = tuple.getT2();

                    return DeviceInitResp.builder()
                            .rtcToken(rtcInfo.getToken())
                            .rtcAppId(rtcInfo.getRtcAppId())
                            .rtcManufacturer("volcengine")
                            .serverCallBackUrl(rtcInfo.getCallBackUrl())
                            .secret(rtcInfo.getSecret())
                            .callTimeOutSeconds(SystemConstant.THIRTY)
                            .hotelCode(hotelInfo.getHotelCode())
                            .hotelName(hotelInfo.getHotelName())
                            .sosSwitch(hotelInfo.getSosSwitch())
                            .dialogSwitch(hotelInfo.getDialogSwitch())
                            .callCommand(hotelInfo.getCallCommand())
                            .deviceId(deviceBaseResp.getDeviceId())
                            .deviceType(deviceBaseResp.getDeviceAppType())
                            .userId(deviceBaseResp.getRtcUserId())
//                            .positionFullName(DeviceUtils.getPositionFullName(room))
                            .build();
                });
    }

    public Mono<RtcCallbackInfoVO> getRtcTokenInfo(String rtcRoomId) {
        return rtcHttpHelper.getRtcCallbackInfo(rtcRoomId).switchIfEmpty(Mono.error(new BusinessException("AUTH-DEVICE-INVALID", "RTC初始化失败")));
    }

    public Mono<HotelRoomListVo> getHotelInfo(GetRoomsReq req) {
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getHotelCode()) || StringUtils.isNotBlank(req.getHotelName()), "hotel must not be blank");
        Criteria criteria = Criteria.empty();
        if (StringUtils.isNotBlank(req.getHotelCode())) {
            criteria = criteria.and(HdsHotelInfoFieldEnum.hotel_code.name()).is(req.getHotelCode());
        }
        if (StringUtils.isNotBlank(req.getHotelName())) {
            criteria = criteria.and(HdsHotelInfoFieldEnum.hotel_name.name()).is(req.getHotelName());
        }
        Query limitOne = Query.query(criteria).limit(1).sort(Sort.by(Sort.Direction.DESC, HdsHotelInfoFieldEnum.id.name()));
        return r2dbcEntityTemplate.selectOne(limitOne, HdsHotelInfoEntity.class).flatMap(hotelInfoEntity -> {
            HotelRoomListVo hotelRoomListVo = new HotelRoomListVo();
            BeanUtils.copyProperties(hotelInfoEntity, hotelRoomListVo);
            return Mono.just(hotelRoomListVo);
        }).switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "酒店编码不正确，请重新输入")));

    }

    public Mono<Boolean> cleanDeviceTickets(String conversationId, String hotelCode, String positionCode,Integer targetStatus) {
        String logContext = String.format("hotelCode=%s, positionCode=%s, conversationId=%s", hotelCode, positionCode, conversationId);
        log.info("cleanDeviceTickets : {}", logContext);
        if (StringUtils.isNotBlank(conversationId)) {
            return executeTicketCleanup(null, null, conversationId, logContext,targetStatus);
        } else {
            Preconditions.checkArgument(StringUtils.isNotBlank(hotelCode), "hotelCode不能为空");
            Preconditions.checkArgument(StringUtils.isNotBlank(positionCode), "positionCode不能为空");
            return executeTicketCleanup(hotelCode, positionCode, null, logContext, targetStatus);
        }
    }

    private Mono<Boolean> executeTicketCleanup(String hotelCode, String positionCode, String conversationId, String logContext, Integer targetStatus) {
        return Mono.zip(
                        hdsServiceTicketDao.selectNotExpired(hotelCode, positionCode, conversationId,targetStatus),
                        hdsServiceTicketDao.updateDeviceTicketExpired(hotelCode, positionCode, conversationId,targetStatus)
                )
                .flatMap(tuple -> {
                    Set<String> ticketNos = tuple.getT1();
                    if (ticketNos.isEmpty()) {
                        log.info("cleanDeviceTickets No active tickets found to expire for {}", logContext);
                        return Mono.just(Boolean.TRUE);
                    }

                    log.info("cleanDeviceTickets Found {} tickets to expire for {}", ticketNos.size(), logContext);
                    return HeaderUtils.getHeaderInfo().flatMap(headerInfo -> serviceTicketService.insertTicketLog(TicketActionTypeEnum.EXPIRE.getCode(), ticketNos,headerInfo.getUserId(),headerInfo.getName()))
                            .doOnSuccess(ignored -> sendRefreshCommand(ticketNos))
                            .thenReturn(Boolean.TRUE);
                })
                .doOnError(e -> log.error("cleanDeviceTickets Failed to clean tickets for {}", logContext, e))
                .onErrorResume(e -> {
                    log.error("cleanDeviceTickets Error occurred during ticket cleanup for {} ", logContext, e);
                    return Mono.just(Boolean.FALSE);
                });
    }

    private void sendRefreshCommand(Set<String> ticketNos) {
        HdsTicketQO hdsTicketQO = HdsTicketQO.builder()
                .ticketNos(ticketNos)
                .hasEndOfCall(true)
                .build();

        callBackBizService.sendTicketCommandRefresh(hdsTicketQO, false)
                .subscribeOn(Schedulers.boundedElastic())
                .doOnSuccess(aVoid -> log.info("Successfully sent ticket refresh command for {} tickets", ticketNos.size()))
                .doOnError(error -> log.error("Failed to send ticket refresh command for tickets: {}", ticketNos, error))
                .subscribe();
    }

    public Mono<DeviceRtcInfoResp> getRtcDeviceInfo(String deviceId, String clientType) {
        Preconditions.checkArgument(StringUtils.isNotBlank(deviceId), "deviceId must not be blank");

        return determineDeviceMonoStrategy(deviceId, clientType).doOnNext(deviceBaseResp -> log.info("deviceId:{},clientType:{},getRtcDeviceInfo: {}", deviceId,clientType,JacksonUtils.writeValueAsString(deviceBaseResp)))
                .switchIfEmpty(Mono.error(new BusinessException("AUTH-DEVICE-INVALID", "无效的设备码")))
                .flatMap(this::validateAndBuildDeviceInfo);
    }

    private Mono<DeviceRtcInfoResp> validateAndBuildDeviceInfo(HdsDeviceEntity deviceBaseResp) {
        // 设备必须是在线状态
        if (ObjectUtil.notEqual(deviceBaseResp.getDeviceStatus(), DeviceStatusEnum.ONLINE.getCode())) {
            return Mono.error(new BusinessException("AUTH-DEVICE-OFFLINE", "设备不在线"));
        }
        if(ObjectUtil.equal(deviceBaseResp.getDeviceAppType(),DeviceTypeEnum.FRONT_APP.getCode())){
            return Mono.just(buildDeviceRtcNullPosition(deviceBaseResp));
        }
        if (ObjectUtil.isNull(deviceBaseResp.getPositionCode())) {
            return Mono.error(new BusinessException("AUTH-DEVICE-INVALID", "设备归属位置为空"));
        }
        return hdsDevicePositionDao.getOnePosition(deviceBaseResp.getPositionCode())
                .switchIfEmpty(Mono.error(new BusinessException("AUTH-DEVICE-INVALID", "设备归属位置不存在")))
                .flatMap(position -> Mono.just(buildDeviceRtcVo(deviceBaseResp, position)));
    }

    private Mono<HdsDeviceEntity> determineDeviceMonoStrategy(String deviceId, String clientType) {
        ClientType clientTypeEnum = ClientType.getByCode(NumberUtil.parseInt(clientType, 0));
        if (Objects.equals(clientTypeEnum, ClientType.MINI_PROGRAM)) {
            return getWechatDevice(deviceId);
        } else {
            return hdsDeviceDao.findOne(HdsDeviceQO.builder().deviceId(deviceId).build())
                    .switchIfEmpty(getBdwAppDeviceInfo(deviceId));
        }
    }

    public Mono<HdsDeviceEntity> getBdwAppDeviceInfo(String deviceId) {
        return Mono.zip(userDao.findOne(getUserReq(deviceId)).switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "用户不存在"))),
                        userLoginDeviceRecordDao.findOne(getUserLoginDeviceRecordQO(deviceId)).switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "用户登录设备记录不存在"))))
                .map(
                        tuple -> {
                            UserEntity userEntity = tuple.getT1();
                            UserLoginDeviceRecord userLoginDeviceRecord = tuple.getT2();
                            log.info("bdwAppDeviceInfo userEntity={}, userLoginDeviceRecord={}"
                                    , JacksonUtils.writeValueAsString(userEntity), JacksonUtils.writeValueAsString(userLoginDeviceRecord));
                            HdsDeviceEntity deviceEntity = new HdsDeviceEntity();
                            deviceEntity.setDeviceId(deviceId);
                            deviceEntity.setDeviceAppType(DeviceTypeEnum.BDW_APP.getCode());
                            deviceEntity.setDeviceStatus(DeviceStatusEnum.ONLINE.getCode());
                            deviceEntity.setHotelCode(userLoginDeviceRecord.getHotelCode());
                            deviceEntity.setPositionCode(userLoginDeviceRecord.getPositionCode());
                            deviceEntity.setUserId(userEntity.getUserId());
                            deviceEntity.setUserName(userEntity.getNickname());
                            return deviceEntity;
                        }
                );


    }

    @NotNull
    private static UserLoginDeviceRecordQO getUserLoginDeviceRecordQO(String deviceId) {
        UserLoginDeviceRecordQO userLoginDeviceRecordQO = new UserLoginDeviceRecordQO();
        userLoginDeviceRecordQO.setUserId(deviceId);
        userLoginDeviceRecordQO.setDeviceType(DeviceTypeEnum.BDW_APP.getCode());
        return userLoginDeviceRecordQO;
    }

    @NotNull
    private static UserReq getUserReq(String deviceId) {
        UserReq userReq = new UserReq();
        userReq.setUserId(deviceId);
        userReq.setStatus(UserStatus.active);
        return userReq;
    }

    private DeviceRtcInfoResp buildDeviceRtcNullPosition(HdsDeviceEntity deviceBaseResp) {
        String rtcUserId = DeviceInitRtcUtil.getRtcUserId(deviceBaseResp.getDeviceAppType(), deviceBaseResp.getDeviceId());
        return DeviceRtcInfoResp.builder()
                .rtcManufacturer("volcengine")// 默认先写死成火山引擎
                .hotelCode(deviceBaseResp.getHotelCode())
                .positionCode("null")
                .deviceId(deviceBaseResp.getDeviceId())
                .deviceType(deviceBaseResp.getDeviceAppType())
                .rtcUserId(rtcUserId)
                .deviceStatus(deviceBaseResp.getDeviceStatus())
                .account(deviceBaseResp.getUserId())
                .username(deviceBaseResp.getUserName())
                .build();
    }
    private DeviceRtcInfoResp buildDeviceRtcVo(HdsDeviceEntity deviceBaseResp, HdsDevicePositionEntity position) {
        String rtcUserId = DeviceInitRtcUtil.getRtcUserId(deviceBaseResp.getDeviceAppType(), deviceBaseResp.getDeviceId());
        return DeviceRtcInfoResp.builder()
                            .rtcManufacturer("volcengine")// 默认先写死成火山引擎
                            .hotelCode(deviceBaseResp.getHotelCode())
                            .positionCode(deviceBaseResp.getPositionCode())
                            .deviceId(deviceBaseResp.getDeviceId())
                            .deviceType(deviceBaseResp.getDeviceAppType())
                            .rtcUserId(rtcUserId)
                            .deviceStatus(deviceBaseResp.getDeviceStatus())
                            .account(deviceBaseResp.getUserId())
                            .username(deviceBaseResp.getUserName())
                            .positionFullName(DeviceUtils.getPositionFullName(position))
                            .build();
    }

    private Mono<HdsDeviceEntity> getWechatDevice(String deviceId) {
        Criteria criteria = Criteria.where(HdsChatUserFieldEnum.account.name()).is(deviceId)
                .and(HdsChatUserFieldEnum.status.name()).is(1);

        Query limitOne = Query.query(criteria).limit(1).sort(Sort.by(Sort.Direction.DESC, HdsChatUserFieldEnum.id.name()));
        return r2dbcEntityTemplate.selectOne(limitOne, HdsChatUserEntity.class)
                .map(this::buildHdsDeviceEntity);

    }
    private HdsDeviceEntity buildHdsDeviceEntity(HdsChatUserEntity userEntity) {
        HdsDeviceEntity deviceEntity = new HdsDeviceEntity();
        deviceEntity.setDeviceId(userEntity.getAccount());
        deviceEntity.setDeviceAppType(DeviceTypeEnum.WECHAT_MINI_APP.getCode());
        deviceEntity.setDeviceStatus(DeviceStatusEnum.ONLINE.getCode());
        deviceEntity.setHotelCode(userEntity.getHotelCode());
        deviceEntity.setPositionCode(userEntity.getPositionCode());
        deviceEntity.setUserId(userEntity.getId().toString());
        deviceEntity.setUserName(userEntity.getNickName());
        return deviceEntity;

    }

    public Mono<DevicePositionVo> getPositionDetails(GetHotelPositionReq req) {
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getDeviceId()) || StringUtils.isNotBlank(req.getPositionCode()), "device or position cannot all be empty");
        return Mono.zip(checkAndGetHotel(req.getHotelCode()), checkAndGetRoomOrDevice(req))
                .flatMap(tuple -> {
                    HdsHotelInfoEntity hotelInfo = tuple.getT1();
                    DevicePositionVo deviceBaseResp = tuple.getT2();
                    deviceBaseResp.setHotelCode(hotelInfo.getHotelCode());
                    deviceBaseResp.setHotelName(hotelInfo.getHotelName());
                    deviceBaseResp.setHotelPhone(StringUtils.isEmpty(hotelInfo.getFrontPhone()) ? hotelInfo.getPhone() : hotelInfo.getFrontPhone());
                    deviceBaseResp.setCurrentTime(LocalDateTime.now());
                    return Mono.just(deviceBaseResp);
                });
    }

    public Mono<List<DeviceRtcInfoResp>> getAvailableDevices(SearchDeviceReq deviceQo) {
        HdsDeviceQO qo = new HdsDeviceQO();
        BeanUtils.copyProperties(deviceQo,qo);
        qo.setDeviceAppType(ObjectUtil.equal(deviceQo.getDeviceType(),DeviceTypeEnum.FRONT.getCode()) ? Lists.newArrayList(DeviceTypeEnum.FRONT.getCode(),DeviceTypeEnum.FRONT_APP.getCode()) : Lists.newArrayList(deviceQo.getDeviceType()));
        if (StringUtils.isNotBlank(deviceQo.getPositionCode())){
            qo.setPositionCodes(Lists.newArrayList(deviceQo.getPositionCode()));
        }
        qo.setDeviceStatus(DeviceStatusEnum.ONLINE.getCode());
        return hdsDeviceDao.findList(qo)
                .flatMap(deviceList-> {
                    if (CollectionUtil.isEmpty(deviceList)){
                        return Mono.just(Lists.newArrayList());
                    }
                    Set<String> positionCodes = deviceList.stream().map(HdsDeviceEntity::getPositionCode).collect(Collectors.toSet());
                    return hdsDevicePositionDao.getPositionList(positionCodes).flatMap(positionList->{
                        Map<String, HdsDevicePositionEntity> positionMap = positionList.stream().collect(Collectors.toMap(HdsDevicePositionEntity::getPositionCode, item -> item));
                        return Mono.just(deviceList.stream().map(deviceBaseResp -> buildDeviceRtcVo(deviceBaseResp, positionMap.get(deviceBaseResp.getPositionCode()))).collect(Collectors.toList()));
                    }).flatMap(list -> {
                        log.info("getAvailableDevices deviceList:{}",JacksonUtils.writeValueAsString(list));
                        if (deviceQo.getDeviceType() == null || ObjectUtil.equal(deviceQo.getDeviceType(),DeviceTypeEnum.ROOM.getCode())){
                            return Mono.just(list);
                        }
                        // 前台设备需要分派逻辑
                        Set<String> employeeIds = list.stream().map(DeviceRtcInfoResp::getAccount).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                        EmployeeInfoQo infoQo = EmployeeInfoQo.builder()
                                .hotelCode(deviceQo.getHotelCode())
                                .employeeIdList(employeeIds)
                                .employeeType(deviceQo.getEmployeeType())
                                .ticketCategory(deviceQo.getTicketCategory()).build();
                        return hdsEmployeeTicketMappingDao.findAvailableEmployee(infoQo)
                                .flatMap(employees->{
                                    Map<String,HdsEmployeeTicketMappingEntity> employeeMap = employees.stream().collect(Collectors.toMap(HdsEmployeeTicketMappingEntity::getEmployeeId, item -> item));
                                    log.info("getAvailableDevices employeeSet:{}",JacksonUtils.writeValueAsString(employeeMap));
                                    return Mono.just( list.stream().filter(ele -> ObjectUtil.contains(employeeMap.keySet(),ele.getAccount()))
                                            .peek(ele-> ele.setEmployeeType(ele.getEmployeeType()))
                                            .toList());
                                });
                    });
                });
    }

    public Mono<Boolean> bindDeviceUserInfo(BindDeviceUserInfoReq req) {
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getDeviceId()), "deviceId must not be blank");


        return hdsDeviceDao.findOne(HdsDeviceQO.builder().deviceId(req.getDeviceId()).build())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "device is not exist")))
                .flatMap(entity -> {

                    String rtcUserId = DeviceInitRtcUtil.getRtcUserId(entity.getDeviceAppType(), entity.getDeviceId());

                    UpdateDeviceQo build = UpdateDeviceQo.builder().userId(req.getUserId()).userName(req.getUserName())
                            .rtcUserId(rtcUserId).deviceId(req.getDeviceId()).build();

                    return hdsDeviceDao.updateByDeviceId(build)
                            .doOnSuccess(aVoid -> log.info("bind user device success {}", JacksonUtils.writeValueAsString(req)))
                            .doOnError(e -> log.error("bind user device error {}", JacksonUtils.writeValueAsString(req), e))
                            .then(Mono.just(true));

                });

    }


    public Mono<List<RoomSimpleVo>> getRoomSimpleVos(GetRoomsReq getRoomsReq) {
        Preconditions.checkArgument(ObjectUtil.isNotEmpty(getRoomsReq.getHotelCode()), "酒店编码不能为空");
        return Mono.zip(hdsDevicePositionDao.getHdsDevicePositions(getRoomsReq.getHotelCode(), getRoomsReq.getPositionKey(), DeviceTypeEnum.ROOM.getCode()),HeaderUtils.getHeaderInfo())
                .flatMap(tuple2 -> {
                    List<HdsDevicePositionEntity> hdsDevicePositionEntities = tuple2.getT1();
                    String language = tuple2.getT2().getLanguage();

                    if (CollUtil.isEmpty(hdsDevicePositionEntities)) {
                        return Mono.just(Collections.<RoomSimpleVo>emptyList()); // 返回空列表，而不是空的Mono
                    }
                    List<String> positionCodes = hdsDevicePositionEntities
                            .stream()
                            .map(HdsDevicePositionEntity::getPositionCode)
                            .collect(Collectors.toList());
                   return hdsDeviceDao.findList(HdsDeviceQO.builder().positionCodes(positionCodes).build())
                            .map(hdsDeviceEntities -> {

                                Map<String, HdsDeviceEntity> positionCode2HdsDevice = hdsDeviceEntities
                                        .stream()
                                        .collect(Collectors.toMap(HdsDeviceEntity::getPositionCode, Function.identity(), (e1, e2) -> e1));
                                return hdsDevicePositionEntities.stream().map(e -> {
                                    RoomSimpleVo roomSimpleVo = new RoomSimpleVo();
                                    BeanUtils.copyProperties(e, roomSimpleVo);
                                    HdsDeviceEntity deviceEntity = positionCode2HdsDevice.get(e.getPositionCode());
                                    roomSimpleVo.setDeviceId(Optional.ofNullable(deviceEntity)
                                            .map(HdsDeviceEntity::getDeviceId)
                                            .orElse(null));
                                    roomSimpleVo.setDeviceUnavailabilityReason(ticketUtils.getDeviceExceptionTipsTranslate(
                                            Optional.ofNullable(deviceEntity)
                                                    .map(HdsDeviceEntity::getDeviceStatus)
                                                    .orElse(DeviceExceptionTipsEnum.EMPTY.getCode()),language)
                                    );
                                    roomSimpleVo.setPositionFullName(DeviceUtils.getPositionFullName(e));
                                    return roomSimpleVo;
                                }).collect(Collectors.toList());
                            });
                })
                .doOnSuccess(roomSimpleVos -> log.info("getRoomSimpleVos success {}", JacksonUtils.writeValueAsString(getRoomsReq)))
                .doOnError(e -> log.error("getRoomSimpleVos error {}", JacksonUtils.writeValueAsString(getRoomsReq), e));
    }



    public Mono<List<PositionSearchVo>> searchRoomsByHotel(GetRoomsReq req, String deviceType) {
        // 查询酒店和所有房间
        return Mono.zip(hdsDevicePositionDao.getHdsDevicePositions(req.getHotelCode(), req.getPositionKey(), deviceType),hdsDeviceDao.getBindPositionCodes(req.getHotelCode()))
                .flatMap(tuple -> {
                    List<HdsDevicePositionEntity> allPositions = tuple.getT1();
                    Set<String> alreadyBinds = tuple.getT2();
                    List<HdsDevicePositionEntity> notBindPositions = allPositions.stream().filter(ele -> !alreadyBinds.contains(ele.getPositionCode())).toList();
                    List<PositionSearchVo> res = new ArrayList<>();
                    for (HdsDevicePositionEntity notBindPosition : notBindPositions) {
                        PositionSearchVo vo = new PositionSearchVo();
                        vo.setHotelCode(notBindPosition.getHotelCode());
                        vo.setPositionCode(notBindPosition.getPositionCode());
                        vo.setPositionFullName(DeviceUtils.getPositionFullName(notBindPosition));
                        res.add(vo);
                    }
                    return Mono.just(res);
                });
    }

    public Mono<Boolean> deviceHeartAlive(DeviceHeartReq deviceHeartReq) {
        Preconditions.checkArgument(StringUtils.isNotBlank(deviceHeartReq.getDeviceId()), "deviceId must not be blank");
        UpdateDeviceQo build = UpdateDeviceQo.builder().deviceId(deviceHeartReq.getDeviceId())
                .deviceStatus(deviceHeartReq.isAlive() ? DeviceStatusEnum.ONLINE.getCode() : DeviceStatusEnum.OFFLINE.getCode())
                .build();
        return hdsDeviceDao.findOne(HdsDeviceQO.builder().deviceId(deviceHeartReq.getDeviceId()).build())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "device is not exist")))
                .flatMap(entity -> {
                    if (Objects.equals(entity.getDeviceStatus(), DeviceStatusEnum.PENDING_ACTIVATION.getCode())
                            || Objects.equals(entity.getDeviceStatus(), DeviceStatusEnum.DEACTIVATED.getCode())) {
                        return Mono.just(false);
                    }

                    Mono<Boolean> updateResult = hdsDeviceDao.updateByDeviceId(build)
                            .doOnSuccess(aVoid -> log.info("deviceHeartAlive success {}", JacksonUtils.writeValueAsString(deviceHeartReq)))
                            .doOnError(e -> log.error("deviceHeartAlive error {}", JacksonUtils.writeValueAsString(deviceHeartReq), e))
                            .then(Mono.just(true));

                    // 异步处理设备下线通知
                    if (!deviceHeartReq.isAlive() && StringUtils.isNotBlank(entity.getHotelCode())
                            && Objects.equals(entity.getDeviceStatus(), DeviceStatusEnum.ONLINE.getCode())) {
                        notifyDeviceOffline(entity.getHotelCode(), entity.getDeviceId());
                    }

                    return updateResult;
                });
    }

    private void notifyDeviceOffline(String hotelCode, String deviceId) {
        Mono.fromRunnable(() -> {
            log.info("设备状态从在线变为离线，准备发送下线通知: deviceId={}, hotelCode={}",
                    deviceId, hotelCode);
            sendDeviceOfflineMessage(hotelCode, deviceId)
                    .subscribeOn(Schedulers.boundedElastic())
                    .doOnError(e -> log.error("发送设备下线消息失败: {}", e.getMessage(), e))
                    .subscribe();
        }).subscribeOn(Schedulers.boundedElastic()).subscribe();
    }

    /**
     * 发送设备下线消息给酒店管理员
     *
     * @param hotelCode 酒店代码
     * @param deviceId 设备ID
     * @return 完成信号
     */
    private Mono<Void> sendDeviceOfflineMessage(String hotelCode, String deviceId) {
        return employeeService.getNotifyEmployees(hotelCode)
                .flatMap(userIds -> {
                    // 构建消息体
                    DeviceMonitorMessage message = new DeviceMonitorMessage();
                    message.setEvent("device_offline");
                    message.setUserIds(userIds);
                    message.setMessage(String.format("设备已下线,设备ID为:%s", deviceId));

                    MessageBody messageBody = new MessageBody("DEVICE_MONITOR", String.valueOf(SystemClock.now()), message);
                    log.info("发送设备下线消息: userIds={}, message={}", userIds, JacksonUtils.writeValueAsString(messageBody));
                    return reactiveMessageSender.sendMessage(SystemConstant.SSE_EVENT_TOPIC, messageBody);
                })
                .then();
    }

    public Mono<Boolean> deviceLogOut(String deviceId) {
        // 客房
        return hdsDeviceDao.findOne(HdsDeviceQO.builder().deviceId(deviceId).build())
                .flatMap(hdsDeviceEntity -> {
                    if (hdsDeviceEntity == null){
                        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "设备不存在"));
                    }
                    String rtcUserId = DeviceInitRtcUtil.getRtcUserId(hdsDeviceEntity.getDeviceAppType(), hdsDeviceEntity.getDeviceId());
                    String redisKey = String.format(RedisConstant.USER_DETAIL_INFO_KEY, rtcUserId);
                    String userId = hdsDeviceEntity.getUserId();

                    // 删除登录设备的用户token
                    Mono<Void> deleteTokensMono = deleteUserTokens(userId)
                            .doOnError(e -> log.error("删除用户缓存异常 {}: {}", userId, e.getMessage()));

                    Mono<Void> deleteRedisKeyMono = deleteRedisKey(redisKey)
                            .doOnError(e -> log.error("删除设备缓存异常 {}: {}", redisKey, e.getMessage()));

                    return Mono.when(deleteTokensMono, deleteRedisKeyMono)
                            .thenReturn(true);
                });
    }

    /**
     * 发送设备RTC状态变更消息
     *
     * @param deviceId 设备ID
     * @param isEnable  true为启用，false为停用
     * @return 完成信号
     */
    public Mono<Boolean> sendDeviceRtcMessage(String deviceId, String isEnable) {
        if (StringUtils.isBlank(deviceId)) {
            log.warn("设备ID为空，跳过发送RTC状态变更消息");
            return Mono.empty();
        }

        // 构建消息体
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("event", StringUtils.equals(isEnable, "true") ? "device_rtc_enable" : "device_rtc_disable");
        dataMap.put("message", String.format("设备RTC已%s，共1个设备",
                StringUtils.equals(isEnable, "true") ? "启用" : "停用"));
        dataMap.put("deviceIds", deviceId);

        // 创建消息体
        MessageBody messageBody = new MessageBody(
                "DEVICE_RTC_STATUS_CHANGE",
                String.valueOf(SystemClock.now()),
                dataMap
        );

        rtcHelper.sendUnicastTextMessageAsync(deviceId, JacksonUtils.writeValueAsString(messageBody))
                .subscribeOn(Schedulers.boundedElastic())
                .doOnSuccess(result -> log.info("发送rtc消息成功: userId={}", deviceId))
                .doOnError(error -> log.error("发送rtc消息失败: userId={}, error={}",
                        deviceId, error.getMessage(), error))
                .subscribe();
        return Mono.just(true);
    }

    private Mono<Void> deleteRedisKey(String redisKey) {
        return reactiveStringRedisTemplate.opsForValue().delete(redisKey)
                .doOnError(e -> log.error("删除redis缓存异常 {}: {}", redisKey, e.getMessage()))
                .onErrorMap(e -> new BusinessException("REDIS-ERROR-001", "redis服务访问失败"))
                .then();
    }

    private Mono<Void> deleteUserTokens(String userId) {
        String source = SourcePlatform.HOTEL.getCode();
        String userTokenKey = TokenKeyUtils.getUserTokenKey(source, userId);
        return reactiveStringRedisTemplate.opsForValue().get(userTokenKey)
                .flatMap(tokenPair -> {
                    if (StringUtils.isBlank(tokenPair)) {
                        return reactiveStringRedisTemplate.opsForValue().delete(userTokenKey).then();
                    }
                    TokenPairDTO dto = JacksonUtils.readValue(tokenPair, TokenPairDTO.class);
                    String refreshKey = TokenKeyUtils.getRefreshTokenKey(source, dto.getRefreshToken());
                    String accessKey = TokenKeyUtils.getTokenKey(source, dto.getAccessToken());
                    return Mono.when(
                            reactiveStringRedisTemplate.delete(refreshKey),
                            reactiveStringRedisTemplate.delete(accessKey),
                            reactiveStringRedisTemplate.delete(userTokenKey)
                    ).then();
                })
                .doOnError(e -> log.error("删除用户redis缓存异常{}: {}", userId, e.getMessage()));
    }

    public Mono<String> getRoomCode(GetRoomsReq getRoomsReq) {
        Preconditions.checkArgument(ObjectUtil.isNotEmpty(getRoomsReq.getHotelCode()), "酒店编码不能为空");
        String redisKey = String.format(RedisConstant.HOTEL_ROOM_CODE_KEY, getRoomsReq.getHotelCode());
        return reactiveStringRedisTemplate.opsForValue().get(redisKey)
                .filter(StringUtils::isNotBlank)
                .doOnNext(cache -> log.info("{} getRoomCode from redis success {}", getRoomsReq.getHotelCode(), cache))
                .switchIfEmpty(
                        hdsDevicePositionDao.getHdsDevicePositions(getRoomsReq.getHotelCode(), null, DeviceTypeEnum.ROOM.getCode())
                                .flatMap(hdsDevicePositionEntities -> {
                                    if (CollUtil.isEmpty(hdsDevicePositionEntities)) {
                                        return Mono.error(new BusinessException(ResultCode.NOT_FOUND, "未找到房间信息"));
                                    }
                                    String result = hdsDevicePositionEntities.stream()
                                            .map(e -> String.join("|",
                                                    ObjectUtil.defaultIfNull(e.getBlock(), ""),
                                                    ObjectUtil.defaultIfNull(e.getArea(), ""),
                                                    ObjectUtil.defaultIfNull(e.getPositionName(), "")
                                            ))
                                            .collect(Collectors.joining(","));
                                    if (StringUtils.isBlank(result)) {
                                        return Mono.error(new BusinessException(ResultCode.NOT_FOUND, "未找到有效房间信息"));
                                    }
                                    // 设置2小时过期
                                    return reactiveStringRedisTemplate.opsForValue()
                                            .set(redisKey, result, Duration.ofHours(2))
                                            .thenReturn(result);
                                })
                )
                .doOnSuccess(roomCode -> log.info("{} getRoomCode success {}", getRoomsReq.getHotelCode(), roomCode))
                .doOnError(e -> log.error("{} getRoomCode error", getRoomsReq.getHotelCode(), e));
    }

    public Mono<DeviceInitResp> chatInit(ChatInitReq req) {
        log.info("chatInit request: {}", JacksonUtils.writeValueAsString(req));
        // 参数验证
        validateChatInitRequest(req);
        return HeaderUtils.getHeaderInfo()
                .doOnNext(headerInfo -> log.info("请求头信息: headerInfo={}", JacksonUtils.writeValueAsString(headerInfo)))
                .flatMap(headerInfo -> initializeChat(req, headerInfo))
                .flatMap(resp-> saveOrderRoomCache(req).subscribeOn(Schedulers.boundedElastic()).thenReturn(resp))
                .doOnSuccess(response -> log.info("chatInit success for userId: {}", JacksonUtils.writeValueAsString(response)))
                .doOnError(e -> log.error("chatInit failed for request: {}", JacksonUtils.writeValueAsString(req), e));
    }

    public Mono<Boolean> saveOrderRoomCache(ChatInitReq req){
        if(StringUtils.isBlank(req.getBdwHotelCode()) || StringUtils.isBlank(req.getChildOrderNo())) {
            return Mono.just(true);
        }

        HotelPositionSimpleDto hotelPositionSimpleDto = new HotelPositionSimpleDto();
        hotelPositionSimpleDto.setHotelCode(req.getHotelCode());
        hotelPositionSimpleDto.setPositionCode(req.getPositionCode());
        return orderRoomRedisDao.save(req.getBdwHotelCode(),req.getChildOrderNo(),hotelPositionSimpleDto);
    }



    /**
     * 验证聊天初始化请求参数
     */
    private void validateChatInitRequest(ChatInitReq req) {
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getHotelCode()), "酒店代码不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getPositionCode()), "位置代码不能为空");
    }

    /**
     * 初始化聊天会话
     */
    private Mono<DeviceInitResp> initializeChat(ChatInitReq req, HeaderUtils.HeaderInfo headerInfo) {
        String userId = headerInfo.getUserId();
        String rtcUserId = DeviceInitRtcUtil.getRtcUserId(DeviceTypeEnum.BDW_APP.getCode(), userId);
        String language = headerInfo.getLanguage();

        log.info("生成RTC用户ID: userId={}, rtcUserId={}", userId, rtcUserId);

        return Mono.zip(getRtcTokenInfo(rtcUserId),
                        checkAndGetHotel(req.getHotelCode()),
                        checkAndGetRoom(req.getHotelCode(), req.getPositionCode()))
                .map(tuple -> buildDeviceInitResponse(req, userId, rtcUserId, tuple.getT1(), tuple.getT2(), tuple.getT3(),language))
                .flatMap(resp -> {
                    UserLoginDeviceRecord userLoginRecord = new UserLoginDeviceRecord();
                    userLoginRecord.setUserId(userId);
                    userLoginRecord.setHotelCode(req.getHotelCode());
                    userLoginRecord.setPositionCode(req.getPositionCode());
                    userLoginRecord.setDeviceType(DeviceTypeEnum.BDW_APP.getCode());
                    return userLoginDeviceRecordDao.save(userLoginRecord).thenReturn(resp);
                });
    }

    /**
     * 构建设备初始化响应
     */
    private DeviceInitResp buildDeviceInitResponse(ChatInitReq req, String userId, String rtcUserId,
                                                   RtcCallbackInfoVO rtcInfo, HdsHotelInfoEntity hotelInfo,
                                                   HdsDevicePositionEntity room, String language) {
        return DeviceInitResp.builder()
                .rtcToken(rtcInfo.getToken())
                .rtcManufacturer("volcengine")
                .rtcAppId(rtcInfo.getRtcAppId())
                .serverCallBackUrl(rtcInfo.getCallBackUrl())
                .secret(rtcInfo.getSecret())
                .callTimeOutSeconds(SystemConstant.THIRTY)
                .hotelCode(hotelInfo.getHotelCode())
                .hotelName(hotelInfo.getHotelName())
                .dialogSwitch(hotelInfo.getDialogSwitch())
                .callCommand(hotelInfo.getCallCommand())
                .sosSwitch(hotelInfo.getSosSwitch())
                .positionCode(req.getPositionCode())
                .deviceId(userId)
                .deviceType(DeviceTypeEnum.BDW_APP.getCode())
                .userId(rtcUserId)
                .rtcUserId(rtcUserId)
                .positionName(room.getPositionName())
                .positionFullName(DeviceUtils.getPositionFullName(room))
                .botCode(wechatProperties.getChatBot( language))
                .build();
    }
}

