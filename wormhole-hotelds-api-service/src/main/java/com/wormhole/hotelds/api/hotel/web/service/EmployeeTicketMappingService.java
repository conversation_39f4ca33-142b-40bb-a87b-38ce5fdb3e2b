package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wormhole.common.config.WormholeTranslationConfig;
import com.wormhole.common.config.WormholeUrlConfig;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.config.TicketProperties;
import com.wormhole.hotelds.api.hotel.constant.SystemConstant;
import com.wormhole.hotelds.api.hotel.resp.BlankResp;
import com.wormhole.hotelds.api.hotel.util.TicketUtils;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDeviceDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDevicePositionDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsEmployeeTicketMappingDao;
import com.wormhole.hotelds.api.hotel.constant.YesNoEnum;
import com.wormhole.hotelds.api.hotel.web.model.req.TicketCommandMessage;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsDevicePositionQO;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsDeviceQO;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsEmployeeTicketMappingQO;
import com.wormhole.hotelds.api.hotel.req.UpdateReceiveStatusReq;
import com.wormhole.hotelds.api.hotel.web.model.req.EmployeeSingleReq;
import com.wormhole.hotelds.api.hotel.web.model.req.GetStaffRosterReq;
import com.wormhole.hotelds.api.hotel.web.model.req.SaveReceiveConfigReq;
import com.wormhole.hotelds.api.hotel.web.model.res.BlankNode;
import com.wormhole.hotelds.api.hotel.web.model.res.ReceiveTicketBlankResp;
import com.wormhole.hotelds.api.hotel.web.model.res.StaffRosterResp;
import com.wormhole.hotelds.api.hotel.web.dao.repository.EmployeeTicketMappingRepository;
import com.wormhole.hotelds.core.enums.*;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.core.utils.HoteldsApiUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.wormhole.common.result.ResultCode.INTERNAL_SERVER_ERROR;
import static com.wormhole.hotelds.core.enums.DeviceTypeEnum.FRONT;
import static com.wormhole.hotelds.core.enums.DeviceTypeEnum.FRONT_APP;

/**
 * <AUTHOR>
 * @date 2025/4/8
 */
@Service
@Slf4j
public class EmployeeTicketMappingService {

    @Autowired
    private EmployeeTicketMappingRepository employeeTicketMappingRepository;

    @Autowired
    private HdsEmployeeTicketMappingDao hdsEmployeeTicketMappingDao;
    @Autowired
    private  EmployeeTicketMappingService employeeTicketMappingService;
    @Autowired
    private HdsDeviceDao hdsDeviceDao;
    @Autowired
    private RtcService rtcService;

    @Autowired
    private TicketProperties ticketProperties;

    @Autowired
    private HdsDevicePositionDao hdsDevicePositionDao;

    @Autowired
    private WormholeTranslationConfig translationConfig;

    @Autowired
    private TicketUtils ticketUtils;

    public Mono<Boolean> updateReceiveStatus(UpdateReceiveStatusReq req) {
        Preconditions.checkArgument(Objects.nonNull(req.getAcceptTicketStatus()), "修改状态不能为空");

        return HeaderUtils.getHeaderInfo()
                .flatMap(header -> employeeTicketMappingRepository.findByEmployeeIdAndHotelCode(header.getUserId(),header.getHotelCode())
                        .switchIfEmpty(Mono.error(new BusinessException(INTERNAL_SERVER_ERROR, "账号不存在")))
                        .flatMap(account -> {
                            account.setAcceptTicketStatus(req.getAcceptTicketStatus());
                            return employeeTicketMappingRepository.save(account)
//                                    .doOnSuccess(savedAccount ->
//                                            refreshAccountTicket(savedAccount.getHotelCode(), AccountTypeEnum.MAIN_SWITCHBOARD)
//                                                    .subscribeOn(Schedulers.boundedElastic())
//                                                    .subscribe())
                                    .thenReturn(true);
                        })
                )
                .doOnSuccess(e -> log.info("updateReceiveStatus, update success " ))
                .doOnError(e -> log.error("updateReceiveStatus, update error " , e))
                .onErrorResume(error -> Mono.error((new BusinessException(ResultCode.INVALID_PARAMETER, "系统繁忙，暂不可使用"))));
    }

    public Mono<Void> refreshAccountTicket(String hotelCode, AccountTypeEnum accountTypeEnum) {

        HdsEmployeeTicketMappingQO build = HdsEmployeeTicketMappingQO
                .builder()
                .accountTypeEnum(accountTypeEnum)
                .acceptTicketStatus(AcceptTicketStatusEnum.ALLOW_ACCEPT.getCode())
                .hotelCode(hotelCode)
                .build();

        return employeeTicketMappingService.findOnlineEmployeeTicketMapping(build).flatMap(mappingEntities -> {
            if (CollUtil.isEmpty(mappingEntities)) {
                return Mono.empty();
            }
            Set<String> accountIds = mappingEntities
                    .stream()
                    .map(HdsEmployeeTicketMappingEntity::getEmployeeId)
                    .collect(Collectors.toSet());

            HdsDeviceQO hdsDeviceQO = HdsDeviceQO.builder()
                    .hotelCode(hotelCode)
                    .userIds(accountIds)
                    .deviceAppType(Lists.newArrayList(FRONT.getCode(), FRONT_APP.getCode()))
                    .build();
            return hdsDeviceDao.findList(hdsDeviceQO).flatMap(e -> {
                Set<String> rtcUserIds = e.stream().map(HdsDeviceEntity::getRtcUserId).collect(Collectors.toSet());
                TicketCommandMessage ticketCommandMessage = TicketCommandMessage
                        .builder()
//                        .refresh(true)
                        .hasSound(false)
                        .refreshIntervalSeconds(ticketProperties.getListRefreshSecond())
                        .build();
                return rtcService.sendTicketCommandMessage(rtcUserIds, ticketCommandMessage);
            });

        });
    }


    public Mono<List<String>> showTicketRtcUserIds(HdsServiceTicketEntity ticketEntity) {
        return hdsDevicePositionDao.findOne(HdsDevicePositionQO.builder().positionCode(ticketEntity.getPositionCode()).build()).flatMap(positionToArea -> {
            HdsEmployeeTicketMappingQO build = HdsEmployeeTicketMappingQO
                    .builder()
                    .hotelCode(ticketEntity.getHotelCode())
                    .serviceCategory(ticketEntity.getServiceCategory())
                    .areaCode(positionToArea.getAreaCode())
                    .areaPublic(ObjectUtil.equal(positionToArea.getArea(), SystemConstant.PUBLIC_AREA_NAME))
                    .acceptTicketStatus(AcceptTicketStatusEnum.ALLOW_ACCEPT.getCode())
                    .build();
            return findOnlineEmployeeTicketMapping(build);
        })
                .flatMap(mappings -> {
                    if (CollUtil.isEmpty(mappings)) {
                        log.info("showTicketRtcUserIds, employeeTicketMap is empty,getTicketNo {}", ticketEntity.getTicketNo());
                        return Mono.just(Collections.emptyList());
                    }
                    // 获取所有符合条件的账号ID
                    List<String> accountIds = mappings.stream()
                            .map(HdsEmployeeTicketMappingEntity::getEmployeeId)
                            .collect(Collectors.toList());

                    HdsDeviceQO hdsDeviceQO = HdsDeviceQO.builder()
                            .deviceStatus(1)
                            .deviceAppType(Lists.newArrayList(FRONT.getCode(), FRONT_APP.getCode()))
                            .hotelCode(ticketEntity.getHotelCode())
                            .userIds(accountIds)
                            .build();

                    return hdsDeviceDao.findList(hdsDeviceQO)
                            .map(devices -> {
                                if (CollUtil.isEmpty(devices)) {
                                    log.info("showTicketRtcUserIds, device is empty,qo:{}", JacksonUtils.writeValueAsString(hdsDeviceQO));
                                    return Collections.emptyList();
                                }

                                // 收集RTC用户ID并发送通知
                                List<String> rtcUserIds = devices.stream()
                                        .map(HdsDeviceEntity::getRtcUserId)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toList());
                                log.info("showTicketRtcUserIds,ticketEntity{} rtcUserIds:{}", JacksonUtils.writeValueAsString(ticketEntity),JacksonUtils.writeValueAsString(rtcUserIds));
                                return rtcUserIds;

                            });
                });
    }


    public Mono<List<HdsEmployeeTicketMappingEntity>> findOnlineEmployeeTicketMapping(HdsEmployeeTicketMappingQO qo) {
        return hdsEmployeeTicketMappingDao.findList(qo).flatMap(mappings -> {
            if (CollUtil.isEmpty(mappings)) {
                log.error("findOnlineEmployeeTicketMapping, employeeTicketMap is empty,qo:{}", JacksonUtils.writeValueAsString(qo));
                return Mono.just(Collections.emptyList());
            }
            // 获取所有符合条件的账号ID
            List<String> accountIds = mappings.stream()
                    .map(HdsEmployeeTicketMappingEntity::getEmployeeId)
                    .collect(Collectors.toList());
            log.info("findOnlineEmployeeTicketMapping,accountIds:{}", JacksonUtils.writeValueAsString(accountIds));
            HdsDeviceQO hdsDeviceQO = HdsDeviceQO
                    .builder()
                    .deviceStatus(DeviceStatusEnum.ONLINE.getCode())
                    .deviceAppType(Lists.newArrayList(FRONT.getCode(), FRONT_APP.getCode()))
                    .hotelCode(qo.getHotelCode())
                    .userIds(accountIds)
                    .build();

            return hdsDeviceDao.findList(hdsDeviceQO)
                    .map(devices -> {
                        if (CollUtil.isEmpty(devices)) {
                            log.error("findOnlineEmployeeTicketMapping, device is empty,qo:{}", JacksonUtils.writeValueAsString(hdsDeviceQO));
                            return Collections.emptyList();
                        }

                        List<String> userIds = devices.stream()
                                .map(HdsDeviceEntity::getUserId)
                                .filter(Objects::nonNull)
                                .toList();
                        return mappings.stream().filter(mapping -> userIds.contains(mapping.getEmployeeId())).collect(Collectors.toList());

                    });
        });
    }

    public Mono<ReceiveTicketBlankResp> receiveTicketBlanks(EmployeeSingleReq req) {
        // 查询 hds_device_position 表的楼栋+楼层
        HdsDevicePositionQO qo = HdsDevicePositionQO
                .builder()
                .hotelCode(req.getHotelCode())
                .deviceAppType(DeviceTypeEnum.ROOM.getCode())
                .build();
        return Mono.zip(hdsDevicePositionDao.findList(qo),
                        hdsEmployeeTicketMappingDao.findOne(HdsEmployeeTicketMappingQO.builder().employId(req.getUserId()).hotelCode(req.getHotelCode()).build()).defaultIfEmpty(new HdsEmployeeTicketMappingEntity()),
                HeaderUtils.getHeaderInfo()
        ).flatMap(tuple -> {
            List<HdsDevicePositionEntity> positions = tuple.getT1();
            HdsEmployeeTicketMappingEntity mapping = tuple.getT2();
            String language = tuple.getT3().getLanguage();
            ReceiveTicketBlankResp resp = ReceiveTicketBlankResp.builder()
                            .categoryTitle(translationConfig.translateSync(WormholeTranslationConfig.TranslationKeys.TICKET_TYPE,language))
                            .categoryDesc(translationConfig.translateSync(WormholeTranslationConfig.TranslationKeys.SELECT_TICKET_TYPE_HINT,language))
                            .positionTitle(translationConfig.translateSync(WormholeTranslationConfig.TranslationKeys.SERVICE_AREA,language))
                            .positionDesc(translationConfig.translateSync(WormholeTranslationConfig.TranslationKeys.SELECT_SERVICE_AREA_HINT,language))
                            .build();
                    List<BlankNode> ticketBlanks = getTicketBlanks(mapping,language);
                    resp.setCategoryBlanks(ticketBlanks);
                    if (CollUtil.isNotEmpty( positions)){
                        List<BlankNode> positionBlanks = getPositionBlanks(positions, mapping.getAreaCodes());
                        resp.setPositionBlanks(positionBlanks);
                    }
                    return Mono.just(resp);
                }).doOnError(e -> log.error("Error occurred while retrieving ticket blanks: {}", e.getMessage(),e));
    }

    private List<BlankNode> getPositionBlanks(List<HdsDevicePositionEntity> positions, String areaCodes) {
        List<BlankNode> result = new ArrayList<>();
        Set<String> areaCodeSet = Sets.newHashSet(Arrays.asList(Optional.ofNullable(areaCodes).orElse(StringUtils.EMPTY).split(",")));
        // 楼栋、楼层分组
        Map<String, List<HdsDevicePositionEntity>> blockMap = positions.stream().collect(Collectors.groupingBy(
                HdsDevicePositionEntity::getBlock,
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(HdsDevicePositionEntity::getBlockAreaSortOrder, Comparator.nullsLast(Integer::compareTo))
                                        .thenComparing(HdsDevicePositionEntity::getArea,Comparator.nullsLast(String::compareTo))
                        )),
                        ArrayList::new
                )
        ));
        // 构建结果
        for (Map.Entry<String, List<HdsDevicePositionEntity>> blockEntry : blockMap.entrySet()) {
            String block = blockEntry.getKey();
            List<HdsDevicePositionEntity> areaList = blockEntry.getValue();
            BlankNode blockNode = new BlankNode();
            blockNode.setKey(block);
            blockNode.setName(block); // 假设名称和编码相同，可根据实际情况修改
            blockNode.setIsLeaf(YesNoEnum.NO.getCode());
            // 排序
            blockNode.setSort(CollUtil.isEmpty(areaList) ? 999 : areaList.get(0).getBlockAreaSortOrder());
            List<BlankNode> areaNodes = new ArrayList<>();
            boolean blockChecked = false;
            for (HdsDevicePositionEntity pos : areaList) {
                BlankNode areaNode = new BlankNode();
                areaNode.setKey(pos.getAreaCode());
                areaNode.setName(pos.getArea());
                areaNode.setIsLeaf(YesNoEnum.YES.getCode());
                areaNode.setChildren(Collections.emptyList()); // 叶子节点没有子节点
                boolean areaChecked = ObjectUtil.contains(areaCodeSet, pos.getAreaCode());
                blockChecked |= areaChecked;
                areaNode.setChecked(areaChecked ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
                areaNodes.add(areaNode);
            }
            blockNode.setChecked(blockChecked ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
            blockNode.setChildren(areaNodes);
            result.add(blockNode);
        }
        return result.stream().sorted(Comparator.comparing(BlankNode::getSort, Comparator.nullsLast(Integer::compareTo))
                .thenComparing(BlankNode::getName,Comparator.nullsLast(String::compareTo))).collect(Collectors.toList());
    }

    private List<BlankNode> getTicketBlanks(HdsEmployeeTicketMappingEntity mapping, String language) {
        Set<String> picked = mapping == null|| StringUtils.isBlank(mapping.getTicketCategories()) ? Sets.newHashSet() : Sets.newHashSet(mapping.getTicketCategories().split(","));
        List<BlankNode> result = new ArrayList<>();
        for (int i = 0; i < ticketProperties.getReceiveTicketList().size(); i++) {
            String category = ticketProperties.getReceiveTicketList().get(i);
            String desc = ticketProperties.getTicketDescList().get(i);
            ServiceCategory byCode = ServiceCategory.getByCode(category, false);
            BlankNode build = BlankNode.builder()
                    .key(byCode.getCode())
                    .name(translationConfig.translateSync(byCode.getCode(),language))
                    .desc(translationConfig.translateSync(desc, language))
                    .iconUrl(ticketUtils.getCategoryIcon(WormholeUrlConfig.UrlKeys.RECEIVE_TICKET,byCode.getCode()))
                    .isLeaf(YesNoEnum.YES.getCode())
                    .checked(picked.contains(byCode.getCode()) ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode())
                    .build();
            result.add(build);
        }
        return result;
    }

    public Mono<Boolean> saveReceiveTicketConfig(SaveReceiveConfigReq req, EmployeeSingleReq employeeSingleReq) {
        // 检查入参
        Preconditions.checkArgument(CollUtil.isNotEmpty(req.getTicketCategories()) || CollUtil.isNotEmpty(req.getAreaCodes()), "工单类型或者服务区域不能为空");
        return employeeTicketMappingRepository.findByEmployeeIdAndHotelCode(employeeSingleReq.getUserId(),employeeSingleReq.getHotelCode())
                .switchIfEmpty(Mono.error(new BusinessException(INTERNAL_SERVER_ERROR, "账号不存在")))
                .flatMap(account -> {
                    if (ObjectUtil.notEqual(account.getEmployeeType(),AccountTypeEnum.EXTENSION.getCode())){
                        return Mono.error(new BusinessException(INTERNAL_SERVER_ERROR, "账号不能设置接单范围"));
                    }
                    account.setTicketCategories(String.join(",", req.getTicketCategories()));
                    account.setAreaCodes(String.join(",", req.getAreaCodes()));
                    return employeeTicketMappingRepository.save(account)
                            .doOnSuccess(savedAccount ->
                                    refreshAccountTicket(savedAccount.getHotelCode(), AccountTypeEnum.EXTENSION)
                                            .subscribeOn(Schedulers.boundedElastic())
                                            .subscribe())
                            .thenReturn(true);
                })
                .doOnSuccess(e -> log.info("saveReceiveTicketConfig, update success " ))
                .doOnError(e -> log.error("saveReceiveTicketConfig, update error " , e));
    }

    public Mono<List<String>> assignTicketBlanks(String hotelCode) {
        return hdsDevicePositionDao.findList(HdsDevicePositionQO.builder()
                .hotelCode(hotelCode)
                .build())
                .map(positions -> positions.stream().sorted(Comparator.comparing(HdsDevicePositionEntity::getBlockAreaSortOrder, Comparator.nullsLast(Integer::compareTo))
                        .thenComparing(HdsDevicePositionEntity::getBlock, Comparator.nullsLast(String::compareTo)))
                        .map(HdsDevicePositionEntity::getBlock).distinct()
                        .collect(Collectors.toList()));
    }

    public Mono<StaffRosterResp> getStaffRoster(GetStaffRosterReq req, String hotelCode) {
        HdsDevicePositionQO qo = HdsDevicePositionQO
                .builder()
                .hotelCode(hotelCode)
                .block(req.getBlock())
                .deviceAppType(DeviceTypeEnum.ROOM.getCode())
                .build();
        HdsEmployeeTicketMappingQO employeeQo = HdsEmployeeTicketMappingQO
                .builder()
                .hotelCode(hotelCode)
                .accountTypeEnum(AccountTypeEnum.EXTENSION)
                .acceptTicketStatus(AcceptTicketStatusEnum.ALLOW_ACCEPT.getCode())
                .build();
        return Mono.zip(hdsDevicePositionDao.findList(qo),hdsEmployeeTicketMappingDao.findList(employeeQo)
                        .flatMap(mappings -> {
                            if (CollUtil.isEmpty(mappings)) {
                                return Mono.just(new ArrayList<HdsEmployeeTicketMappingEntity>());
                            }
                            Set<String> employeeIds = mappings.stream().map(HdsEmployeeTicketMappingEntity::getEmployeeId).collect(Collectors.toSet());
                            // 过滤离线设备
                            return hdsDeviceDao.findList(HdsDeviceQO.builder().userIds(employeeIds).build())
                                    .map(devices -> {
                                        Map<String, Set<String>> userHotelOnlineMap = devices.stream().collect(Collectors.groupingBy(HdsDeviceEntity::getUserId, Collectors.mapping(
                                                HdsDeviceEntity::getHotelCode,
                                                Collectors.toSet()
                                        )));
                                        return mappings.stream().filter(mapping -> userHotelOnlineMap.get(mapping.getEmployeeId())!= null && userHotelOnlineMap.get(mapping.getEmployeeId()).contains(mapping.getHotelCode())).collect(Collectors.toList());
                                    });
                })
            ,HeaderUtils.getHeaderInfo())
                .map(tuple -> {
                    // 获取员工
                    List<HdsEmployeeTicketMappingEntity> employees = tuple.getT2();
                    Map<String,List<HdsEmployeeTicketMappingEntity>> employeeMap = new HashMap<>();
                    String language = tuple.getT3().getLanguage();
                    for (HdsEmployeeTicketMappingEntity employee : employees) {
                        // 每个员工设置area-category
                        String[] currentAreaCode = Optional.ofNullable(employee.getAreaCodes()).orElse(StringUtils.EMPTY).split(",");
                        if (currentAreaCode.length == 0){
                            continue;
                        }
                        String[] currentCategories = Optional.ofNullable(employee.getTicketCategories()).orElse(StringUtils.EMPTY).split(",");
                        for (String area : currentAreaCode) {
                            for (String category : currentCategories) {
                                String key = area + "_" + category;
                                employeeMap.computeIfAbsent(key, k -> new ArrayList<>()).add(employee);
                            }
                        }
                    }
                    // 获取区域
                    List<HdsDevicePositionEntity> positions = tuple.getT1();
                    Set<String> areaCodeSet = new HashSet<>();
                    StaffRosterResp resp = new StaffRosterResp();
                    List<StaffRosterResp.RowDetail> rowDetails = new ArrayList<>();
                    AtomicInteger emptyNum = new AtomicInteger();
                    positions.stream().sorted(Comparator.comparing(HdsDevicePositionEntity::getBlockAreaSortOrder, Comparator.nullsLast(Integer::compareTo))
                            .thenComparing(HdsDevicePositionEntity::getBlock,Comparator.nullsLast(String::compareTo))).forEach(ele->{
                        if (!areaCodeSet.contains(ele.getAreaCode())){
                            StaffRosterResp.RowDetail rowDetail = new StaffRosterResp.RowDetail();
                            rowDetail.setCode(ele.getAreaCode());
                            rowDetail.setName(ele.getArea());
                            areaCodeSet.add(ele.getAreaCode());
                            List<StaffRosterResp.RowDetail.TicketStaffDetail> ticketStaffDetails = new ArrayList<>();
                            for (String category : ticketProperties.getReceiveTicketList()) {
                                String key = ele.getAreaCode() + "_" + category;
                                StaffRosterResp.RowDetail.TicketStaffDetail  ticketStaffDetail = new StaffRosterResp.RowDetail.TicketStaffDetail();
                                ticketStaffDetail.setTicketCategory(category);
                                ticketStaffDetail.setTicketName(ServiceCategory.getByCode(category,false).getDisplayText());
                                if (CollUtil.isEmpty(employeeMap.get(key))){
                                    ticketStaffDetail.setEmployeeNames(Lists.newArrayList());
                                    ticketStaffDetail.setEmptyFlag(true);
                                    emptyNum.getAndIncrement();
                                } else {
                                    ticketStaffDetail.setEmployeeNames(employeeMap.get(key).stream().map(HdsEmployeeTicketMappingEntity::getEmployeeName).collect(Collectors.toList()));
                                    ticketStaffDetail.setEmptyFlag(false);
                                }
                                ticketStaffDetails.add(ticketStaffDetail);
                            }
                            rowDetail.setTicketDetails(ticketStaffDetails);
                            rowDetails.add(rowDetail);
                        }
                    });
                    resp.setEmptyNum(emptyNum.get());
                    resp.setRowDetails(rowDetails);
                    resp.setColumnTitles(ticketProperties.getReceiveTicketList().stream().map(category -> BlankResp.builder().code( category).name(translationConfig.translateSync(category,language)).build()).collect(Collectors.toList()));
                    return resp;
                });

    }

    public Mono<Boolean> showConfigEntry(String hotelCode, String userId) {
        HdsEmployeeTicketMappingQO employeeQo = HdsEmployeeTicketMappingQO
                .builder()
                .hotelCode(hotelCode)
                .employId(userId)
                .accountTypeEnum(AccountTypeEnum.EXTENSION)
                .build();
        return hdsEmployeeTicketMappingDao.findOne(employeeQo)
                .switchIfEmpty(Mono.just(new HdsEmployeeTicketMappingEntity()))
                .map(employee -> {
                    if (employee == null || employee.getId() == null) {
                        return false;
                    }
                    return  StringUtils.isBlank(employee.getTicketCategories()) || StringUtils.isBlank(employee.getAreaCodes());
                });
    }

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    public Mono<Boolean> initAreaCodes() {

        return hdsDevicePositionDao.findList(HdsDevicePositionQO.builder().build())
                .flatMapMany(Flux::fromIterable)
                .flatMap(position -> {
                        String areaCode = HoteldsApiUtils.generateAreaCode(position.getHotelCode(),position.getBlock(),position.getArea());
                        position.setAreaCode(areaCode);
                        return r2dbcEntityTemplate.update(position).thenReturn(true);
                }).then(Mono.just(true));
    }
}
