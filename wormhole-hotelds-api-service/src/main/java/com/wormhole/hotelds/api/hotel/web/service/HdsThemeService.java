package com.wormhole.hotelds.api.hotel.web.service;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.hotelds.api.hotel.config.HdsThemeProperties;
import com.wormhole.hotelds.api.hotel.web.dao.HdsHotelInfoDao;
import com.wormhole.hotelds.api.hotel.web.model.res.ThemeResp;
import com.wormhole.hotelds.core.model.entity.HdsHotelInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 主题服务类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class HdsThemeService {

    @Autowired
    private HdsThemeProperties themeProperties;

    @Autowired
    private HdsHotelInfoDao hdsHotelInfoDao;

    /**
     * 初始化主题资源
     *
     * @param hotelCode 酒店代码（可选）
     * @return 主题初始化数据
     */
    public Mono<ThemeResp> initTheme(String hotelCode) {
        log.info("Init theme with hotelCode: {}", hotelCode);

        // 如果hotelCode为空 直接返回默认资源
        if (StringUtils.isBlank(hotelCode)) {
            return Mono.just(createThemeResponse(null));
        }
        // 查询酒店自定义资源
        return hdsHotelInfoDao.queryByHotelCode(hotelCode)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "酒店为空")))
                .map(this::createThemeResponse)
                .defaultIfEmpty(createThemeResponse(null));
    }

    /**
     * 创建主题响应数据
     *
     * @param hotelInfo 酒店信息（可为 null）
     * @return 主题响应数据
     */
    private ThemeResp createThemeResponse(HdsHotelInfoEntity hotelInfo) {
        String defaultLogo = themeProperties.getDefaultLogo();
        String defaultHotelLogo = themeProperties.getDefaultHotelLogo();
        String defaultBanner = themeProperties.getDefaultBanner();

        ThemeResp resp = new ThemeResp();

        ThemeResp.ThemeResources resources = new ThemeResp.ThemeResources();
        resources.setDefaultLogo(defaultLogo);
        resources.setDefaultBanner(defaultBanner);
        // 判断是否有酒店自定义 logo
        String hotelLogo = (hotelInfo != null && StringUtils.isNotBlank(hotelInfo.getHotelLogo()))
                ? hotelInfo.getHotelLogo()
                : defaultHotelLogo;
        resources.setHotelLogo(hotelLogo);

        resp.setResources(resources);
        resp.setTheme(new ThemeResp.ThemeInfo());
        resp.setFeatures(new ThemeResp.ThemeFeatures());

        return resp;
    }
}