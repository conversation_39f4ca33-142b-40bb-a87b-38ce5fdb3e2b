package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.util.StrUtil;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.constant.SystemConstant;
import com.wormhole.hotelds.api.hotel.web.dao.HdsHotelDailyAiStatisticsDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsServiceTicketDao;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsTicketQO;
import com.wormhole.hotelds.api.hotel.web.model.req.ServiceSatisfactionTrendReq;
import com.wormhole.hotelds.api.hotel.web.model.req.TicketsTrendReq;
import com.wormhole.hotelds.api.hotel.web.model.res.ServiceSatisfactionTrendVO;
import com.wormhole.hotelds.api.hotel.web.model.res.TicketsTrendVO;
import com.wormhole.hotelds.core.enums.*;
import com.wormhole.hotelds.core.model.entity.HdsHotelDailyAiStatisticsEntity;
import com.wormhole.hotelds.core.model.entity.HdsLivedRtcCallInfo;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketEntity;
import com.wormhole.hotelds.core.model.entity.HdsLivedRtcCallInfoFieldEnum;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Flux;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.time.DayOfWeek;
import java.time.temporal.IsoFields;


/**
 * 酒店每日AI统计服务
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@Service
public class HotelDailyAiStatisticsService {

    @Autowired
    private HdsHotelDailyAiStatisticsDao statisticsDao;

    @Autowired
    private HdsServiceTicketDao serviceTicketDao;

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    /**
     * 生成指定日期的统计数据
     * 数据查询范围：昨天14:00到今天14:00（不超过）
     */
    public Mono<Void> generateDailyStatistics(LocalDate businessDate) {
        log.info("开始生成{}的酒店AI统计数据", businessDate);
        // 修改时间范围：昨天14:00到今天14:00（不超过）
        LocalDateTime startTime = businessDate.atTime(14, 0);
        LocalDateTime endTime = businessDate.plusDays(1).atTime(14, 0);

        // 1. 统计工单数据
        // 2. 统计通话数据
        // 3. 统计OTA差评数据
        // 4. 统计使用房间数据
        return generateTicketStatistics(startTime, endTime, businessDate)
            .then(generateCallStatistics(startTime, endTime, businessDate))
            .then(generateOtaCommentStatistics(businessDate, businessDate, businessDate))
            .doOnSuccess(v -> log.info("{}的酒店AI统计数据生成完成", businessDate))
            .doOnError(e -> log.error("生成{}的酒店AI统计数据失败", businessDate, e));
    }

    private Mono<Void> generateTicketStatistics(LocalDateTime startTime, LocalDateTime endTime, LocalDate businessDate) {
        HdsTicketQO ticketQO = HdsTicketQO.builder()
            .start(startTime)
            .end(endTime)
            .build();

        Map<String, List<HdsServiceTicketEntity>> hotelTicketsMap = new ConcurrentHashMap<>();
        AtomicInteger page = new AtomicInteger(0);

        return fetchAndAggregate(ticketQO, page, hotelTicketsMap)
            .then(Mono.defer(() -> {
                return Flux.fromIterable(hotelTicketsMap.entrySet())
                    .concatMap(entry -> {
                        String hotelCode = entry.getKey();
                        List<HdsServiceTicketEntity> hotelTickets = entry.getValue();
                        log.info("[工单统计] 查找统计数据: hotelCode={}, businessDate={}", hotelCode, businessDate);
                        return statisticsDao.findByHotelCodeAndDate(hotelCode, businessDate)
                            .doOnNext(stat -> log.info("[工单统计] 查到统计数据: {}", JacksonUtils.writeValueAsString(stat)))
                            .flatMap(existingStat -> {
                                log.info("[工单统计] 进入 update 逻辑: hotelCode={}, businessDate={}", hotelCode, businessDate);
                                updateTicketStatistics(existingStat, hotelTickets);
                                return statisticsDao.update(existingStat)
                                    .doOnSuccess(s -> log.info("[工单统计] update 完成: hotelCode={}, businessDate={}", hotelCode, businessDate));
                            })
                            .switchIfEmpty(Mono.defer(() -> {
                                log.info("[工单统计] 未查到，准备 insert: hotelCode={}, businessDate={}", hotelCode, businessDate);
                                HdsHotelDailyAiStatisticsEntity newStat = createTicketStatistics(hotelCode, businessDate, hotelTickets);
                                return statisticsDao.save(newStat)
                                    .doOnSuccess(s -> log.info("[工单统计] insert 完成: hotelCode={}, businessDate={}", hotelCode, businessDate));
                            }));
                    })
                    .then();
            }));
    }

    private Mono<Void> fetchAndAggregate(HdsTicketQO ticketQO, AtomicInteger page, Map<String, List<HdsServiceTicketEntity>> hotelTicketsMap) {
        return serviceTicketDao.findListPaged(ticketQO, page.get(), SystemConstant.PAGE_SIZE_1000)
            .collectList()
            .flatMap(tickets -> {
                if (tickets.isEmpty()) {
                    return Mono.empty();
                }
                // 累加到Map
                for (HdsServiceTicketEntity ticket : tickets) {
                    hotelTicketsMap.computeIfAbsent(ticket.getHotelCode(), k -> new ArrayList<>()).add(ticket);
                }
                page.incrementAndGet();
                return fetchAndAggregate(ticketQO, page, hotelTicketsMap);
            });
    }

    /**
     * 生成通话统计数据
     */
    private Mono<Void> generateCallStatistics(LocalDateTime startTime, LocalDateTime endTime, LocalDate businessDate) {
        Criteria criteria = Criteria.where(HdsLivedRtcCallInfoFieldEnum.created_at.name()).greaterThanOrEquals(startTime)
            .and(HdsLivedRtcCallInfoFieldEnum.created_at.name()).lessThan(endTime)
            .and(HdsLivedRtcCallInfoFieldEnum.call_status.name()).is(5) // 已结束的通话
            .and(HdsLivedRtcCallInfoFieldEnum.row_status.name()).is(1);

        return r2dbcEntityTemplate.select(Query.query(criteria), HdsLivedRtcCallInfo.class)
            .collectList()
            .flatMap(calls -> {
                // 过滤掉 hotelCode 为空的数据
                List<HdsLivedRtcCallInfo> filteredCalls = calls.stream()
                    .filter(call -> StringUtil.isNotBlank(call.getHotelCode()))
                    .toList();
                // 按酒店分组统计
                Map<String, List<HdsLivedRtcCallInfo>> hotelCallsMap = filteredCalls.stream()
                    .collect(Collectors.groupingBy(HdsLivedRtcCallInfo::getHotelCode));
                return Flux.fromIterable(hotelCallsMap.entrySet())
                    .concatMap(entry -> {
                        String hotelCode = entry.getKey();
                        List<HdsLivedRtcCallInfo> hotelCalls = entry.getValue();
                        log.info("[通话统计] 查找统计数据: hotelCode={}, businessDate={}", hotelCode, businessDate);
                        return statisticsDao.findByHotelCodeAndDate(hotelCode, businessDate)
                            .doOnNext(stat -> log.info("[通话统计] 查到统计数据: {}", JacksonUtils.writeValueAsString(stat)))
                            .flatMap(existingStat -> {
                                log.info("[通话统计] 进入 update 逻辑: hotelCode={}, businessDate={}", hotelCode, businessDate);
                                updateCallStatistics(existingStat, hotelCalls);
                                return statisticsDao.update(existingStat)
                                    .doOnSuccess(s -> log.info("[通话统计] update 完成: hotelCode={}, businessDate={}", hotelCode, businessDate));
                            })
                            .switchIfEmpty(Mono.defer(() -> {
                                log.info("[通话统计] 未查到，准备 insert: hotelCode={}, businessDate={}", hotelCode, businessDate);
                                HdsHotelDailyAiStatisticsEntity newStat = createCallStatistics(hotelCode, businessDate, hotelCalls);
                                return statisticsDao.save(newStat)
                                    .doOnSuccess(s -> log.info("[通话统计] insert 完成: hotelCode={}, businessDate={}", hotelCode, businessDate));
                            }));
                    })
                    .then();
            });
    }

    /**
     * 生成OTA差评统计数据
     */
    private Mono<Void> generateOtaCommentStatistics(LocalDate startDate, LocalDate endDate, LocalDate businessDate) {
        String sql = """
            SELECT hotel_code, COUNT(*) as negative_count 
            FROM wp_comment 
            WHERE comment_date >= ? AND comment_date <= ? 
            AND comment_score < 4 
            AND row_status = 1 
            GROUP BY hotel_code
            """;

        return r2dbcEntityTemplate.getDatabaseClient()
            .sql(sql)
            .bind(0, startDate)
            .bind(1, endDate)
            .map(row -> {
                String hotelCode = row.get("hotel_code", String.class);
                Integer negativeCount = row.get("negative_count", Integer.class);
                return Map.entry(hotelCode, negativeCount);
            })
            .all()
            .collectList()
            .flatMap(commentStats -> {
                // 过滤掉 hotelCode 为空的数据
                List<Map.Entry<String, Integer>> filteredStats = commentStats.stream()
                    .filter(entry -> StringUtil.isNotBlank(entry.getKey()))
                    .collect(Collectors.toList());
                return Flux.fromIterable(filteredStats)
                    .concatMap(entry -> {
                        String hotelCode = entry.getKey();
                        Integer negativeCount = entry.getValue();
                        log.info("[OTA差评统计] 查找统计数据: hotelCode={}, businessDate={}", hotelCode, businessDate);
                        return statisticsDao.findByHotelCodeAndDate(hotelCode, businessDate)
                            .doOnNext(stat -> log.info("[OTA差评统计] 查到统计数据: {}", JacksonUtils.writeValueAsString(stat)))
                            .flatMap(existingStat -> {
                                log.info("[OTA差评统计] 进入 update 逻辑: hotelCode={}, businessDate={}", hotelCode, businessDate);
                                existingStat.setOtaNegativeReviewCount(negativeCount);
                                return statisticsDao.update(existingStat)
                                    .doOnSuccess(s -> log.info("[OTA差评统计] update 完成: hotelCode={}, businessDate={}", hotelCode, businessDate));
                            })
                            .switchIfEmpty(Mono.defer(() -> {
                                log.info("[OTA差评统计] 未查到，准备 insert: hotelCode={}, businessDate={}", hotelCode, businessDate);
                                HdsHotelDailyAiStatisticsEntity newStat = createOtaCommentStatistics(hotelCode, businessDate, negativeCount);
                                return statisticsDao.save(newStat)
                                    .doOnSuccess(s -> log.info("[OTA差评统计] insert 完成: hotelCode={}, businessDate={}", hotelCode, businessDate));
                            }));
                    })
                    .then();
            });
    }

    /**
     * 更新工单统计数据
     */
    private void updateTicketStatistics(HdsHotelDailyAiStatisticsEntity stat, List<HdsServiceTicketEntity> tickets) {
        // 总工单数量
        stat.setTicketCount(tickets.size());
        
        // 已完成工单数量
        int completedCount = (int) tickets.stream()
            .filter(ticket -> TicketStatus.COMPLETED.getCode().equals(ticket.getStatus()))
            .count();
        stat.setCompletedTicketCount(completedCount);
        
        // 客诉工单数量
        int complaintCount = (int) tickets.stream()
            .filter(ticket -> ServiceCategory.COMPLAINT.getCode().equals(ticket.getServiceCategory()))
            .count();
        stat.setComplaintTicketCount(complaintCount);
        
        // 客诉预警工单数量 (投诉工单 + 回复异常工单)
        int complaintWarningCount = (int) tickets.stream()
            .filter(ticket -> ServiceCategory.COMPLAINT.getCode().equals(ticket.getServiceCategory()) ||
                             (ticket.getReplyExceptionType() != null && 
                              !ReplyExceptionEnum.NORMAL.getCode().equals(ticket.getReplyExceptionType())))
            .count();
        stat.setComplaintWarningTicketCount(complaintWarningCount);
        
        // 问询总数
        int inquiryCount = (int) tickets.stream()
            .filter(ticket -> ServiceType.INQUIRY.name().equals(ticket.getServiceType()))
            .count();
        stat.setInquiryCount(inquiryCount);
        
        // 问询已完成总数
        int inquiryCompletedCount = (int) tickets.stream()
            .filter(ticket -> ServiceType.INQUIRY.name().equals(ticket.getServiceType()) && 
                             TicketStatus.COMPLETED.getCode().equals(ticket.getStatus()))
            .count();
        stat.setInquiryCompletedCount(inquiryCompletedCount);
        
        // 客需工单总数
        int serviceNeedCount = (int) tickets.stream()
            .filter(ticket -> ServiceType.SERVICE.name().equals(ticket.getServiceType()))
            .count();
        stat.setServiceNeedCount(serviceNeedCount);
        
        // 客需工单已完成总数
        int serviceNeedCompletedCount = (int) tickets.stream()
            .filter(ticket -> ServiceType.SERVICE.name().equals(ticket.getServiceType()) && 
                             TicketStatus.COMPLETED.getCode().equals(ticket.getStatus()))
            .count();
        stat.setServiceNeedCompletedCount(serviceNeedCompletedCount);
        
        // 客诉工单已完成总数
        int complaintCompletedCount = (int) tickets.stream()
            .filter(ticket -> ServiceCategory.COMPLAINT.getCode().equals(ticket.getServiceCategory()) && 
                             TicketStatus.COMPLETED.getCode().equals(ticket.getStatus()))
            .count();
        stat.setComplaintCompletedCount(complaintCompletedCount);
        
        // 紧急事项总数
        int emergencyCount = (int) tickets.stream()
            .filter(ticket -> ServiceCategory.EMERGENCY.getCode().equals(ticket.getServiceCategory()))
            .count();
        stat.setEmergencyCount(emergencyCount);
        
        // 紧急事项工单已完成总数
        int emergencyCompletedCount = (int) tickets.stream()
            .filter(ticket -> ServiceCategory.EMERGENCY.getCode().equals(ticket.getServiceCategory()) && 
                             TicketStatus.COMPLETED.getCode().equals(ticket.getStatus()))
            .count();
        stat.setEmergencyCompletedCount(emergencyCompletedCount);

        // 使用房间数
        int roomUseCount = (int) tickets.stream()
            .map(HdsServiceTicketEntity::getPositionCode)
                .filter(StrUtil::isNotBlank).distinct()
            .count();
        stat.setRoomUseCount(roomUseCount);

        // 文字对话数：rtc_room_id为空或null的工单数
        int textDialogueCount = (int) tickets.stream()
            .filter(ticket -> Objects.equals(TicketStatus.COMPLETED.getCode(), ticket.getStatus()) &&
                    StrUtil.isBlank(ticket.getRtcRoomId()))
            .count();
        stat.setTextDialogueCount(textDialogueCount);

        // AI解决数：closed_loop_level为1或5的工单数
        int aiSolveCount = (int) tickets.stream()
            .filter(ticket -> Objects.equals(TicketStatus.COMPLETED.getCode(), ticket.getStatus()) &&
                    ticket.getClosedLoopLevel() != null &&
                             (Objects.equals(ticket.getClosedLoopLevel(), TicketClosedLoopLevel.L1.getCode()) ||
                              Objects.equals(ticket.getClosedLoopLevel(), TicketClosedLoopLevel.L5.getCode())) )
            .count();
        stat.setAiSolveCount(aiSolveCount);

        // 处理时长：已完成工单的平均处理时长（秒）
        OptionalDouble avgCompleteDuration = tickets.stream()
            .filter(ticket -> TicketStatus.COMPLETED.getCode().equals(ticket.getStatus()) &&
                             ticket.getCompletionTime() != null &&
                    !Objects.equals(ticket.getClosedLoopLevel(), TicketClosedLoopLevel.L1.getCode()) &&
                    !Objects.equals(ticket.getClosedLoopLevel(), TicketClosedLoopLevel.L4.getCode()))
            .mapToLong(ticket -> Duration.between(ticket.getCreatedAt(), ticket.getCompletionTime()).abs().getSeconds())
            .average();
        stat.setAvgCompleteDurationSeconds(avgCompleteDuration.isPresent() ?
                BigDecimal.valueOf(avgCompleteDuration.getAsDouble()).setScale(2, RoundingMode.HALF_UP).doubleValue() : 0d);


        // 有效处理时长工单数
        int validCompleteDurationTicketCount = (int) tickets.stream()
                .filter(ticket -> TicketStatus.COMPLETED.getCode().equals(ticket.getStatus()) &&
                        ticket.getCompletionTime() != null &&
                        !ticket.getClosedLoopLevel().equals(TicketClosedLoopLevel.L1.getCode()) &&
                        !ticket.getClosedLoopLevel().equals(TicketClosedLoopLevel.L4.getCode()))
                .count();
        stat.setValidCompleteDurationTicketCount(validCompleteDurationTicketCount);

        // 超时工单数：overdue_flag为1的工单数
        int overdueCount = (int) tickets.stream()
            .filter(ticket -> TicketStatus.COMPLETED.getCode().equals(ticket.getStatus()) &&
                    ticket.getOverdueFlag() != null && ticket.getOverdueFlag() == 1)
            .count();
        stat.setOverdueCount(overdueCount);

        // 人工回拨工单数：处理方式为 人工回电/客诉回电 的工单数
        int returnCallTicketCount = (int) tickets.stream()
            .filter(ticket ->  Objects.equals(TicketStatus.COMPLETED.getCode(), ticket.getStatus()) &&
                    (Objects.equals(ticket.getClosedLoopLevel(), TicketClosedLoopLevel.L2.getCode()) && !StrUtil.equals(ticket.getServiceCategory(), ServiceCategory.COMPLAINT.getCode()) )
                     || ((Objects.equals(ticket.getClosedLoopLevel(), TicketClosedLoopLevel.L3.getCode()) || StrUtil.equals(ticket.getServiceCategory(), ServiceCategory.COMPLAINT.getCode()) )))
            .count();
        stat.setReturnCallTicketCount(returnCallTicketCount);

        stat.setUpdatedAt(LocalDateTime.now());
        stat.setUpdatedBy("system");
        stat.setUpdatedByName("system");
    }

    /**
     * 创建工单统计记录
     */
    private HdsHotelDailyAiStatisticsEntity createTicketStatistics(String hotelCode, LocalDate businessDate, List<HdsServiceTicketEntity> tickets) {
        HdsHotelDailyAiStatisticsEntity stat = new HdsHotelDailyAiStatisticsEntity();
        stat.setBusinessDate(businessDate);
        stat.setHotelCode(hotelCode);
        stat.setCreatedAt(LocalDateTime.now());
        stat.setCreatedBy("system");
        stat.setCreatedByName("system");

        updateTicketStatistics(stat, tickets);
        
        return stat;
    }

    /**
     * 更新通话统计数据
     */
    private void updateCallStatistics(HdsHotelDailyAiStatisticsEntity stat, List<HdsLivedRtcCallInfo> calls) {
        // AI通话数量: initiator_type = 2 and receiver_type = 3
        int aiCallCount = (int) calls.stream()
            .filter(call -> Integer.valueOf(2).equals(call.getInitiatorType()) && Integer.valueOf(3).equals(call.getReceiverType()) &&
                            Objects.nonNull(call.getCallStartTime()) && Objects.nonNull(call.getCallEndTime()) &&
                            Duration.between(call.getCallStartTime(), call.getCallEndTime()).abs().getSeconds() > 0
                    )
            .count();
        stat.setAiCallCount(aiCallCount);
        
        // 回拨通话数量: initiator_type = 1 and receiver_type = 2
        int returnCallCount = (int) calls.stream()
            .filter(call -> Integer.valueOf(1).equals(call.getInitiatorType()) && 
                           Integer.valueOf(2).equals(call.getReceiverType()))
            .count();
        stat.setReturnCallCount(returnCallCount);

        // 平均通话时长（秒）
        OptionalDouble avgCallDuration = calls.stream()
            .filter(call -> Objects.nonNull(call.getCallStartTime()) && Objects.nonNull(call.getCallEndTime()) &&
                    Integer.valueOf(2).equals(call.getInitiatorType()) && Integer.valueOf(3).equals(call.getReceiverType()) &&
                    Duration.between(call.getCallStartTime(), call.getCallEndTime()).abs().getSeconds() > 0)
            .mapToLong(call -> Duration.between(call.getCallStartTime(), call.getCallEndTime()).abs().getSeconds())
            .average();
        stat.setAvgCallDurationSeconds(avgCallDuration.isPresent() ? avgCallDuration.getAsDouble() : 0.0d);
    }

    /**
     * 创建通话统计记录
     */
    private HdsHotelDailyAiStatisticsEntity createCallStatistics(String hotelCode, LocalDate businessDate, List<HdsLivedRtcCallInfo> calls) {
        HdsHotelDailyAiStatisticsEntity stat = new HdsHotelDailyAiStatisticsEntity();
        stat.setBusinessDate(businessDate);
        stat.setHotelCode(hotelCode);
        stat.setCreatedAt(LocalDateTime.now());
        stat.setCreatedBy("system");
        stat.setCreatedByName("system");
        updateCallStatistics(stat, calls);
        
        return stat;
    }

    /**
     * 创建OTA差评统计记录
     */
    private HdsHotelDailyAiStatisticsEntity createOtaCommentStatistics(String hotelCode, LocalDate businessDate, Integer negativeCount) {
        HdsHotelDailyAiStatisticsEntity stat = new HdsHotelDailyAiStatisticsEntity();
        stat.setBusinessDate(businessDate);
        stat.setHotelCode(hotelCode);
        stat.setOtaNegativeReviewCount(negativeCount);
        
        return stat;
    }

    public Flux<ServiceSatisfactionTrendVO> getServiceSatisfactionTrend(ServiceSatisfactionTrendReq req) {
        // 本周周一
        LocalDate thisWeekMonday = LocalDate.now().with(DayOfWeek.MONDAY);
        // 4周前的周一
        LocalDate startWeekMonday = thisWeekMonday.minusWeeks(4);
        // 上周的周日
        LocalDate endWeekSunday = thisWeekMonday.minusDays(1);

        String sql = "SELECT * FROM hds_hotel_daily_ai_statistics " +
            "WHERE hotel_code = :hotelCode " +
            "AND row_status = 1 " +
            "AND business_date >= :startDate " +
            "AND business_date <= :endDate " +
            "ORDER BY business_date ASC";

        return r2dbcEntityTemplate.getDatabaseClient()
            .sql(sql)
            .bind("hotelCode", req.getHotelCode())
            .bind("startDate", startWeekMonday)
            .bind("endDate", endWeekSunday)
            .map((row, meta) -> {
                ServiceSatisfactionTrendVO vo = new ServiceSatisfactionTrendVO();
                vo.setWeekStart(row.get("business_date", java.time.LocalDate.class)); // 临时，后面分组
                vo.setTotalTicketCount(row.get("ticket_count", Integer.class) == null ? 0 : row.get("ticket_count", Integer.class));
                vo.setComplaintTicketCount(row.get("complaint_ticket_count", Integer.class) == null ? 0 : row.get("complaint_ticket_count", Integer.class));
                vo.setWarningTicketCount(row.get("complaint_warning_ticket_count", Integer.class) == null ? 0 : row.get("complaint_warning_ticket_count", Integer.class));
                vo.setOtaNegativeCount(row.get("ota_negative_review_count", Integer.class) == null ? 0 : row.get("ota_negative_review_count", Integer.class));
                return vo;
            })
            .all()
            .collectList()
            .flatMapMany(list -> {
                // 按周分组聚合
                Map<LocalDate, List<ServiceSatisfactionTrendVO>> weekMap = list.stream()
                    .collect(Collectors.groupingBy(vo -> vo.getWeekStart().with(DayOfWeek.MONDAY)));
                // 只保留4个周（本周前的4个自然周）
                List<LocalDate> weekStartList = new ArrayList<>();
                for (int i = 0; i < 4; i++) {
                    weekStartList.add(thisWeekMonday.minusWeeks(4 - i));
                }
                return Flux.fromIterable(weekStartList)
                    .map(weekStart -> {
                        List<ServiceSatisfactionTrendVO> weekList = weekMap.getOrDefault(weekStart, new ArrayList<>());
                        ServiceSatisfactionTrendVO agg = new ServiceSatisfactionTrendVO();
                        agg.setWeekOfYear(weekStart.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR));
                        agg.setWeekStart(weekStart);
                        agg.setWeekEnd(weekStart.with(DayOfWeek.SUNDAY));
                        agg.setTotalTicketCount(weekList.stream().mapToInt(ServiceSatisfactionTrendVO::getTotalTicketCount).sum());
                        agg.setComplaintTicketCount(weekList.stream().mapToInt(ServiceSatisfactionTrendVO::getComplaintTicketCount).sum());
                        agg.setWarningTicketCount(weekList.stream().mapToInt(ServiceSatisfactionTrendVO::getWarningTicketCount).sum());
                        agg.setOtaNegativeCount(weekList.stream().mapToInt(ServiceSatisfactionTrendVO::getOtaNegativeCount).sum());
                        int total = agg.getTotalTicketCount();
//                        int unsatisfied = agg.getComplaintTicketCount() + agg.getWarningTicketCount() + agg.getOtaNegativeCount();
                        log.info("weekStart: {}, agg:{}", weekStart, JacksonUtils.writeValueAsString(agg));
                        agg.setSatisfactionRate(total == 0 ? 1.0 : Math.round((1.0 - (agg.getComplaintTicketCount() * 1.0 / total)) * 100.0) / 100.0);
                        return agg;
                    });
            });
    }

    public Flux<TicketsTrendVO> getTicketsTrend(TicketsTrendReq req) {
        String hotelCode = req.getHotelCode();
        LocalDate endDate;
        // 计算昨天的前7天
        if (LocalDateTime.now().getHour() < 14) {
            endDate = LocalDate.now().minusDays(2);
        } else {
            endDate = LocalDate.now().minusDays(1);
        }

        LocalDate startDate = endDate.minusDays(6);
        String sql = "SELECT business_date, ai_call_count, return_call_count, ticket_count, completed_ticket_count " +
                "FROM hds_hotel_daily_ai_statistics WHERE hotel_code = :hotelCode AND business_date >= :startDate AND business_date <= :endDate AND row_status = 1 ORDER BY business_date ASC";
        return r2dbcEntityTemplate.getDatabaseClient()
                .sql(sql)
                .bind("hotelCode", hotelCode)
                .bind("startDate", startDate)
                .bind("endDate", endDate)
                .map((row, meta) -> {
                    TicketsTrendVO vo = new TicketsTrendVO();
                    vo.setBusinessDate(row.get("business_date", LocalDate.class));
                    vo.setAiCallCount(row.get("ai_call_count", Integer.class) == null ? 0 : row.get("ai_call_count", Integer.class));
                    vo.setReturnCallCount(row.get("return_call_count", Integer.class) == null ? 0 : row.get("return_call_count", Integer.class));
                    vo.setTicketCount(row.get("ticket_count", Integer.class) == null ? 0 : row.get("ticket_count", Integer.class));
                    vo.setCompletedTicketCount(row.get("completed_ticket_count", Integer.class) == null ? 0 : row.get("completed_ticket_count", Integer.class));
                    return vo;
                })
                .all()
                .collectList()
                .flatMapMany(list -> {
                    // 转为Map，方便补全
                    Map<LocalDate, TicketsTrendVO> map = list.stream()
                        .collect(Collectors.toMap(TicketsTrendVO::getBusinessDate, v -> v));
                    List<TicketsTrendVO> result = new ArrayList<>();
                    for (int i = 0; i < 7; i++) {
                        LocalDate date = startDate.plusDays(i);
                        TicketsTrendVO vo = map.getOrDefault(date, null);
                        if (vo == null) {
                            vo = new TicketsTrendVO();
                            vo.setBusinessDate(date);
                            vo.setAiCallCount(0);
                            vo.setReturnCallCount(0);
                            vo.setTicketCount(0);
                            vo.setCompletedTicketCount(0);
                        }
                        result.add(vo);
                    }
                    return Flux.fromIterable(result);
                });
    }

}