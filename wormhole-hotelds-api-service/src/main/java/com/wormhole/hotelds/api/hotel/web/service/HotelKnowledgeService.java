package com.wormhole.hotelds.api.hotel.web.service;

import com.google.common.base.Preconditions;
import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.client.chat.client.AgentApiClient;
import com.wormhole.agent.client.chat.params.KnowledgeSearchReq;
import com.wormhole.agent.knowledge.model.dto.KnowledgeMessageDTO;
import com.wormhole.agent.knowledge.model.entity.KnowledgeBaseEntity;
import com.wormhole.agent.knowledge.params.KnowledgeSearchParams;
import com.wormhole.agent.knowledge.search.KnowledgeSearchContext;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.config.HotelKnowledgeProperties;
import com.wormhole.hotelds.api.hotel.req.BatchHotelKnowledgeReq;
import com.wormhole.hotelds.api.hotel.resp.HotelKnowledgeResp;
import com.wormhole.hotelds.api.hotel.web.model.entity.HdsHotelKbEntity;
import com.wormhole.hotelds.api.hotel.web.model.req.KbSearchReq;
import com.wormhole.hotelds.api.hotel.web.model.res.KbResultResp;
import com.wormhole.hotelds.api.hotel.web.dao.repository.HdsHotelKbRepository;
import com.wormhole.hotelds.api.hotel.web.dao.repository.KnowledgeBaseRepository;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

/**
 * @author: joker.liu
 * @date: 2025/6/16
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class HotelKnowledgeService {

    private final ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    private final HdsHotelKbRepository hdsHotelKbRepository;

    private final AgentApiClient agentApiClient;

    private final HotelKnowledgeProperties hotelKnowledgeProperties;

    private final KnowledgeBaseRepository knowledgeBaseRepository;

    private final static String hotelKbCodeKey = "hotel:%s:kb:code";

    public Mono<KbResultResp> search(KbSearchReq req) {

        Preconditions.checkArgument(StringUtils.isNotBlank(req.getSpaceCode()), "space code must not null");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getHotelCode()), "hotel code must not null");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getQuery()), "query must not null");

        Mono<String> kbCodeMono = reactiveStringRedisTemplate.opsForValue().get(String.format(hotelKbCodeKey, req.getHotelCode()));

        return kbCodeMono
                .switchIfEmpty(findKbCodeByHotelCode(req.getSpaceCode(), req.getHotelCode()))
                .flatMap(kbCode->{
                    Mono<KnowledgeBaseEntity> knowledgeBaseEntityMono = knowledgeBaseRepository.findByKnowledgeCode(kbCode);
                    return knowledgeBaseEntityMono
                            .switchIfEmpty(Mono.error(new BusinessException("NOT-KB-CODE", "no kb code found")))
                            .flatMap(knowledgeBaseEntity -> {
                                String embeddingModel = knowledgeBaseEntity.getEmbeddingModel();
                                if (StringUtils.isBlank(embeddingModel)) {
                                    embeddingModel = hotelKnowledgeProperties.getEmbeddingModel();
                                }
                                KnowledgeSearchReq searchReq = buildKnowledgeSearchReq(req, kbCode, embeddingModel);
                                Mono<KnowledgeSearchContext> contextMono  = searchKnowledge(searchReq);
                                return contextMono.flatMap(context -> {
                                    List<KnowledgeMessageDTO> searchResultList = context.getSearchResultList();
                                    return Mono.just(buildKbResultResp(searchResultList));
                                }).onErrorResume(t -> Mono.error(new BusinessException("KB-FOUND-ERROR", t.getMessage())));
                    });

                });
    }

    private Mono<String> findKbCodeByHotelCode(String spaceCode, String hotelCode) {
        Mono<HdsHotelKbEntity> kbEntityMono = hdsHotelKbRepository.findBySpaceCodeAndHotelCode(spaceCode, hotelCode, 1);
        return kbEntityMono.switchIfEmpty(Mono.error(new BusinessException("NOT-KB-CODE", "space not kb code found")))
                .flatMap(kbEntity-> {
                    String knowledgeCode = kbEntity.getKnowledgeCode();
                    return reactiveStringRedisTemplate
                                .opsForValue().set(String.format(hotelKbCodeKey, hotelCode), kbEntity.getKnowledgeCode(), Duration.ofHours(1))
                            .thenReturn(knowledgeCode);
                });
    }

    private Mono<KnowledgeSearchContext> searchKnowledge(KnowledgeSearchReq req) {
        return agentApiClient.kbSearch(req)
                .flatMap(result -> {
                    if (ResultCode.SUCCESS.getCode().equals(result.getCode())) {
                        KnowledgeSearchContext searchContext = result.getData();
                        return Mono.just(searchContext);
                    }
                    return Mono.error(new BusinessException(result.getCode(), result.getMsg()));
                });
    }

    private KnowledgeSearchReq buildKnowledgeSearchReq(KbSearchReq req, String kbCode, String embeddingModel) {

        UnifiedModelEnum embeddingModelEnum = UnifiedModelEnum.findByModel(embeddingModel);
        Preconditions.checkArgument(Objects.nonNull(embeddingModelEnum), "embedding model not support");

        UnifiedModelEnum rerankModelEnum = UnifiedModelEnum.findByModel(hotelKnowledgeProperties.getRerankModel());
        Preconditions.checkArgument(Objects.nonNull(rerankModelEnum), "rerank model not support");

        KnowledgeSearchParams.SearchInput searchInput = new KnowledgeSearchParams.SearchInput();
        searchInput.setQuery(req.getQuery());

        KnowledgeSearchParams.RerankConfig rerankConfig = KnowledgeSearchParams.RerankConfig.builder()
                .model(hotelKnowledgeProperties.getRerankModel())
                .modelProvider(rerankModelEnum.getProvider())
                .topN(req.getTopK())
                .returnDocuments(hotelKnowledgeProperties.getReturnDocuments())
                .maxChunksPerDoc(hotelKnowledgeProperties.getMaxChunksPerDoc())
                .overlapTokens(hotelKnowledgeProperties.getOverlapTokens())
                .build();

        KnowledgeSearchParams searchParams = KnowledgeSearchParams.builder()
                .knowledgeCodeList(List.of(kbCode))
                .topK(req.getTopN())
                .minScore(req.getMinScore().doubleValue())
                .strategy(hotelKnowledgeProperties.getStrategy())
                .embeddingModel(embeddingModelEnum.getModel())
                .embeddingModelProvider(embeddingModelEnum.getProvider())
                .useRerank(hotelKnowledgeProperties.getUseRerank())
                .useRewrite(hotelKnowledgeProperties.getUseRewrite())
                .rewriteSystemPrompt(StringUtils.EMPTY)
                .isPersonalOnly(false)
                .rerankConfig(rerankConfig)
                .build();

        return KnowledgeSearchReq.builder()
                .searchInput(searchInput)
                .knowledgeSearchParams(searchParams)
                .recentMessageList(List.of())
                .build();

    }

    private KbResultResp buildKbResultResp(List<KnowledgeMessageDTO> searchResultList) {

        List<KbResultResp.Content> outputs = searchResultList.stream().map(item -> {
            String content = item.getContent();
            Double distance = item.getDistance();
            return KbResultResp.Content.builder()
                    .content(content)
                    .score(distance)
                    .build();
        }).toList();

        String output = JacksonUtils.writeValueAsString(outputs);

        return KbResultResp.builder()
                .output(output)
                .outputs(outputs)
                .size(searchResultList.size())
                .build();
    }

    public Mono<List<HotelKnowledgeResp>> batchQueryKnowledgeByHotelCodes(BatchHotelKnowledgeReq req) {
        return Flux.fromIterable(req.getHotelCodes())
                .flatMap(hotelCode ->
                        hdsHotelKbRepository.findByHotelCode(hotelCode, 1)
                                .flatMap(hotelKb ->
                                        knowledgeBaseRepository.findByKnowledgeCode(hotelKb.getKnowledgeCode())
                                                .map(knowledgeBase -> HotelKnowledgeResp.builder()
                                                        .hotelCode(hotelCode)
                                                        .knowledgeId(String.valueOf(hotelKb.getId()))
                                                        .knowledgeName(knowledgeBase.getName())
                                                        .build())
                                )
                                .switchIfEmpty(Mono.just(HotelKnowledgeResp.builder()
                                        .hotelCode(hotelCode)
                                        .build()))
                )
                .collectList();
    }
}
