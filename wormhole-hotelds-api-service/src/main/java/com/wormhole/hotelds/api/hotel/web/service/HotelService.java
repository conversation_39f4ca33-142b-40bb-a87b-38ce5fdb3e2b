package com.wormhole.hotelds.api.hotel.web.service;

import com.wormhole.agent.knowledge.core.KnowledgeBaseService;
import com.wormhole.agent.knowledge.model.dto.PluginHotelDTO;
import com.wormhole.common.constant.*;
import com.wormhole.common.enums.*;
import com.wormhole.common.result.Result;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.req.BatchHotelKnowledgeReq;
import com.wormhole.hotelds.api.hotel.req.PluginHotelReq;
import com.wormhole.hotelds.api.hotel.resp.HotelKnowledgeResp;
import com.wormhole.hotelds.api.hotel.web.model.res.HotelDetailVO;
import com.wormhole.hotelds.api.hotel.web.model.req.GetRoomsReq;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.plugin.service.PluginTaskService;
import com.wormhole.task.model.vo.TaskScheduleDetailVO;
import lombok.extern.slf4j.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.BeanUtils;
import org.springframework.data.r2dbc.core.*;
import org.springframework.data.relational.core.query.*;
import org.springframework.stereotype.*;
import org.springframework.web.bind.annotation.RequestBody;
import reactor.core.publisher.Mono;

import javax.annotation.*;
import java.time.*;
import java.util.*;


/**
 * <AUTHOR>
 * @date 2025/4/23 11:20
 */
@Service
@Slf4j
public class HotelService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    
    @Resource
    private DeviceBindService deviceBindService;

    @Resource
    private PluginTaskService pluginTaskService;

    @Resource
    private HotelKnowledgeService hotelKnowledgeService;

    public Mono<Boolean> checkHotelAccess(String hotelCode, String source) {
        Criteria criteria = Criteria.where(HdsHotelInfoFieldEnum.hotel_code.name()).is(hotelCode)
                .and(HdsHotelInfoFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId())
                .and(HdsHotelInfoFieldEnum.status.name()).not(RowStatusEnum.DELETE.getId());
        Query query = Query.query(criteria);

        return r2dbcEntityTemplate.selectOne(query, HdsHotelInfoEntity.class)
                .flatMap(hotelInfo -> {
                    if (isOtaExpired(hotelInfo, source)) {
                        log.warn("酒店服务已过期，hotelCode: {}, source: {}", hotelCode, source);
                        return Mono.just(false);
                    }
                    log.info("酒店信息校验通过，hotelCode: {}, source: {}", hotelCode, source);
                    return Mono.just(true);
                })
                .switchIfEmpty(Mono.defer(() -> {
                    log.warn("酒店信息不存在或已被禁用，hotelCode: {}, source: {}", hotelCode, source);
                    return Mono.just(false);
                }));
    }

    private boolean isOtaExpired(HdsHotelInfoEntity hotelInfo, String source) {
        if (!SourcePlatform.CHROME_PLUGIN.getCode().equals(source)) {
            return false;
        }
        // 如果状态为未完成初始化，则直接通过
        if (Objects.equals(hotelInfo.getStatus(), 2)) {
            return false;
        }
        // 不包含 OTA_AGENT则为不可用
        if (!StringUtils.contains(hotelInfo.getAiProductTypes(), "OTA_AGENT")){
            return true;
        }
        // 计算过期时间
        LocalDateTime createTime = hotelInfo.getInitFinishedAt();
        if (Objects.nonNull(hotelInfo.getOtaExtendMonths())) {
            createTime = createTime.plusMonths(hotelInfo.getOtaExtendMonths());
        }
        if (Objects.nonNull(hotelInfo.getOtaRewardMonths())) {
            createTime = createTime.plusMonths(hotelInfo.getOtaRewardMonths());
        }
        // 判断是否过期
        LocalDateTime expireTime = createTime.with(LocalTime.MAX);
        return expireTime.isBefore(LocalDateTime.now());
    }

    /**
     * 获取酒店详细信息
     *
     * @param hotelCode 酒店编码
     * @return 酒店详细信息
     */
    public Mono<HotelDetailVO> getHotelInfo(String hotelCode) {
        Criteria criteria = Criteria.where(HdsHotelInfoFieldEnum.hotel_code.name()).is(hotelCode)
                .and(HdsHotelInfoFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId())
                .and(HdsHotelInfoFieldEnum.status.name()).not(RowStatusEnum.DELETE.getId());
        Query query = Query.query(criteria);

        return r2dbcEntityTemplate.selectOne(query, HdsHotelInfoEntity.class)
                .flatMap(hotelInfo -> {
                    HotelDetailVO detailVO = new HotelDetailVO();
                    detailVO.setHotelCode(hotelInfo.getHotelCode());
                    detailVO.setHotelName(hotelInfo.getHotelName());
                    detailVO.setAddress(hotelInfo.getAddress());
                    detailVO.setCityCode(hotelInfo.getCityCode());
                    detailVO.setDistrictCode(hotelInfo.getDistrictCode());
                    detailVO.setLatitude(hotelInfo.getGaodeLatitude());
                    detailVO.setLongitude(hotelInfo.getGaodeLongitude());
                    
                    // 获取房间号信息
                    GetRoomsReq roomsReq = new GetRoomsReq();
                    roomsReq.setHotelCode(hotelCode);
                    return deviceBindService.getRoomCode(roomsReq)
                            .map(roomCode -> {
                                detailVO.setRoomCodes(roomCode);
                                return detailVO;
                            })
                            .defaultIfEmpty(detailVO);
                })
                .switchIfEmpty(Mono.error(new RuntimeException("酒店信息不存在或已被禁用")));
    }

    public Mono<Boolean> hotelInfoSync(PluginHotelReq req) {
        log.info("收到请求，开始同步酒店信息，req: {}", JacksonUtils.writeValueAsString(req));

        String requestJson = JacksonUtils.writeValueAsString(req);

        PluginHotelDTO pluginHotelDTO = JacksonUtils.readValue(requestJson, PluginHotelDTO.class);
        log.info("收到请求，转化后的数据，req: {}", JacksonUtils.writeValueAsString(pluginHotelDTO));
        return pluginTaskService.hotelInfoSync(pluginHotelDTO)
                .thenReturn(true)
                .onErrorResume(e ->{
                    log.error("同步酒店信息失败，req: {}", JacksonUtils.writeValueAsString(req), e);
                    return Mono.just(false);
                });
    }

    public Mono<List<HotelKnowledgeResp>> batchQueryHotelKnowledge(@RequestBody BatchHotelKnowledgeReq req) {
        return hotelKnowledgeService.batchQueryKnowledgeByHotelCodes(req);
    }
}
