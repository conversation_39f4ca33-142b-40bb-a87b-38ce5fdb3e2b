package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.SystemClock;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.MessageBody;
import com.wormhole.channel.consts.helper.RtcHelper;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.constant.SystemConstant;
import com.wormhole.hotelds.api.hotel.enums.EmployeeTypeEnum;
import com.wormhole.hotelds.api.hotel.web.model.req.TicketCommandMessage;
import com.wormhole.hotelds.api.hotel.web.model.req.TicketCreatedMessage;
import com.wormhole.hotelds.core.enums.ServiceSubcategory;
import com.wormhole.hotelds.core.enums.UserType;
import com.wormhole.mq.producer.ReactiveMessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@Service
@Slf4j
public class RtcService {

    @Autowired
    private RtcHelper rtcHelper;

    @Autowired
    private ReactiveMessageSender reactiveMessageSender;

    public Mono<Void> sendTicketCommandMessage(Collection<String> rtcUserIds, TicketCommandMessage ticketCommandMessage) {
        log.info("sendTicketCommandMessage rtcUserIds {} , ticketCommandMessage {}",
                JacksonUtils.writeValueAsString(rtcUserIds), JacksonUtils.writeValueAsString(ticketCommandMessage));
        // 1. 提取新创建的工单
        List<TicketCommandMessage.TicketSummary> newTickets = Optional.ofNullable(ticketCommandMessage.getTickets())
                .orElse(Collections.emptyList())
                .stream()
                .filter(summary -> Boolean.TRUE.equals(summary.getIsCreatedTicket()))
                .toList();
        // 对于分机：有非员工创建的新工单时，才需要播报新工单
        List<TicketCommandMessage.TicketSummary> normalNewTickets = newTickets.stream().filter(sum -> ObjectUtil.equal(sum.getUserType(), UserType.NORMAL.getCode())).toList();

        // 2. 【核心修改】如果有新工单，则强制设置 refresh 标志为 true
        if (CollectionUtil.isNotEmpty(newTickets)) {
            ticketCommandMessage.setRefresh(true);
        }

        CallbackMessage callbackMessage = new CallbackMessage()
                .setCommand(Instructions.TICKET_COMMAND.getCode())
                .setTimestamp(String.valueOf(SystemClock.now()))
                .setData(ticketCommandMessage);
        return sendDevice(rtcUserIds, callbackMessage)
                .then(Mono.defer(() -> {
                    if (ObjectUtil.equal(ticketCommandMessage.getEmployeeType(), EmployeeTypeEnum.EXTENSION.getCode())
                            && CollectionUtil.isNotEmpty(normalNewTickets) ) {
                        // 投递延迟队列消息
                        return sendDelayMessage(rtcUserIds, ticketCommandMessage, normalNewTickets)
                                .then();
                    }
                    return Mono.empty();
                }));
    }

    private Mono<Void> sendDelayMessage(Collection<String> rtcUserIds,
                                        TicketCommandMessage ticketCommandMessage,
                                        List<TicketCommandMessage.TicketSummary> newTickets) {
        MessageBody messageBody = new MessageBody();
        messageBody.setAction("CREATE_TICKET");
        messageBody.setTimestamp(DateUtil.current() + "");
        ticketCommandMessage.setTickets(newTickets);

        TicketCreatedMessage ticketCreatedMessage = TicketCreatedMessage.builder()
                .ticketCommandMessage(ticketCommandMessage)
                .userIds(rtcUserIds).build();

        messageBody.setData(ticketCreatedMessage);
        return reactiveMessageSender.sendDelayMessage(SystemConstant.CREATE_TICKET_PUSH_DELAY_MESSAGE, messageBody, SystemConstant.SIX)
                .doOnSuccess(aVoid -> log.info("RtcService sendDelayMessage success"))
                .doOnError(throwable -> log.error("RtcService sendDelayMessage error", throwable))
                .then();
    }

    /**
     * 向多个用户发送消息
     *
     * @param userIds 用户ID列表
     * @param callbackMessage 回调消息
     * @return 完成信号
     */
    public Mono<Void> sendDevice(Collection<String> userIds, CallbackMessage callbackMessage) {
        if (userIds == null || userIds.isEmpty() || callbackMessage == null) {
            log.error("发送消息失败: 用户列表为空或消息为空");
            return Mono.empty();
        }

        String message = JacksonUtils.writeValueAsString(callbackMessage);
        if (StrUtil.isBlank(message)) {
            log.error("发送消息失败: 序列化消息为空");
            return Mono.empty();
        }

        log.info("开始发送广播消息: userIds={}, messageType={}", userIds, callbackMessage);

        for (String userId : userIds) {
            rtcHelper.sendUnicastTextMessageAsync(userId, message)
                    .subscribeOn(Schedulers.boundedElastic())
                    .doOnSuccess(result -> log.info("发送广播消息成功: userId={}", userId))
                    .doOnError(error -> log.error("发送广播消息失败: userId={}, error={}",
                            userId, error.getMessage(), error))
                    .subscribe();
        }

        return Mono.empty();
    }
}
