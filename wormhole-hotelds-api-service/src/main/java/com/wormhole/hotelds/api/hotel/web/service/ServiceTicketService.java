package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.*;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.wormhole.agent.core.model.entity.UserEntity;
import com.wormhole.common.config.WormholeTranslationConfig;
import com.wormhole.common.config.WormholeUrlConfig;
import com.wormhole.channel.consts.message.*;
import com.wormhole.common.constant.ClientType;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.IdUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.config.TicketProperties;
import com.wormhole.hotelds.api.hotel.constant.*;
import com.wormhole.hotelds.api.hotel.req.CreateTicketSimpleReq;
import com.wormhole.hotelds.api.hotel.req.RefreshCommandReq;
import com.wormhole.hotelds.api.hotel.web.dao.*;
import com.wormhole.hotelds.api.hotel.web.dao.repository.EmployeeRepository;
import com.wormhole.hotelds.api.hotel.web.model.req.*;
import com.wormhole.hotelds.api.hotel.util.DeviceUtils;
import com.wormhole.hotelds.api.hotel.web.model.req.RoomTicketPriorityDTO;
import com.wormhole.hotelds.api.hotel.web.model.field.TicketCreateField;
import com.wormhole.hotelds.api.hotel.web.model.field.TicketGetField;
import com.wormhole.hotelds.api.hotel.web.model.res.PositionInfoVO;
import com.wormhole.hotelds.api.hotel.web.model.res.RoomTicketVO;
import com.wormhole.hotelds.api.hotel.web.model.res.ServiceTicketSimpleVO;
import com.wormhole.hotelds.api.hotel.web.model.res.ServiceTicketVO;
import com.wormhole.hotelds.api.hotel.web.dao.repository.EmployeeTicketMappingRepository;
import com.wormhole.hotelds.api.hotel.web.dao.repository.ServiceTicketRepository;
import com.wormhole.hotelds.api.hotel.util.TicketUtils;
import com.wormhole.hotelds.core.enums.*;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.mq.producer.ReactiveMessageSender;
import com.wormhole.task.model.constant.TicketEventTypeEnum;
import com.wormhole.task.model.dto.event.CleaningCompletedCallbackDTO;
import com.wormhole.task.model.dto.event.EmptyCleaningCompletedDTO;
import com.wormhole.task.model.dto.event.TicketMessageDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.redis.core.ReactiveHashOperations;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple8;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wormhole.common.config.WormholeTranslationConfig.TranslationKeys.*;
import static com.wormhole.hotelds.api.hotel.constant.RedisConstant.CONVERSATION_TICKETS_KEY;
import static com.wormhole.hotelds.core.enums.AccountTypeEnum.EXTENSION;
import static com.wormhole.hotelds.core.enums.AccountTypeEnum.MAIN_SWITCHBOARD;
import static com.wormhole.hotelds.core.enums.DeviceTypeEnum.FRONT;
import static com.wormhole.hotelds.core.enums.DeviceTypeEnum.FRONT_APP;
import static com.wormhole.hotelds.core.enums.ServiceSubcategory.CL_EMPTY_DIRTY;

import com.wormhole.channel.consts.MessageBody;

@Component
@Slf4j
public class ServiceTicketService {

    // 缓存相关常量
    private static final Duration CACHE_TTL = Duration.ofDays(1);

    private static final int DEFAULT_PAGE_SIZE = 15;
    @Resource
    private ServiceTicketRepository serviceTicketRepository;

    @Resource
    private EmployeeRepository employeeRepository;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Autowired
    private WormholeTranslationConfig translationConfig;

    @Resource
    private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;

    @Autowired
    private EmployeeTicketMappingRepository employeeTicketMappingRepository;
    @Autowired
    private HdsServiceTicketDao hdsServiceTicketDao;
    @Autowired
    private RtcService rtcService;

    @Autowired
    private DeviceInRoomService roomDeviceService;
    @Autowired
    private EmployeeTicketMappingService employeeTicketMappingService;

    @Autowired
    private HdsTicketLogsDao hdsTicketLogsDao;
    @Autowired
    private HdsDeviceDao hdsDeviceDao;
    @Autowired
    private CallbackInfoRedisDao callbackInfoRedisDao;
    @Autowired
    private CallBackBizService callBackBizService;

    @Autowired
    private CallLogService callLogService;

    @Autowired
    private HdsEmployeeTicketMappingDao hdsEmployeeTicketMappingDao;

    @Resource
    private ReactiveMessageSender reactiveMessageSender;

    @Autowired
    private HdsDevicePositionDao hdsDevicePositionDao;

    @Autowired
    private HdsChatUserDao hdsChatUserDao;

    @Autowired
    private UserDao userDao;

    @Autowired
    private TicketUtils ticketUtils;

    @Autowired
    private HdsHotelInfoDao hdsHotelInfoDao;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private DailyTicketStatService dailyTicketStatService;

    @Autowired
    private TicketProperties ticketProperties;
    /**
     * 创建工单
     *
     * @param req 创建工单请求参数
     * @return 创建结果
     */
    public Mono<String> createTicket(CreateTicketReq req) {
        // 参数校验
        validateTicketRequest(req);

        // 构建工单实体
        HdsServiceTicketEntity ticket = buildTicketEntity(req);

        // 获取Header信息并保存工单
        return HeaderUtils.getHeaderInfo()
                .doOnNext(headerInfo -> log.info("createTicket HeaderInfo:{},req:{}", JacksonUtils.writeValueAsString(headerInfo), JacksonUtils.writeValueAsString(req)))
                .flatMap(headerInfo -> {
                    // 设置用户信息
                    populateUserInfo(ticket, req, headerInfo);

                    // 保存工单
                    return serviceTicketRepository.save(ticket)
                            .flatMap(entity -> addCache(entity)
                                    .then(Mono.defer(() -> insertTicketLog(TicketActionTypeEnum.CREATE.getCode(), Collections.singletonList(ticket.getTicketNo()),req.getUserId(),req.getUserName())))
                                    .then(Mono.defer(() -> {
                                        // 更新工单创建统计指标
                                        return dailyTicketStatService.updateTicketCreateStat(
                                                req.getHotelCode(),
                                                req.getServiceCategory(),
                                                req.getServiceType()
                                        );
                                    }))
                                    .then(Mono.defer(() -> {
                                        afterCreateTicket(entity);
                                        return Mono.just(entity.getId().toString());
                                    })));

                })
                .doOnSuccess(result -> log.debug("工单创建成功 | req:{}", JacksonUtils.writeValueAsString(req)))
                .doOnError(e -> log.error("工单创建失败 | req:{} ", JacksonUtils.writeValueAsString(req), e))
                .onErrorResume(e -> Mono.error((new BusinessException(ResultCode.INVALID_PARAMETER, "系统繁忙，暂不可使用"))));
    }

    public Mono<String> createTicketSimple(CreateTicketReq req){
        // 参数校验
        validateTicketRequest(req);

        // 构建工单实体
        HdsServiceTicketEntity ticket = buildTicketEntity(req);
        // 获取Header信息并保存工单
        return HeaderUtils.getHeaderInfo()
                .doOnNext(headerInfo -> log.info("createTicketSimple.HeaderInfo:{},req:{}", JacksonUtils.writeValueAsString(headerInfo), JacksonUtils.writeValueAsString(req)))
                .flatMap(headerInfo -> {
                    // 设置用户信息
                    populateUserInfo(ticket, req, headerInfo);

                    // 保存工单
                    return serviceTicketRepository.save(ticket).then(Mono.just("创建工单成功"));
                })
                .doOnSuccess(result -> log.debug("createTicketSimple | req:{}", JacksonUtils.writeValueAsString(req)))
                .doOnError(e -> log.error("createTicketSimple | req:{} ", JacksonUtils.writeValueAsString(req), e))
                .onErrorResume(e -> Mono.error((new BusinessException(ResultCode.INVALID_PARAMETER, "系统繁忙，暂不可使用"))));
    }

    public Mono<Boolean> afterAllCreate(AfterAllTicketCreateReq req){
        log.info("afterAllCreate,req:{}",JacksonUtils.writeValueAsString(req));
        return Mono.just(true);
    }



    private void afterCreateTicket(HdsServiceTicketEntity entity) {
        log.info("afterCreateTicket,entity:{}", JacksonUtils.writeValueAsString(entity));

        // 初始化所有可能的Mono
        Mono<Void> sendTextTicketRefreshMono = Mono.empty();
        Mono<Void> bindCallLogMono = Mono.empty();
        Mono<Void> ticketRefreshMono = Mono.empty();
        Mono<Boolean> autoHandleMono = Mono.just(false);
//        Mono<Void> compensateCallBackMono = Mono.empty();
        Mono<Void> monitorOverdueStartMono = sendMonitorOverdue(entity);
        // 处理文字对话场景
        if (ObjectUtil.notEqual(entity.getConversationType(), ConversationType.VOICE.getCode())) {
            HdsTicketQO ticketQO = buildTicketQO(entity);
            // 文字sos类型新增
            ticketQO.setSosRemindFlag(StringUtils.equals(entity.getServiceType(), ServiceType.EMERGENCY.name()));
            ticketQO.setIsCreatedTicket(true);
            sendTextTicketRefreshMono = callBackBizService.sendTicketCommandRefresh(ticketQO, true);
            if (ObjectUtil.equal(entity.getUserType(), YesNoEnum.NO.getCode())){
                bindCallLogMono = callLogService.ticketServiceBindCallLog(entity);
            }
        }
        // 处理自动处理工单逻辑
        if (TicketUtils.autoComplete(entity)) {
            log.info("工单自动处理 创建时完成 | ticketNo:{}", entity.getTicketNo());
            autoHandleMono = handle(buildHandleReq(entity));
        }

        // 处理RTC房间回调逻辑
//        if (StringUtils.isNotBlank(entity.getRtcRoomId())) {
//            compensateCallBackMono = callbackInfoRedisDao.getCallInfoMsg(entity.getRtcRoomId(), RtcCallStatusEnum.FINISHED)
//                    .filter(Objects::nonNull)
//                    .flatMap(callInfoMessage -> {
//                        log.info("通话结束时工单还没创建 {}",entity.getTicketNo());
//                        return callBackBizService.fillCallEndTimeAndPushFeedback(callInfoMessage);
//                    })
//                    .doOnSuccess(result -> log.info("创建订单时，发送补偿回调成功 {}",entity.getTicketNo())) // HJE6CXB_DPMLQL57_1E0E3B9E-8DCA-4BF5-9B43-C9F2BAEDEF2B_1750147508592
//                    .doOnError(e -> log.error("创建订单时，发送补偿回调失败, {}",entity.getTicketNo(), e))
//                    .onErrorResume(e -> {
//                        log.error("创建订单时，发送补偿回调失: {}", e.getMessage());
//                        return Mono.empty();
//                    });
//        }

        // 新增：SOS工单发送MQ
        if (ObjectUtil.equal(entity.getServiceType(), ServiceType.EMERGENCY.name())) {
            ticketRefreshMono = sendSosTicketRefreshMessage(entity);
        }

        sendTextTicketRefreshMono
                .then(bindCallLogMono)
//                .then(compensateCallBackMono)
                .then(monitorOverdueStartMono)
                .then(ticketRefreshMono)
                .then(autoHandleMono.flatMap(result -> {
                    log.info("自动处理工单结果: {}", result);
                    return Mono.empty();
                }))
                .doOnSuccess(result -> log.info("所有工单后续处理完成"))
                .doOnError(e -> log.error("工单后续处理过程中发生错误,entity:{} ", JacksonUtils.writeValueAsString(entity),e))
                .onErrorResume(e -> {
                    log.error("工单后续处理过程中发生错误,entity:{} ", JacksonUtils.writeValueAsString(entity),e);
                    return Mono.empty();
                })
                .subscribeOn(Schedulers.boundedElastic())
                .subscribe();
    }

    /**
     * SOS工单发送MQ消息
     */
    private Mono<Void> sendSosTicketRefreshMessage(HdsServiceTicketEntity entity) {
        return employeeService.getNotifyEmployees(entity.getHotelCode())
                .flatMap(userIds -> {
                    // 构建消息体
                    TicketRefreshMessage msg = new TicketRefreshMessage();
                    msg.setHotelCode(entity.getHotelCode());
                    msg.setEvent("sos_ticket_create");
                    msg.setMessage("您有一张紧急SOS工单，请及时处理");
                    msg.setRefresh(true);
                    msg.setHasSound(true);
                    msg.setUserIds(userIds);

                    MessageBody messageBody = new MessageBody("DASHBOARD_REFRESH", String.valueOf(SystemClock.now()), msg);
                    log.info("发送SOS工单MQ消息: {}", JacksonUtils.writeValueAsString(messageBody));
                    return reactiveMessageSender.sendMessage(SystemConstant.SSE_EVENT_TOPIC, messageBody);
                })
                .then();
    }

    private Mono<Void> sendMonitorOverdue(HdsServiceTicketEntity entity) {
        // 20分钟。RocketMQ 不支持任意时间自定义的延迟消息，仅支持内置预设值的延迟时间间隔的延迟消息。
        SingleTicketReq req = new SingleTicketReq();
        req.setTicketNo(entity.getTicketNo());
        req.setRtcRoomId(entity.getRtcRoomId());
        req.setHotelCode(entity.getHotelCode());
        // todo 优化一下出入参
        return reactiveMessageSender.sendDelayMessage(SystemConstant.TICKET_OVERDUE_EVENT_TOPIC, req, 15).map(sendResult -> {
            log.info("发送工单超时到期消息成功 | ticketNo: {} {}", entity.getTicketNo(),JacksonUtils.writeValueAsString(sendResult));
            return Mono.just(sendResult);
        }).then();
    }

    private static HdsTicketQO buildTicketQO(HdsServiceTicketEntity entity) {
        return HdsTicketQO
                .builder()
                .ticketNos(Lists.newArrayList(entity.getTicketNo()))
                .build();
    }

    private static HandleTicketReq buildHandleReq(HdsServiceTicketEntity entity) {
        HandleTicketReq build = HandleTicketReq
                .builder()
                .ticketId(String.valueOf(entity.getId()))
                .isPush(false)
                // 自动
                .userId(StringUtils.isNotBlank(entity.getCompletedBy()) ? entity.getCompletedBy() : entity.getCreatedBy())
                .userName(StringUtils.isNotBlank(entity.getCompletedByName()) ? entity.getCompletedByName() : entity.getCreatedByName())
                .verifyPermission(false)
                .build();
        return build;
    }

    /**
     * 验证工单请求参数
     *
     * @param req 创建工单请求
     * @throws IllegalArgumentException 如果参数验证失败
     */
    private void validateTicketRequest(CreateTicketReq req) {
        // 检查用户输入内容
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getGuestRequest()), "用户输入内容不能为空");

        ServiceType serviceType = ServiceType.getByCode(req.getServiceType());
        if (serviceType == null){
            log.info("创建工单识别类型失败 serviceType 改写为其他 {}",req.getServiceType());
            req.setServiceType(ServiceType.OTHER.name());
        }

        // 检查工单类型
        ServiceCategory category = ServiceCategory.getByCode(req.getServiceCategory(), false);
        if (category == null){
            log.info("创建工单识别类型失败 ServiceCategory 改写为其他 {}",req.getServiceCategory());
            req.setServiceCategory(ServiceCategory.OTHER.name());
        }

        // 检查工单子类型
        ServiceSubcategory subcategory = ServiceSubcategory.getByCode(req.getServiceSubcategory(),false);
        if (subcategory == null){
            log.info("创建工单识别类型失败 ServiceSubcategory 改写为其他 {}",req.getServiceSubcategory());
            req.setServiceSubcategory(ServiceSubcategory.OTHER.name());
        }

    }

    /**
     * 构建工单实体
     */
    private HdsServiceTicketEntity buildTicketEntity(CreateTicketReq req) {
        HdsServiceTicketEntity ticket = new HdsServiceTicketEntity();
        BeanUtils.copyProperties(req, ticket);

        // 设置服务信息
        ServiceCategory category = ServiceCategory.getByCode(req.getServiceCategory(), false);
        ServiceSubcategory subcategory = ServiceSubcategory.getByCode(req.getServiceSubcategory(),false);
        ServiceType serviceType = ServiceType.getByCode(req.getServiceType());
        if (ObjectUtil.equal(category, ServiceCategory.COMPLAINT)){
            ticket.setClosedLoopLevel(TicketClosedLoopLevel.L2.getCode());
            ticket.setReplyExceptionType(SystemConstant.ZERO);
        }
        ticket.setServiceType(serviceType.name());
        ticket.setStatus(0);
        ticket.setTicketNo(IdUtils.generateId());
        ticket.setServiceCategory(category.getCode());
        ticket.setServiceCategoryName(category.getChineseName());
        ticket.setServiceSubcategory(subcategory.getCode());
        ticket.setServiceSubcategoryName(subcategory.getChineseName());
        ticket.setServiceKeywords(req.getServiceKeywords());

        // 设置酒店相关字段（使用默认值）
        ticket.setHotelCode(req.getHotelCode());
        ticket.setPositionCode(req.getPositionCode());
        ticket.setRtcRoomId(req.getRtcRoomId());
        ticket.setDeviceId(req.getDeviceId());
        ticket.setPriority(ServiceCategory.isPriorityType(category.getCode()) ? 1 : 0);
        ticket.setUserType(req.getUserType());

        // 设置审计字段
        LocalDateTime now = LocalDateTime.now();
        ticket.setCreatedAt(now);
        ticket.setUpdatedAt(now);
        ticket.setCreateType(req.getCreateType());
        ticket.setConversationId(req.getConversationId());
        Integer conversationType = null;
        if (StringUtils.isBlank(req.getConversationId())){
            conversationType = ConversationType.NOT_CONSERVATION.getCode();
        } else if (StringUtils.isNotBlank(req.getRtcRoomId())){
            conversationType = ConversationType.VOICE.getCode();
        } else {
            conversationType = ConversationType.TEXT.getCode();
        }
        ticket.setConversationType(conversationType);
        ticket.setEndOfCallTime(ObjectUtil.notEqual(ticket.getConversationType(), ConversationType.VOICE.getCode()) ? LocalDateTime.now() : null);
        return ticket;
    }

    /**
     * 设置用户信息
     */
    private void populateUserInfo(HdsServiceTicketEntity ticket, CreateTicketReq req, HeaderUtils.HeaderInfo headerInfo)  {
        // 提取HeaderInfo信息
        String userId = Objects.nonNull(req.getUserId()) ? req.getUserId() : headerInfo.getUserId();
        String userName = Objects.nonNull(req.getUserName()) ? req.getUserName() : headerInfo.getUsername();
        String clientType = StringUtils.isNotBlank(req.getClientType()) ? req.getClientType() : headerInfo.getClientType();

        ticket.setCreatedBy(userId);
        ticket.setCreatedByName(userName);
        ticket.setUpdatedBy(userId);
        ticket.setUpdatedByName(userName);
        ticket.setClientType(StringUtils.isNotBlank(clientType) ? Integer.parseInt(clientType) : null);
    }
    /**
     * 处理已保存的工单
     */
    private Mono<HdsServiceTicketEntity> addCache(HdsServiceTicketEntity savedEntity) {
        if (StringUtils.isNotBlank(savedEntity.getConversationId())) {
            return addTicketToCache(savedEntity.getConversationId(), savedEntity)
                    .then(Mono.just(savedEntity));
        } else {
            return Mono.just(savedEntity);
        }
    }

    /**
     * 将工单添加到Redis缓存（使用Hash结构）
     */
    private Mono<Void> addTicketToCache(String conversationId, HdsServiceTicketEntity ticketEntity) {
        if (StringUtils.isBlank(conversationId) || ticketEntity == null) {
            return Mono.empty();
        }

        String cacheKey = String.format(CONVERSATION_TICKETS_KEY, conversationId);
        String ticketJson = JSONUtil.toJsonStr(ticketEntity);

        // 使用Hash结构存储，key是会话ID，field是工单ID，value是工单JSON
        ReactiveHashOperations<String, String, String> hashOps = reactiveRedisTemplate.opsForHash();

        return hashOps.put(cacheKey, ticketEntity.getId().toString(), ticketJson)
                .then(reactiveRedisTemplate.expire(cacheKey, CACHE_TTL))
                .doOnSuccess(v -> log.debug("工单已添加到缓存 | conversationId: {} | ticketId: {}",
                        conversationId, ticketEntity.getId()))
                .then();
    }

    /**
     * 获取工单列表（后台接口，不使用缓存）
     */
    public Mono<PageResult<ServiceTicketVO>> getTickets(GetTicketReq req) {
        return Mono.fromCallable(() -> buildTicketQuery(req))
                .flatMap(query -> Mono.zip(
                        r2dbcEntityTemplate.count(query, HdsServiceTicketEntity.class),
                        r2dbcEntityTemplate.select(query, HdsServiceTicketEntity.class)
                                .map(this::toTicketVO)
                                .collectList()
                ))
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()));
    }




    /**
     * 获取工单列表（后台接口，不使用缓存）
     */
    public Mono<String> getSimpleTicketsJson(GetTicketReq req) {
        if (StringUtils.isBlank(req.getHotelCode()) || StringUtils.isBlank(req.getPositionCode())) {
            return Mono.just(StrUtil.EMPTY);
        } else {
            return Mono.fromCallable(() -> buildTicketQuery(req))
                    .flatMap(query ->
                            r2dbcEntityTemplate.select(query, HdsServiceTicketEntity.class)
                                    .map(ticket->toSimpleTicketVO(ticket,req.getLanguage()))
                                    .collectList()
                    )
                    .map(JacksonUtils::writeValueAsString);
        }
    }

    /**
     * 构建工单查询条件
     */
    private Query buildTicketQuery(GetTicketReq req) {
        Criteria criteria = Criteria.empty();

        // 添加过滤条件
        if (StringUtils.isNotBlank(req.getServiceCategory())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.service_category.name()).is(req.getServiceCategory());
        }

        if (req.getStatus() != null) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.status.name()).is(req.getStatus());
        }


        if (StringUtils.isNotBlank(req.getHotelCode())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.hotel_code.name()).is(req.getHotelCode());
        }

        if (StringUtils.isNotBlank(req.getPositionCode())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.position_code.name()).is(req.getPositionCode());
        }

        if (StringUtils.isNotBlank(req.getDeviceId())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.device_id.name()).is(req.getDeviceId());
        }

        if (StringUtils.isNotBlank(req.getUserId())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.created_by.name()).is(req.getUserId());
        }

        if (StringUtils.isNotBlank(req.getConversationId())) {
            criteria = criteria.and(HdsServiceTicketFieldEnum.conversation_id.name()).is(req.getConversationId());
        }
        if (req.getRecentMinutes() != null){
            LocalDateTime recentTime = LocalDateTime.now().minusMinutes(req.getRecentMinutes());
            criteria = criteria.and(HdsServiceTicketFieldEnum.created_at.name()).greaterThanOrEquals(recentTime);
        }
        // 设置排序
        Sort sort;
        if (StringUtils.isNotBlank(req.getSortName()) && Objects.nonNull(req.getSort())) {
            sort = Sort.by(req.getSort(), req.getSortName());
        } else {
            sort = Sort.by(Sort.Direction.ASC, HdsServiceTicketFieldEnum.created_at.name(), HdsServiceTicketFieldEnum.status.name());
        }

        // 设置分页
        PageRequest pageRequest = PageRequest.of(req.getCurrent() - 1, req.getPageSize());

        return Query.query(criteria).with(pageRequest).sort(sort);
    }

    /**
     * 实体转VO
     */
    private ServiceTicketVO toTicketVO(HdsServiceTicketEntity entity) {
        ServiceTicketVO vo = new ServiceTicketVO();
        BeanUtils.copyProperties(entity, vo);
        vo.setCreatedUser(entity.getCreatedBy());
        vo.setCreatedUserName(entity.getCreatedByName());
        vo.setIsOverdue(ObjectUtil.equal(entity.getOverdueFlag(), SystemConstant.ONE));
        return vo;
    }


    /**
     * 实体转VO
     */
    private ServiceTicketSimpleVO toSimpleTicketVO(HdsServiceTicketEntity entity, String language) {
        ServiceTicketSimpleVO serviceTicketSimpleVO = new ServiceTicketSimpleVO();
        TicketStatus ticketStatus = TicketStatus.getByCode(entity.getStatus());
        serviceTicketSimpleVO.setStatusDesc(Objects.nonNull(ticketStatus) ? ticketStatus.getDescription() : null);
        TicketPriority byCode = TicketPriority.getByCode(entity.getPriority());
        serviceTicketSimpleVO.setPriorityDesc(Objects.nonNull(byCode) ? byCode.getDescription() : null);
        boolean staff = Objects.equals(entity.getUserType(), UserType.STAFF.getCode());
        ServiceCategory serviceCategory = ServiceCategory.getByCode(entity.getServiceCategory()
                , staff);
        ServiceSubcategory serviceSubcategory = ServiceSubcategory.getByCode(entity.getServiceSubcategory(), staff);
        serviceTicketSimpleVO.setServiceCategoryName(serviceCategory.getChineseName());
        serviceTicketSimpleVO.setServiceSubcategoryName(serviceSubcategory.getChineseName());
        serviceTicketSimpleVO.setGuestRequest(TicketUtils.getGuestRequestByLang(entity,language));
        serviceTicketSimpleVO.setCreatedAt(entity.getCreatedAt());
        return serviceTicketSimpleVO;
    }


    /**
     * 从参数Map构建创建工单请求
     */
    /**
     * 从参数Map构建创建工单请求
     */
    private CreateTicketReq buildReq(Map<String, Object> inputParams) {
        return CreateTicketReq.builder()
                .serviceCategory(extractValue(inputParams, TicketCreateField.SERVICE_CATEGORY.getField(), String.class))
                .serviceSubcategory(extractValue(inputParams, TicketCreateField.SERVICE_SUBCATEGORY.getField(), String.class))
                .guestRequest(extractValue(inputParams, TicketCreateField.GUEST_REQUEST.getField(), String.class))
                .hotelCode(extractValue(inputParams, TicketCreateField.HOTEL_CODE.getField(), String.class))
                .positionCode(extractValue(inputParams, TicketCreateField.ROOM_CODE.getField(), String.class))
                .rtcRoomId(extractValue(inputParams, TicketCreateField.RTC_ROOM_ID.getField(), String.class))
                .deviceId(extractValue(inputParams, TicketCreateField.DEVICE_ID.getField(), String.class))
                .conversationId(extractValue(inputParams, TicketCreateField.CONVERSATION_ID.getField(), String.class))
                .clientReqId(extractValue(inputParams, TicketCreateField.CLIENT_REQ_ID.getField(), String.class))
                .userId(extractValue(inputParams, TicketCreateField.USER_ID.getField(), String.class))
                .userName(extractValue(inputParams, TicketCreateField.USER_NAME.getField(), String.class))
                .build();
    }

    private <T> T extractValue(Map<String, Object> map, String filed, Class<T> type) {
        Object value = map.get(filed);
        return type.isInstance(value) ? type.cast(value) : null;
    }

    /**
     * 从Map获取字符串值
     */
    private String getStringValue(Map<String, Object> inputParams, String key) {
        Object value = inputParams.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 从Redis缓存获取工单实体列表
     *
     * @param conversationId 会话ID
     * @return 工单实体列表
     */
    private Mono<List<HdsServiceTicketEntity>> getTicketEntitiesFromCache(String conversationId) {
        if (StringUtils.isBlank(conversationId)) {
            return Mono.just(new ArrayList<>());
        }

        String cacheKey = String.format(CONVERSATION_TICKETS_KEY, conversationId);
        ReactiveHashOperations<String, String, String> hashOps = reactiveRedisTemplate.opsForHash();

        // 检查缓存是否存在
        return reactiveRedisTemplate.hasKey(cacheKey)
                .flatMap(exists -> {
                    if (!exists) {
                        // 缓存不存在，直接从数据库加载
                        return loadAndCacheTicketEntities(conversationId);
                    }

                    // 获取Hash中的所有值
                    return hashOps.values(cacheKey)
                            .map(this::parseTicketEntity)
                            .filter(Objects::nonNull)
                            .collectList()
                            .flatMap(entities -> {
                                if (entities.isEmpty()) {
                                    // 缓存解析失败或为空，从数据库重新加载
                                    return loadAndCacheTicketEntities(conversationId);
                                }
                                return Mono.just(entities);
                            });
                });
    }

    /**
     * 解析JSON字符串为工单实体
     */
    private HdsServiceTicketEntity parseTicketEntity(String json) {
        try {
            return JSONUtil.toBean(json, HdsServiceTicketEntity.class);
        } catch (Exception e) {
            log.warn("解析缓存工单实体失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从数据库加载工单实体并缓存
     *
     * @param conversationId 会话ID
     * @return 工单实体列表
     */
    private Mono<List<HdsServiceTicketEntity>> loadAndCacheTicketEntities(String conversationId) {
        if (StringUtils.isBlank(conversationId)) {
            return Mono.just(new ArrayList<>());
        }

        // 构建查询
        Criteria criteria = Criteria.where(HdsServiceTicketFieldEnum.conversation_id.name()).is(conversationId);
        Query query = Query.query(criteria);

        // 使用Schedulers.boundedElastic()执行I/O操作
        return r2dbcEntityTemplate.select(query, HdsServiceTicketEntity.class)
                .collectList()
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(entities -> {
                    if (CollUtil.isEmpty(entities)) {
                        log.debug("数据库中不存在会话 {} 的工单", conversationId);
                        return Mono.just(new ArrayList<HdsServiceTicketEntity>());
                    }

                    log.debug("从数据库加载到 {} 条工单记录 | conversationId: {}", entities.size(), conversationId);
                    return cacheTicketEntities(conversationId, entities);
                })
                .doOnError(e -> log.error("从数据库加载工单失败 | conversationId: {} | error: {}",
                        conversationId, e.getMessage()));
    }

    /**
     * 将工单实体列表缓存到Redis
     *
     * @param conversationId 会话ID
     * @param entities       工单实体列表
     * @return 缓存成功后返回原工单实体列表
     */
    private Mono<List<HdsServiceTicketEntity>> cacheTicketEntities(String conversationId, List<HdsServiceTicketEntity> entities) {
        if (CollUtil.isEmpty(entities)) {
            return Mono.just(entities);
        }

        String cacheKey = String.format(CONVERSATION_TICKETS_KEY, conversationId);
        ReactiveHashOperations<String, String, String> hashOps = reactiveRedisTemplate.opsForHash();

        // 构建批量存储操作
        Map<String, String> entityMap = entities.stream()
                .filter(entity -> entity.getId() != null)
                .collect(Collectors.toMap(
                        entity -> entity.getId().toString(),
                        JSONUtil::toJsonStr,
                        (v1, v2) -> v2  // 处理可能的ID冲突，保留后值
                ));

        if (entityMap.isEmpty()) {
            return Mono.just(entities);
        }

        // 删除旧缓存，然后添加新缓存并设置过期时间
        return reactiveRedisTemplate.delete(cacheKey)
                .then(hashOps.putAll(cacheKey, entityMap))
                .then(reactiveRedisTemplate.expire(cacheKey, CACHE_TTL))
                .thenReturn(entities)
                .doOnSuccess(result -> log.debug("成功缓存 {} 条工单 | conversationId: {}",
                        entityMap.size(), conversationId))
                .onErrorResume(e -> {
                    log.error("缓存工单失败 | conversationId: {} | error: {}",
                            conversationId, e.getMessage());
                    return Mono.just(entities);  // 缓存失败仍返回实体列表
                });
    }

    /**
     * 根据条件过滤工单实体
     *
     * @param entities    工单实体列表
     * @param inputParams 过滤参数
     * @return 过滤后的工单列表
     */
    private List<HdsServiceTicketEntity> filterTicketEntities(List<HdsServiceTicketEntity> entities, Map<String, Object> inputParams) {
        if (CollUtil.isEmpty(entities)) {
            return new ArrayList<>();
        }
        Integer statusParam = extractValue(inputParams, TicketGetField.STATUS.getField(), Integer.class);
        String categoryParam = extractValue(inputParams, TicketGetField.SERVICE_CATEGORY.getField(), String.class);

        List<HdsServiceTicketEntity> filteredList = entities.stream()
                .filter(entity -> matchesStatusFilter(entity, statusParam))
                .filter(entity -> matchesCategoryFilter(entity, categoryParam))
                .collect(Collectors.toList());

        int limit = getIntValue(inputParams, "limit", DEFAULT_PAGE_SIZE);
        return limit < filteredList.size() ?
                filteredList.subList(0, limit) :
                filteredList;
    }

    /**
     * 检查工单是否匹配状态过滤条件
     */
    private boolean matchesStatusFilter(HdsServiceTicketEntity entity, Object statusParam) {
        if (statusParam == null || entity.getStatus() == null) {
            return true;
        }
        return entity.getStatus().toString().equals(statusParam.toString());
    }

    /**
     * 检查工单是否匹配类型过滤条件
     */
    private boolean matchesCategoryFilter(HdsServiceTicketEntity entity, String categoryParam) {
        if (StringUtils.isBlank(categoryParam)) {
            return true;
        }
        return categoryParam.equals(entity.getServiceCategory());
    }

    /**
     * 从Map中获取整数值，并确保不超过最大值
     *
     * @param params       参数Map
     * @param key          键名
     * @param defaultValue 默认值
     * @param maxValue     最大允许值
     * @return 解析后的整数值
     */
    private int getIntValue(Map<String, Object> params, String key, int defaultValue, int maxValue) {
        Object value = params.get(key);
        if (value == null) {
            return defaultValue;
        }

        try {
            int intValue = Integer.parseInt(value.toString());
            return intValue <= maxValue ? intValue : maxValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 从Map中获取整数值（使用默认的最大页面大小限制）
     */
    private int getIntValue(Map<String, Object> params, String key, int defaultValue) {
        return getIntValue(params, key, defaultValue, DEFAULT_PAGE_SIZE);
    }

    /**
     * 工单实体列表转VO列表
     */
    private List<ServiceTicketVO> entitiesToVOs(List<HdsServiceTicketEntity> entities) {
        if (CollUtil.isEmpty(entities)) {
            return new ArrayList<>();
        }

        return entities.stream()
                .map(this::toTicketVO)
                .collect(Collectors.toList());
    }

    /**
     * 通过Groovy获取工单列表（前端接口，使用缓存）
     *
     * @param inputParams 查询参数
     * @return JSON格式的分页结果
     */
    public Mono<String> getTicketsByGroovy(Map<String, Object> inputParams) {
        String conversationId = extractValue(inputParams, HdsServiceTicketFieldEnum.conversation_id.name(), String.class);
        if (StringUtils.isBlank(conversationId)) {
            log.warn("获取工单列表失败：会话ID为空");
            return Mono.just(JSONUtil.toJsonStr(PageResult.create(0L, new ArrayList<>())));
        }

        return getTicketEntitiesFromCache(conversationId)
                .map(entities -> filterTicketEntities(entities, inputParams))
                .map(this::convertToPageResult)
                .onErrorResume(e -> {
                    log.error("从缓存获取工单列表失败: {}", e.getMessage());
                    // 出错时回退到数据库查询
                    return queryTicketsFromDatabase(inputParams);
                })
                .switchIfEmpty(queryTicketsFromDatabase(inputParams))
                .doOnSuccess(result -> log.debug("获取工单列表成功 | conversationId: {}", conversationId));
    }

    /**
     * 将过滤后的工单实体转换为分页结果JSON
     */
    private String convertToPageResult(List<HdsServiceTicketEntity> entities) {
        List<ServiceTicketVO> vos = entitiesToVOs(entities);
        PageResult<ServiceTicketVO> pageResult = PageResult.create((long) vos.size(), vos);
        return JSONUtil.toJsonStr(pageResult);
    }

    /**
     * 从数据库查询工单并转换为JSON
     */
    private Mono<String> queryTicketsFromDatabase(Map<String, Object> inputParams) {
        GetTicketReq getTicketReq = createGetTicketReq(inputParams);
        return getTickets(getTicketReq)
                .map(JSONUtil::toJsonStr)
                .onErrorResume(e -> {
                    log.error("从数据库查询工单失败: {}", e.getMessage());
                    return Mono.just(JSONUtil.toJsonStr(PageResult.create(0L, new ArrayList<>())));
                });
    }

    /**
     * 通过Groovy获取工单用户请求内容（前端接口，使用缓存）
     */
    public Mono<String> getTicketGuestRequestByGroovy(Map<String, Object> inputParams) {
        String conversationId = extractValue(inputParams, TicketGetField.CONVERSATION_ID.getField(), String.class);
        if (StringUtils.isBlank(conversationId)) {
            return Mono.empty();
        }

        return getTicketEntitiesFromCache(conversationId)
                .map(entities -> filterTicketEntities(entities, inputParams))
                .map(filteredEntities -> {
                    if (CollUtil.isEmpty(filteredEntities)) {
                        return "";
                    }

                    // 获取并连接所有请求内容
                    return filteredEntities.stream()
                            .map(HdsServiceTicketEntity::getGuestRequest)
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining(", "));
                })
                .filter(StringUtils::isNotBlank)
                .switchIfEmpty(
                        // 缓存为空时查询数据库
                        Mono.defer(() -> {
                            GetTicketReq getTicketReq = createGetTicketReq(inputParams);
                            return getTickets(getTicketReq)
                                    .flatMap(pageResult -> {
                                        if (pageResult == null || CollUtil.isEmpty(pageResult.getDataList())) {
                                            return Mono.empty();
                                        }

                                        // 连接所有请求内容
                                        String allRequests = pageResult.getDataList().stream()
                                                .map(ServiceTicketVO::getGuestRequest)
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.joining(", "));

                                        return Mono.justOrEmpty(allRequests);
                                    });
                        })
                )
                .doOnSuccess(result -> log.debug("获取工单用户请求内容 | conversationId: {}", conversationId));
    }

    /**
     * 创建查询工单请求
     */
    @NotNull
    private GetTicketReq createGetTicketReq(Map<String, Object> inputParams) {
        GetTicketReq getTicketReq = new GetTicketReq();

        // 设置状态
        Object status = inputParams.get(HdsServiceTicketFieldEnum.status.name());
        getTicketReq.setStatus(Objects.nonNull(status) ? Integer.parseInt(status.toString()) : null);

        // 设置其他参数
        getTicketReq.setServiceCategory(extractValue(inputParams, TicketGetField.SERVICE_CATEGORY.getField(), String.class));
        getTicketReq.setConversationId(extractValue(inputParams, TicketGetField.CONVERSATION_ID.getField(), String.class));
        getTicketReq.setCurrent(1);

        // 设置分页大小
        int limit = getIntValue(inputParams, "limit", DEFAULT_PAGE_SIZE);
        getTicketReq.setPageSize(limit);

        return getTicketReq;
    }


    public Mono<PageResult<RoomTicketVO>>  listByStuff(GetRoomTicketReq req) {
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getHotelCode()), "酒店编码不能为空");
        Preconditions.checkArgument(Objects.nonNull(req.getCurrentPage()), "页码不能为空");
        Preconditions.checkArgument(Objects.nonNull(req.getPageSize()), "页面大小不能为空");
        return HeaderUtils.getHeaderInfo()
                .doOnNext(header -> log.info("HeaderInfo:{},req:{}", JacksonUtils.writeValueAsString(header), JacksonUtils.writeValueAsString(req)))
                .flatMap(header -> employeeTicketMappingRepository.findByEmployeeIdAndHotelCode(header.getUserId(),header.getHotelCode())
                        .doOnNext(accountMapping -> log.info("accountMapping result:{}", JacksonUtils.writeValueAsString(accountMapping)))
                        .flatMap(accountMapping -> processAccountMapping(accountMapping, req))
                        .switchIfEmpty(Mono.fromCallable(() -> PageResult.empty(req.getCurrentPage(),req.getPageSize()))))
                .doOnNext(result -> log.info("listByStuff result:{}", JacksonUtils.writeValueAsString(result)))
                .doOnError(e -> log.error("查询工单列表失败:",e))
                .onErrorResume(e -> Mono.error((new BusinessException(ResultCode.INVALID_PARAMETER, "系统繁忙，暂不可使用"))));

    }

    private Mono<PageResult<RoomTicketVO>> processAccountMapping(HdsEmployeeTicketMappingEntity accountMapping, GetRoomTicketReq req) {
        List<String> ticketCategories = getTicketCategories(accountMapping);
        Integer accountType = accountMapping.getEmployeeType();

        if (CollUtil.isEmpty(ticketCategories) && Objects.equals(accountType, 2)) {
            return Mono.fromCallable(() -> PageResult.empty(req.getCurrentPage(),req.getPageSize()));
        }
        // 根据后台配置设置限制位置
        return hdsDevicePositionDao.transferAreaCodesToPositions(accountMapping.getAreaCodes(),accountMapping.getHotelCode()).flatMap( positions-> {
            req.setLimitPositionCodes( positions);
            return findRoomsByPriority(req, ticketCategories, accountType);
        });
    }

    private Mono<PageResult<RoomTicketVO>> findRoomsByPriority(GetRoomTicketReq req, List<String> ticketCategories, Integer accountType) {
        return hdsServiceTicketDao.findRoomsWithPriorityAndCountByConditions(req.getPositionName(),
                        req.getHotelCode(), ticketCategories, req.getCurrentPage(), req.getPageSize(),req.getLimitPositionCodes())
                .flatMap(tuple2 -> {

                    List<RoomTicketPriorityDTO> datas = tuple2.getT1();
                    Long total = tuple2.getT2();
                    if (CollUtil.isEmpty(datas)) {
                        return Mono.fromCallable(() -> PageResult.empty(req.getCurrentPage(), req.getPageSize()));
                    }

                    return processRooms(datas,total, req, ticketCategories, accountType);
                });
    }

    private Mono<PageResult<RoomTicketVO>> processRooms(List<RoomTicketPriorityDTO> roomPriorities, Long totalCount,
                                                        GetRoomTicketReq req,
                                                        List<String> ticketCategories,
                                                        Integer accountType) {
        // 提取房间编码
        List<String> positionCodes = roomPriorities.stream()
                .map(RoomTicketPriorityDTO::getPositionCode)
                .collect(Collectors.toList());

        // 并行查询所需数据
        Mono<Map<String, HdsDevicePositionEntity>> roomInfoMono = fetchRoomInfo(positionCodes, req.getHotelCode());
        HdsTicketQO hdsTicketQO = HdsTicketQO
                .builder()
                .positionCodes(positionCodes)
                .serviceCategories(ticketCategories)
                .hotelCode(req.getHotelCode())
                .expiredFlag(ExpiredFlagEnum.NORMAL.getCode())
                .hasEndOfCall(true)
                .build();
        Mono<List<HdsServiceTicketEntity>> ticketsMono = hdsServiceTicketDao.findList(
                hdsTicketQO);
        Mono<Boolean> hasPorityMono= CollUtil.isEmpty(ticketCategories)
                ||ticketCategories.contains(ServiceCategory.COMPLAINT.name())
                || ticketCategories.contains(ServiceCategory.EMERGENCY.name())
                ? hdsServiceTicketDao.hasPriorityTicket(req.getHotelCode())
                : Mono.just(false);

        HdsDeviceQO hdsDeviceQO = HdsDeviceQO
                .builder()
                .hotelCode(req.getHotelCode())
                .positionCodes(positionCodes)
                .build();
        Mono<Map<String, HdsDeviceEntity>> positionCode2DeviceMono = hdsDeviceDao
                .findList(hdsDeviceQO)
                .map(hdsDeviceEntities -> Optional.ofNullable(hdsDeviceEntities).orElse(new ArrayList<>())
                        .stream()
                        .collect(Collectors.toMap((HdsDeviceEntity::getPositionCode), Function.identity(), (e1, e2) -> e1)));

        Mono<HdsHotelInfoEntity> hotelInfoMono = hdsHotelInfoDao.queryByHotelCode(req.getHotelCode());


        return ticketsMono.flatMap(tickets -> {

            // 提取创建者ID并查询用户信息
            Mono<Map<String, HdsChatUserEntity>> wechatUserMono = getWechatUserInfoMap(tickets);

            Mono<Map<String, UserEntity>> bdwUserInfoMono = getAppUserInfoMap(tickets);
            // 先 zip 8 个
            Mono<Tuple8<Map<String, HdsDevicePositionEntity>, Long, List<HdsServiceTicketEntity>,
                    Map<String, HdsChatUserEntity>, HeaderUtils.HeaderInfo,
                    Boolean, Map<String, HdsDeviceEntity>, Map<String, UserEntity>>> baseZipMono =
                    Mono.zip(roomInfoMono, Mono.just(totalCount), Mono.just(tickets),
                            wechatUserMono, HeaderUtils.getHeaderInfo(), hasPorityMono,
                            positionCode2DeviceMono, bdwUserInfoMono);

            return Mono.zip(baseZipMono, hotelInfoMono)
                    .map(tuple ->{
                        HdsHotelInfoEntity hotelInfo = tuple.getT2();
                        String timezone = Optional.ofNullable(hotelInfo)
                                .map(HdsHotelInfoEntity::getTimeZone)
                                .orElse("Asia/Shanghai");
                        return buildPageResult(tuple.getT1(), roomPriorities, req, timezone);
                    });
        });
    }

    public Mono<Map<String, UserEntity>> getAppUserInfoMap(List<HdsServiceTicketEntity> tickets) {
        Set<String> appUserIds = extractBdwAppCreatorIds(tickets);
        return CollUtil.isEmpty(appUserIds)
                ? Mono.just(Collections.emptyMap())
                :  userDao.fetchBdwUserInfo(appUserIds);
    }

    public Mono<Map<String, HdsChatUserEntity>> getWechatUserInfoMap(List<HdsServiceTicketEntity> tickets) {
        Set<String> wechatUserIds = extractWechatCreatorIds(tickets);
        return CollUtil.isEmpty(wechatUserIds)
                ? Mono.just(Collections.emptyMap())
                :  hdsChatUserDao.fetchWechatUserInfo(wechatUserIds);
    }





    private Set<String> extractBdwAppCreatorIds(List<HdsServiceTicketEntity> tickets) {
        return tickets.stream()
                .filter(e -> ObjectUtil.notEqual(e.getClientType(), ClientType.MINI_PROGRAM.getCode()))
                .map(HdsServiceTicketEntity::getDeviceId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }
    private Set<String> extractWechatCreatorIds(List<HdsServiceTicketEntity> tickets) {
        return tickets.stream()
                .filter(e -> ClientType.isMiniProgram(e.getClientType()))
                .map(HdsServiceTicketEntity::getDeviceId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    private PageResult<RoomTicketVO> buildPageResult(Tuple8<Map<String, HdsDevicePositionEntity>, Long, List<HdsServiceTicketEntity>,
                Map<String, HdsChatUserEntity>, HeaderUtils.HeaderInfo , Boolean, Map<String, HdsDeviceEntity>,Map<String, UserEntity>> tuple,
                                                     List<RoomTicketPriorityDTO> roomPriorities,
                                                     GetRoomTicketReq req, String timezone) {
        Map<String, HdsDevicePositionEntity> roomInfoMap = tuple.getT1();
        Long total = tuple.getT2();
        List<HdsServiceTicketEntity> tickets = tuple.getT3();
        Map<String, HdsChatUserEntity> wechatUserInfoMap = tuple.getT4();
        String language = tuple.getT5().getLanguage();
        Boolean hasPriorityTicket = tuple.getT6();
        Map<String, HdsDeviceEntity> positionCode2Device = tuple.getT7();
        Map<String, UserEntity> bdwUsrInfoMap = tuple.getT8();

        // 按房间分组工单
        Map<String, List<HdsServiceTicketEntity>> ticketsByRoom = tickets.stream()
                .collect(Collectors.groupingBy(HdsServiceTicketEntity::getPositionCode));

        // 为每个房间创建 RoomTicketVO
        List<RoomTicketVO> roomTicketVOs = roomPriorities.stream()
                .map(roomPriority -> {
                    String positionCode = roomPriority.getPositionCode();
                    List<HdsServiceTicketEntity> roomTickets = ticketsByRoom.getOrDefault(positionCode, Collections.emptyList());
                    return createRoomTicketVO(
                            Optional.ofNullable(roomInfoMap.get(positionCode))
                            , roomTickets, wechatUserInfoMap
                            , Optional.ofNullable(positionCode2Device.get(positionCode))
                            ,bdwUsrInfoMap,language, timezone);
                })
                .collect(Collectors.toList());

        HashMap<String, Object> extraInfo = new HashMap<>();
        extraInfo.put("has_priority_ticket", hasPriorityTicket);
        return PageResult.<RoomTicketVO>create(total, roomTicketVOs, req.getCurrentPage(), req.getPageSize(), null, extraInfo);
    }

    /**
     * 从账户映射中获取票据类别
     */
    private List<String> getTicketCategories(HdsEmployeeTicketMappingEntity accountMapping) {
        if (Objects.equals(accountMapping.getEmployeeType(), 1)) {
            // 账户类型1：返回null表示查询所有类别
            return null;
        } else if (Objects.equals(accountMapping.getEmployeeType(), 2)) {
            // 账户类型2：解析逗号分隔的类别
            if (StringUtils.isEmpty(accountMapping.getTicketCategories())) {
                return null; // 空字符串返回null，在调用方处理为空结果
            } else {
                return Arrays.asList(accountMapping.getTicketCategories().split(","));
            }
        }
        return new ArrayList<>(); // 默认返回空列表
    }

//    private Mono<List<HdsEmployeeTicketMappingEntity>> getExtensionAccountMapping(String hotelCode) {
//        HdsEmployeeTicketMappingQO hdsEmployeeTicketMappingQO = HdsEmployeeTicketMappingQO
//                .builder()
//                .hotelCode(hotelCode)
//                .accountTypeEnum(EXTENSION)
//                .acceptTicketStatus(AcceptTicketStatusEnum.ALLOW_ACCEPT.getCode())
//                .build();
//        return employeeTicketMappingService.findOnlineEmployeeTicketMapping(hdsEmployeeTicketMappingQO);
//    }

    /**
     * 获取房间信息
     */
    private Mono<Map<String, HdsDevicePositionEntity>> fetchRoomInfo(List<String> positionCodes, String hotelCode) {
        Criteria roomCriteria = Criteria.where(HdsDevicePositionFieldEnum.position_code.name()).in(positionCodes)
                .and(HdsDevicePositionFieldEnum.hotel_code.name()).is(hotelCode);

        return r2dbcEntityTemplate.select(Query.query(roomCriteria), HdsDevicePositionEntity.class)
                .collectMap(HdsDevicePositionEntity::getPositionCode);
    }




    /**
     * 创建RoomTicketVO对象，增加手机号信息
     */
    private RoomTicketVO createRoomTicketVO(Optional<HdsDevicePositionEntity> positionEntityOpt,
                                            List<HdsServiceTicketEntity> roomTickets, Map<String, HdsChatUserEntity> userInfoMap,
                                            Optional<HdsDeviceEntity> hdsDeviceOpt, Map<String, UserEntity> bdwUsrInfoMap,
                                            String language, String timeZone) {
        RoomTicketVO roomTicketVO = new RoomTicketVO();


        roomTicketVO.setRoomDeceiveId(hdsDeviceOpt
                .map(HdsDeviceEntity::getDeviceId)
                .orElse(null));
        roomTicketVO.setDeviceUnavailabilityReason(hdsDeviceOpt
                .map(HdsDeviceEntity::getDeviceStatus)
                .map(status->ticketUtils.getDeviceExceptionTipsTranslate(status,language))
                .orElse(null));


        positionEntityOpt.ifPresent(e -> {
            PositionInfoVO positionInfoVO = new PositionInfoVO();
            BeanUtils.copyProperties(e, positionInfoVO);
            positionInfoVO.setPositionName(DeviceUtils.getPositionFullName(e));
            roomTicketVO.setPositionInfoVO(positionInfoVO);
        });

        //时区
        roomTicketVO.setTimeZone(timeZone);

        // 分离完成和未完成的工单
        List<RoomTicketVO.TicketSimpleVO> notCompleteTickets = new ArrayList<>();
        List<RoomTicketVO.TicketSimpleVO> completeTickets = new ArrayList<>();

        for (HdsServiceTicketEntity ticket : roomTickets) {
            RoomTicketVO.TicketSimpleVO ticketVO = convertToTicketSimpleVO(ticket, userInfoMap, bdwUsrInfoMap,language);

//            if (Objects.equals(accountType, MAIN_SWITCHBOARD.getCode())) {
//                boolean b = HdsEmployeeTicketMappingEntitys
//                        .stream().anyMatch(e -> e.getTicketCategories()
//                                .contains(ticket.getServiceCategory()));
//                ticketVO.setAllowHandle(!b);
//            } else {
//            }
            // 总机权限改为所有
            ticketVO.setAllowHandle(true);

            if (ticket.getStatus() == 0) { // 未完成
                notCompleteTickets.add(ticketVO);
            } else { // 已完成
                ticketVO.setTicketCompletionTime(ticket.getCompletionTime());
                ticketVO.setTicketCompletionTimestamp(LocalDateTimeUtil.toEpochMilli(ticket.getCompletionTime()));
                ticketVO.setUserTypeTagColor(ticketProperties.getCompleteTicketColor());
                completeTickets.add(ticketVO);
            }
            // 回复异常
            ticketVO.setReplayExceptionType(ticket.getReplyExceptionType());
            ticketVO.setServiceSubcategory(ticket.getServiceSubcategory());
            ticketVO.setReplayExceptionText(ObjectUtil.equal(ticket.getReplyExceptionType(),1)|| ObjectUtil.equal(ticket.getReplyExceptionType(),2) ? translationConfig.translateSync(WormholeTranslationConfig.TranslationKeys.complaint_warning,language) : StringUtils.EMPTY);
        }


        // 对未完成工单进行排序
        sortNotCompleteTickets(notCompleteTickets);

        // 对已完成工单进行排序
        sortCompleteTickets(completeTickets);

        roomTicketVO.setNotCompleteTickets(notCompleteTickets);
        roomTicketVO.setCompleteTickets(completeTickets);

        return roomTicketVO;
    }

    /**
     * 排序未完成工单：先按照displayStatus降序，相同的按照callEndTime、ticketCreateTime升序
     * @param notCompleteTickets 未完成工单列表
     */
    private void sortNotCompleteTickets(List<RoomTicketVO.TicketSimpleVO> notCompleteTickets) {
        notCompleteTickets.sort((o1, o2) -> {
            // 下单类型升序
            int userTypeCompare = o1.getUserType().compareTo(o2.getUserType());
            if (userTypeCompare != 0) {
                return userTypeCompare;
            }
            // userType 相同，都是员工（1）时，按 serviceSubcategory 排序
            if (o1.getUserType() == 1) {
                return compareServiceSubcategory(o1.getServiceSubcategory(), o2.getServiceSubcategory());
            }
            // 先按displayStatus降序
            int statusCompare = o2.getDisplayStatus().compareTo(o1.getDisplayStatus());
            if (statusCompare != 0) {
                return statusCompare;
            }
            // 相同displayStatus，按callEndTime升序
            int callTimeCompare = compareDateTime(o1.getCallEndTime(), o2.getCallEndTime());
            if (callTimeCompare != 0) {
                return callTimeCompare;
            }
            // 相同callEndTime，按ticketCreateTime升序
            return compareDateTime(o1.getTicketCreateTime(), o2.getTicketCreateTime());
        });
    }
    /**
     * 比较员工订单的 serviceSubcategory 优先级
     * 优先级：空房清扫 > 住房清扫 > 其他
     */
    private int compareServiceSubcategory(String s1, String s2) {
        int rank1 = getSubcategoryRank(s1);
        int rank2 = getSubcategoryRank(s2);
        return Integer.compare(rank1, rank2);
    }

    /**
     * 获取 serviceSubcategory 的优先级等级（数字越小优先级越高）
     */
    private int getSubcategoryRank(String serviceSubcategory) {
        if (serviceSubcategory == null) {
            return 3; // 视为“其他”
        }
        return switch (ServiceSubcategory.getByCode(serviceSubcategory,false)) {
            case CL_EMPTY_DIRTY -> 1;
            case CL_OCCUPIED -> 2;
            default -> 3;
        };
    }
    /**
     * 排序已完成工单：按照callEndTime、ticketCreateTime升序
     * @param completeTickets 已完成工单列表
     */
    private void sortCompleteTickets(List<RoomTicketVO.TicketSimpleVO> completeTickets) {
        completeTickets.sort((o1, o2) -> {
            // 先按callEndTime升序
            int callTimeCompare = compareDateTime(o1.getCallEndTime(), o2.getCallEndTime());
            if (callTimeCompare != 0) {
                return callTimeCompare;
            }
            // 相同callEndTime，按ticketCreateTime升序
            return compareDateTime(o1.getTicketCreateTime(), o2.getTicketCreateTime());
        });
    }

    /**
     * 辅助方法：比较两个LocalDateTime，考虑null值情况
     * @param time1 第一个时间
     * @param time2 第二个时间
     * @return 比较结果
     */
    private int compareDateTime(LocalDateTime time1, LocalDateTime time2) {
        if (time1 == null && time2 == null) {
            return 0;
        }
        if (time1 == null) {
            return -1; // null值排在前面
        }
        if (time2 == null) {
            return 1;
        }
        return time1.compareTo(time2);
    }

    /**
     * 转换为TicketSimpleVO，包含用户手机号
     */
    private RoomTicketVO.TicketSimpleVO convertToTicketSimpleVO(HdsServiceTicketEntity ticket,
                                                                Map<String, HdsChatUserEntity> userInfoMap, Map<String, UserEntity> bdwUsrInfoMap, String language) {
        RoomTicketVO.TicketSimpleVO ticketVO = new RoomTicketVO.TicketSimpleVO();
        ticketVO.setGuestRequest(TicketUtils.getGuestRequestByLang(ticket,language));
        ticketVO.setCallEndTime(ticket.getEndOfCallTime());
        ticketVO.setCallEndTimeStamp(LocalDateTimeUtil.toEpochMilli(ticket.getEndOfCallTime()));
        ticketVO.setTicketCreateTime(ticket.getCreatedAt());
        ticketVO.setTicketCreateTimeStamp(LocalDateTimeUtil.toEpochMilli(ticket.getCreatedAt()));
        ticketVO.setServiceCategory(ticket.getServiceCategory());
        ticketVO.setServiceType(ticket.getServiceType());
        ticketVO.setUserId(ticket.getCreatedBy());
        ticketVO.setConversationId(ticket.getConversationId());
        ticketVO.setStatus(ticket.getStatus());
        ticketVO.setTagFlag(TicketTagFlagEnum.NOT_TAG.getCode());
        ticketVO.setUserType(ticket.getUserType());
        //文字颜色
        if (Objects.equals(ticket.getUserType(), UserType.STAFF.getCode())) {
            ticketVO.setUserTypeTagText(ticketProperties.getStaffTicketText());
            ticketVO.setUserTypeTagColor(ticketProperties.getNotCompleteStaffTicketColor());
        }else {
            ticketVO.setUserTypeTagText(ticketProperties.getCustomerTicketText());
            ticketVO.setUserTypeTagColor(ticketProperties.getNotCompleteCustomerTicketColor());
        }

        ticketVO.setTicketId(String.valueOf(ticket.getId()));

        Optional.ofNullable(ServiceCategory.getByCode(ticket.getServiceCategory(), false)).ifPresent(e -> {
            ticketVO.setDisplayCategoryText(e.getDisplayText());
        });
        ticketVO.setDisplayCategoryUrl(ticketUtils.getCategoryIcon(WormholeUrlConfig.UrlKeys.LIST_BY_STUFF,ticket.getServiceCategory()));
        Optional.ofNullable(TicketClosedLoopLevel.getByCode(ticket.getClosedLoopLevel())).ifPresent(e -> {
            // 根据闭环机制设置处理状态
            ticketVO.setIncompleteProcessType(e.getIncompleteProcessType());
        });


        if (Objects.equals(ticket.getStatus(), 0) && Objects.equals(ticket.getPriority(), 1)) {
            ticketVO.setDisplayStatus(2);
        } else if (Objects.equals(ticket.getStatus(), 0)) {
            ticketVO.setDisplayStatus(0);
        } else {
            ticketVO.setDisplayStatus(1);
        }
        ticketVO.setDisplayCategoryTextColor(TicketUtils.getShowTextColor(ticket));
        ticketVO.setIsMiniProgram(ClientType.isMiniProgram(ticket.getClientType()));
        ticketVO.setOfflineDialing(Objects.equals(ticket.getClientType(), ClientType.MINI_PROGRAM.getCode())
                || Objects.equals(ticket.getClientType(), ClientType.BDW_APP.getCode()));


        Integer clientType = ticket.getClientType();
        ticketVO.setClientType(clientType);
        String deviceId = ticket.getDeviceId();
        if (Objects.equals(clientType, ClientType.MINI_PROGRAM.getCode())) {
            Optional.ofNullable(userInfoMap.get(deviceId))
                    .ifPresent(ele->{
                        ticketVO.setMobile( ele.getMobile());
                        ticketVO.setAreaCode("86");
                    });
        } else if (Objects.equals(clientType, ClientType.BDW_APP.getCode())) {
            Optional.ofNullable(bdwUsrInfoMap.get(deviceId))
                    .ifPresent(ele->{
                        ticketVO.setMobile( ele.getPhone());
                        ticketVO.setAreaCode(ele.getAreaCode());
                    });

        }

        return ticketVO;
    }

    public Mono<Boolean> handle(HandleTicketReq req) {
        Preconditions.checkArgument(Objects.nonNull(req.getTicketId()), "工单ID不能为空");

        return HeaderUtils.getHeaderInfo()
                .doOnNext(headerInfo -> log.info("handle headerInfo:{},req:{}", JacksonUtils.writeValueAsString(headerInfo), JacksonUtils.writeValueAsString(req)))
                .flatMap(headerInfo -> serviceTicketRepository.findById(Long.parseLong(req.getTicketId()))
                        .doOnNext(entity -> log.info("处理工单 handle entity:{}", JacksonUtils.writeValueAsString(entity)))
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "工单不存在或已被删除")))
                        .flatMap(entity -> {
                            if(req.isVerifyPermission()) {
                                return hdsDevicePositionDao.findOne(HdsDevicePositionQO.builder().positionCode(entity.getPositionCode()).build()).flatMap(positionToArea -> {
                                    HdsEmployeeTicketMappingQO build = HdsEmployeeTicketMappingQO
                                            .builder()
                                            .hotelCode(entity.getHotelCode())
                                            .employId(headerInfo.getUserId())
                                            .serviceCategory(entity.getServiceCategory())
                                            .acceptTicketStatus(AcceptTicketStatusEnum.ALLOW_ACCEPT.getCode())
                                            .areaCode(positionToArea.getAreaCode())
                                            .areaPublic(ObjectUtil.equal(positionToArea.getArea(), SystemConstant.PUBLIC_AREA_NAME))
                                            .build();
                                    return hdsEmployeeTicketMappingDao.findOne(build)
                                            .switchIfEmpty(Mono.error(
                                                    new BusinessException(ResultCode.INVALID_PARAMETER, "没有权限处理该工单")))
                                            .map(e -> entity);
                                });
                            }
                            return Mono.just(entity);
                        })
                        .flatMap(entity -> {
                            // 如果工单已经处理过，直接返回成功
                            if (Objects.equals(entity.getStatus(), 1)) {
                                return Mono.just(true);
                            }

                            // 更新工单状态
                            entity.setStatus(1);
                            entity.setCompletedBy(StringUtils.isBlank(req.getUserId()) ? headerInfo.getUserId() : req.getUserId());
                            entity.setCompletedByName(StringUtils.isBlank(req.getUserName()) ? headerInfo.getName() : req.getUserName());
                            entity.setCompletionTime(LocalDateTime.now());
                            entity.setReturnRoomId(req.getRtcRoomId());
                            return enrichCompletdName(entity)
                                    .flatMap(res -> serviceTicketRepository.save(res))
                                    .flatMap(this::addCache)
                                    .flatMap(info -> insertTicketLog(TicketActionTypeEnum.COMPLETE.getCode(), Collections.singletonList(entity.getTicketNo()),entity.getCompletedBy(),entity.getCompletedByName()).then(Mono.just(info)))
                                    .map(saved -> {
                                        // 异步处理推送逻辑，不阻塞主流程
                                        callBackBizService.sendTicketCommandRefresh(buildTicketQO(entity), false)
                                                .subscribeOn(Schedulers.boundedElastic()).subscribe();
                                        roomDeviceService.callFeedback(entity).subscribeOn(Schedulers.boundedElastic()).subscribe();
//                                        if (req.isPush()){
//                                            notifyServiceTicketFeedback(saved).subscribeOn(Schedulers.boundedElastic()).subscribe();
//                                        }
                                        return true;
                                    })
                                    .then(Mono.defer(() -> {
                                        // 更新工单完成统计指标
                                        return dailyTicketStatService.updateTicketCompleteStat(entity);
                                    }))
                                    .then(Mono.defer(() -> sendCallbackMessage(entity)))
                                    .then(Mono.just(true));
                        }))
                .doOnSuccess(v -> {
                    log.info("处理工单成功,req:{}",JacksonUtils.writeValueAsString(req));
                }).doOnError(e -> {
                    log.error("处理工单失败:req {}",JacksonUtils.writeValueAsString(req), e);
                }).onErrorResume(e -> {
                    if(e instanceof BusinessException){
                        return Mono.error(e);
                    } else {
                        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "系统繁忙，暂不可使用"));
                    }
                });

    }

    private Mono<Boolean> sendCallbackMessage(HdsServiceTicketEntity entity) {
        if (ObjectUtil.notEqual(entity.getServiceSubcategory(), CL_EMPTY_DIRTY.getCode())){
            return Mono.just(true);
        }
        EmptyCleaningCompletedDTO dto = EmptyCleaningCompletedDTO.builder()
                .ticketId(entity.getId().toString())
                .ticketNo(entity.getTicketNo())
                .guestRequest(entity.getGuestRequest())
                .completedAt(DateUtil.format(entity.getCompletionTime(),"yyyy-MM-dd HH:mm:ss"))
                .completedBy(entity.getCompletedBy())
                .completedByName(entity.getCompletedByName())
                .build();
        TicketMessageDTO messageBody = TicketMessageDTO.builder()
                .eventType(TicketEventTypeEnum.EMPTY_CLEANING_COMPLETED.getEventType())
                .data(dto)
                .build();
        return reactiveMessageSender.sendMessage(SystemConstant.TICKET_CREATE_EVENT_TOPIC, messageBody).thenReturn(Boolean.TRUE);
    }

    private Mono<HdsServiceTicketEntity> enrichCompletdName(HdsServiceTicketEntity entity) {
        int aLong = NumberUtils.toInt(entity.getCompletedBy(), 0);
        if (aLong == 0){
            return Mono.just(entity);
        }
        return employeeRepository.findById(aLong).defaultIfEmpty(new HdsEmployeeEntity()).map(employee -> {
            if (employee.getId() != null){
                entity.setCompletedByName(employee.getName());
            }
            return entity;
        });
    }

    public Mono<Boolean> insertTicketLog(String actionType, Collection<String> ticketLogs,String createdBy,String createdByName) {
        List<HdsTicketLogsEntity> logsEntities = buildTicketLogs(actionType, ticketLogs, createdBy, createdByName);
        return hdsTicketLogsDao.batchInsert(logsEntities)
                .onErrorResume(e -> {
                    log.error("insertTicketLog error {} {}", actionType, JacksonUtils.writeValueAsString(ticketLogs), e);
                    return Mono.just(false);
                }).subscribeOn(Schedulers.boundedElastic());
    }


    private  List<HdsTicketLogsEntity> buildTicketLogs(String actionType, Collection<String> ticketNoList, String createdBy, String createdByName) {
       return ticketNoList.stream().map(ticketNo->{
                       HdsTicketLogsEntity logEntity = new HdsTicketLogsEntity();
                       logEntity.setTicketNo(ticketNo);
                       logEntity.setActionType(actionType);
                       if (ObjectUtil.equal(actionType, TicketActionTypeEnum.EXPIRE.getCode())){
                           logEntity.setExpiredFlag(ExpiredFlagEnum.EXPIRED.getCode());
                       }
                       if (ObjectUtil.equal(actionType, TicketActionTypeEnum.COMPLETE.getCode())){
                           logEntity.setStatus(TicketStatus.COMPLETED.getCode());
                       }
                       logEntity.setCreatedBy(createdBy);
                       logEntity.setCreatedByName(createdByName);
                       logEntity.setUpdatedBy(createdBy);
                       logEntity.setUpdatedByName(createdByName);
                       return logEntity;
                   }).toList();
   }


    /**
     * 异步通知客房pad反馈
     */
//    private Mono<Boolean> notifyServiceTicketFeedback(HdsServiceTicketEntity saved) {
//        // 推送单个处理类型的工单反馈
//        String toRtcUserId = saved.getCreatedBy();
//        if (!saved.getCreatedBy().startsWith(DeviceTypeEnum.ROOM.getCode())){
//            toRtcUserId = DeviceInitRtcUtil.getRtcUserId(DeviceTypeEnum.ROOM.getCode(), saved.getDeviceId());
//        }
//        return roomDeviceService.getServiceTicketsFeedbackVo(Lists.newArrayList(saved),toRtcUserId).onErrorResume(e -> {
//            log.info("notifyServiceTicketFeedback error");
//            return Mono.just(false);
//        });
//    }


    public Mono<Boolean> createSosTicket(CreateTicketSimpleReq req) {
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getHotelCode()), "酒店编码不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getPositionCode()), "位置编码不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getDeviceId()), "设备Id为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getRtcRoomId()), "rtcRoomId 不能为空");

        CreateTicketReq createTicketReq = new CreateTicketReq();

        createTicketReq.setDeviceId(req.getDeviceId());
        createTicketReq.setRtcRoomId(req.getRtcRoomId());

        createTicketReq.setServiceType(ServiceType.EMERGENCY.name());
        createTicketReq.setServiceCategory(ServiceCategory.EMERGENCY.getCode());
        createTicketReq.setServiceSubcategory(ServiceSubcategory.EM_SOS.getCode());
        createTicketReq.setGuestRequest(translationConfig.translateSync(WormholeTranslationConfig.TranslationKeys.SOS_REQUEST, LANGUAGE_ZH_CN));
        createTicketReq.setGuestRequestEn(translationConfig.translateSync(WormholeTranslationConfig.TranslationKeys.SOS_REQUEST, LANGUAGE_EN_US));
        createTicketReq.setGuestRequestInd(translationConfig.translateSync(WormholeTranslationConfig.TranslationKeys.SOS_REQUEST, LANGUAGE_IN_ID));
        createTicketReq.setHotelCode(req.getHotelCode());
        createTicketReq.setPositionCode(req.getPositionCode());
        createTicketReq.setUserType(UserType.NORMAL.getCode());
        createTicketReq.setCreateType(CreateType.MANUAL.getCode());
        createTicketReq.setClientType(req.getClientType());
        createTicketReq.setUserId(req.getRtcUserId());
        createTicketReq.setUserName("delonix");
        createTicketReq.setClosedLoopLevel(req.getClosedLoopLevel());
        createTicketReq.setCompletedBy(req.getCompletedBy());
        createTicketReq.setCompletedByName(req.getCompletedByName());
        return createTicket(createTicketReq).then(Mono.just(true));
    }


    public Mono<Boolean> sendRefreshCommand(RefreshCommandReq req) {
        Preconditions.checkArgument(CollUtil.isNotEmpty(req.getHotelCodes()), "酒店编码不能为空");
        return Mono.just(req.getHotelCodes())
                .flatMapMany(Flux::fromIterable)
                .parallel()
                .runOn(Schedulers.boundedElastic())
                .flatMap(hotelCode -> employeeTicketMappingService.refreshAccountTicket(hotelCode, null))
                .sequential()
                .collectList()
                .then(Mono.just(true));
    }



    public Mono<Void> createTicketMessage(TicketCreatedMessage ticketCreatedMessage) {
        // 1. 对输入消息进行健壮性检查
        if (isInvalidMessage(ticketCreatedMessage)) {
            log.info("createTicketMessage: 消息为空或不包含工单/用户信息，已跳过处理。");
            return Mono.empty();
        }

        log.info("serviceTicketService.createTicketMessage: 收到延迟消息，准备检查工单状态。 Message: {}", JacksonUtils.writeValueAsString(ticketCreatedMessage));

        // 2. 从消息中提取所有有效的工单ID
        List<String> ticketIds = extractTicketIds(ticketCreatedMessage);
        if (ticketIds.isEmpty()) {
            log.info("createTicketMessage: 消息中未找到有效的工单ID。");
            return Mono.empty();
        }

        // 3. 根据工单ID查询数据库，获取最新状态并处理
        return hdsServiceTicketDao.findList(HdsTicketQO.builder().ticketIds(ticketIds).build())
                .flatMap(ticketsFromDb -> processPendingTickets(ticketsFromDb, ticketCreatedMessage));
    }


    /**
     * 检查传入的消息是否无效。
     */
    private boolean isInvalidMessage(TicketCreatedMessage message) {
        return Objects.isNull(message) ||
                Objects.isNull(message.getTicketCommandMessage()) ||
                CollUtil.isEmpty(message.getTicketCommandMessage().getTickets()) ||
                CollUtil.isEmpty(message.getUserIds());
    }

    /**
     * 从消息中提取工单ID列表。
     */
    private List<String> extractTicketIds(TicketCreatedMessage message) {
        return message.getTicketCommandMessage().getTickets().stream()
                .map(TicketCommandMessage.TicketSummary::getTicketId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();
    }

    /**
     * 处理待处理工单：过滤、构建消息、查找目标用户并发送。
     */
    private Mono<Void> processPendingTickets(List<HdsServiceTicketEntity> ticketsFromDb, TicketCreatedMessage originalMessage) {
        // 筛选出仍然是“未完成”状态的工单
        List<HdsServiceTicketEntity> pendingTickets = ticketsFromDb.stream()
                .filter(ticket -> Objects.equals(ticket.getStatus(), TicketStatus.PENDING.getCode()))
                .toList();

        if (pendingTickets.isEmpty()) {
            log.info("createTicketMessage: 所有相关工单都已完成，无需发送提醒。");
            return Mono.empty();
        }

        // 构建一个只包含未完成工单的新消息体
        TicketCommandMessage overdueMessage = buildOverdueMessage(pendingTickets, originalMessage.getTicketCommandMessage());
        if (overdueMessage.getTickets().isEmpty()) {
            log.info("createTicketMessage: 发现未完成工单，但在原始消息中找不到对应的摘要信息。");
            return Mono.empty();
        }

        log.info("createTicketMessage: 发现 {} 个未完成工单，准备发送超时提醒。", pendingTickets.size());

        // 查找可接收消息的用户并发送
        return findEligibleRtcUsersAndSend(pendingTickets, originalMessage.getUserIds(), overdueMessage);
    }

    /**
     * 构建只包含待处理工单的超时消息。
     */
    private TicketCommandMessage buildOverdueMessage(List<HdsServiceTicketEntity> pendingTickets, TicketCommandMessage originalCmdMsg) {
        Set<String> pendingTicketIds = pendingTickets.stream()
                .map(ticket -> String.valueOf(ticket.getId()))
                .collect(Collectors.toSet());

        List<TicketCommandMessage.TicketSummary> pendingSummaries = originalCmdMsg.getTickets().stream()
                .filter(summary -> pendingTicketIds.contains(summary.getTicketId()))
                .toList();

        return TicketCommandMessage.builder()
//                .refresh(true)
                .hasSound(true) // 超时提醒通常需要声音
                .hasSos(originalCmdMsg.getHasSos())
                .refreshIntervalSeconds(originalCmdMsg.getRefreshIntervalSeconds())
                .tickets(pendingSummaries)
                .build();
    }

    /**
     * 查找在线且有权限接单的用户，并向他们发送消息。
     */
    private Mono<Void> findEligibleRtcUsersAndSend(List<HdsServiceTicketEntity> pendingTickets, Collection<String> originalRtcUserIds, TicketCommandMessage overdueMessage) {

        // 查询在线设备
        HdsDeviceQO onlineDeviceQO = HdsDeviceQO.builder()
                .rtcUserIds(originalRtcUserIds)
                .deviceStatus(DeviceStatusEnum.ONLINE.getCode())
                .deviceAppType(Lists.newArrayList(FRONT.getCode(), FRONT_APP.getCode()))
                .build();

        return hdsDeviceDao.findList(onlineDeviceQO)
                .flatMap(onlineDevices -> {
                    if (CollUtil.isEmpty(onlineDevices)) {
                        log.info("createTicketMessage: 设备不在线，不发送超时提醒。 rtcUserIds: {}", originalRtcUserIds);
                        return Mono.empty();
                    }
                    // 过滤出有接单权限的员工，并获取其 rtcUserId
                    return filterUsersByAcceptanceStatus(onlineDevices)
                            .flatMap(finalRtcUserIds -> {
                                if (finalRtcUserIds.isEmpty()) {
                                    return Mono.empty(); // 日志已在内部方法打印
                                }
                                log.info("createTicketMessage: 准备向 {} 个在线且可接单的用户发送超时提醒。 OnlineRtcUserIds: {}",
                                        finalRtcUserIds.size(), finalRtcUserIds);
                                // 7. 向最终筛选出的用户发送消息
                                return rtcService.sendTicketCommandMessage(finalRtcUserIds, overdueMessage);
                            });
                });
    }

    /**
     * 从在线设备列表中，筛选出有接单权限的员工，并返回其RTC User ID列表。
     */
    private Mono<List<String>> filterUsersByAcceptanceStatus(List<HdsDeviceEntity> onlineDevices) {
        // 提取在线员工的ID
        List<String> onlineEmployeeIds = onlineDevices.stream()
                .map(HdsDeviceEntity::getUserId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();

        if (onlineEmployeeIds.isEmpty()) {
            log.info("createTicketMessage: 找到在线设备，但无有效的员工ID，不发送消息。");
            return Mono.just(Collections.emptyList());
        }

        // 查询这些在线员工是否可接单
        HdsEmployeeTicketMappingQO mappingQO = HdsEmployeeTicketMappingQO.builder()
                .employIdList(onlineEmployeeIds)
                .acceptTicketStatus(AcceptTicketStatusEnum.ALLOW_ACCEPT.getCode())
                .build();

        return hdsEmployeeTicketMappingDao.findList(mappingQO)
                .map(allowedMappings -> {
                    if (CollUtil.isEmpty(allowedMappings)) {
                        log.info("createTicketMessage: 在线用户均未开启接单权限，不发送超时提醒。 EmployeeIds: {}", onlineEmployeeIds);
                        return Collections.<String>emptyList();
                    }

                    // 获取可接单的员工ID集合
                    Set<String> allowedEmployeeIds = allowedMappings.stream()
                            .map(HdsEmployeeTicketMappingEntity::getEmployeeId)
                            .collect(Collectors.toSet());

                    // 从在线设备中，根据可接单的员工ID，筛选出最终要发送的RTC User ID
                    List<String> finalRtcUserIds = onlineDevices.stream()
                            .filter(device -> allowedEmployeeIds.contains(device.getUserId()))
                            .map(HdsDeviceEntity::getRtcUserId)
                            .filter(StringUtils::isNotBlank)
                            .distinct()
                            .toList();

                    if (finalRtcUserIds.isEmpty()) {
                        log.info("createTicketMessage: 在线且可接单的用户没有有效的RTCUserID，不发送消息。 AllowedEmployeeIds: {}", allowedEmployeeIds);
                    }
                    return finalRtcUserIds;
                });
    }
}