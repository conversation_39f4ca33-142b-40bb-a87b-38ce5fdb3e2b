package com.wormhole.hotelds.api.hotel.web.service;

import com.wormhole.agent.client.chat.client.AgentApiClient;
import com.wormhole.agent.client.chat.params.TtsRequest;
import com.wormhole.hotelds.api.hotel.config.TtsProperties;
import com.wormhole.hotelds.api.hotel.storage.config.BucketProperties;
import com.wormhole.storage.model.StorageParams;
import com.wormhole.storage.service.CosObjectStorageService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Service
@Slf4j
public class TtsService {
    @Resource
    private AgentApiClient agentApiClient;

    @Resource
    private BucketProperties bucketProperties;

    @Resource
    @Lazy
    private CosObjectStorageService cosObjectStorageService;

    @Resource
    private TtsProperties ttsProperties;

    /**
     * 将文本转换为语音，上传后返回可播放的URL
     *
     * @param text 需要转换的文本
     * @return 包含音频URL的Mono，如果失败则返回空字符串的Mono
     */
    public Mono<String> generateAndUploadAudio(String text, String language) {
        if (StringUtils.isBlank(text)) {
            return Mono.just(StringUtils.EMPTY);
        }

        log.info("开始为文本生成语音: '{}'", text);

        // 1. 准备TTS请求
        TtsRequest request = TtsRequest.builder()
                .text(text)
                .voice(ttsProperties.getVoice())
                .format(ttsProperties.getFormat())
                .speed(ttsProperties.getSpeed())
                .language(StringUtils.isNotBlank(language)?language: ttsProperties.getDefaultLanguage())
                .build();

        // 2. 调用TTS API获取语音数据流
        return agentApiClient.ttsSynthesizeStreamWithProvider(ttsProperties.getProvider(), request)
                .flatMapMany(responseEntity -> {
                    // 检查HTTP响应状态码
                    if (!responseEntity.getStatusCode().is2xxSuccessful()) {
                        log.error("TTS服务返回非成功状态码: {}", responseEntity.getStatusCode());
                        // 返回一个错误信号，终止后续流程
                        return Flux.error(new RuntimeException("TTS service failed with status: " + responseEntity.getStatusCode()));
                    }
                    // 获取响应体中的音频流 Flux<DataBuffer>
                    return responseEntity.getBody();
                })
                // 3. 将语音数据流上传到COS
                .as(this::uploadAudioStreamToCos)
                .doOnSuccess(url -> log.info("语音生成并上传成功, URL: {}", url))
                .doOnError(e -> log.error("语音生成或上传失败, text: '{}'", text, e))
                .onErrorReturn(StringUtils.EMPTY); // 关键：发生错误时返回空字符串，避免中断主流程

    }

    /**
     * 将响应式的音频流(Flux<DataBuffer>)上传到COS
     */
    private Mono<String> uploadAudioStreamToCos(Flux<DataBuffer> dataStream) {
        // 为上传对象生成一个唯一的文件名
        String uniqueId = UUID.randomUUID().toString().substring(0, 8);
        String fileName = String.format("%s%d_%s%s", bucketProperties.getAudioPrefix(), System.currentTimeMillis(), uniqueId, bucketProperties.getAudioSuffix());
        // 1. 将数据流聚合成单个DataBuffer，以便获取InputStream
        return DataBufferUtils.join(dataStream)
                .flatMap(dataBuffer -> {
                    // 2. 准备上传所需的参数
                    StorageParams params = StorageParams.builder()
                            .bucketName(bucketProperties.getCommonBucketName())
                            .objectKey(fileName)
                            .build();

                    // 3. 调用COS服务上传对象。asInputStream()会消耗DataBuffer，只能调用一次。
                    return cosObjectStorageService.putObject(params, dataBuffer.asInputStream(), null)
                            .doOnSuccess(p -> log.info("成功上传音频文件到COS: {}", fileName))
                            .doOnError(e -> log.error("上传音频文件到COS失败: {}", fileName, e))
                            // 4. 上传成功后，获取该对象的公开访问URL
                            // 假设cosObjectStorageService提供了获取URL的方法
                            .then(Mono.just(cosObjectStorageService.getObjectUrl(params)));
                });
    }

}
