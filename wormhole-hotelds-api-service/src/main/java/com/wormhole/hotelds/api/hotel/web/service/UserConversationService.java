package com.wormhole.hotelds.api.hotel.web.service;

import com.wormhole.agent.core.model.entity.UserConversationEntity;
import com.wormhole.agent.core.model.entity.UserConversationFieldEnum;
import com.wormhole.common.enums.SourcePlatform;
import com.wormhole.hotelds.api.hotel.constant.YesNoEnum;
import jodd.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/4/24 19:39
 */
@Service
@Slf4j
public class UserConversationService {

    @Value("${wormhole.ticket.cleanHour:14}")
    private Integer hourConfig;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;



    public Mono<List<UserConversationEntity>> findYesterdayHotelConversations(Long idx, int pageSize) {

        // 设置结束时间为当前时间
        Calendar endTime = Calendar.getInstance();
        endTime.setTime(new Date());

        // 设置结束时间为今天的指定小时
        endTime.set(Calendar.HOUR_OF_DAY, hourConfig);
        endTime.set(Calendar.MINUTE, 0);
        endTime.set(Calendar.SECOND, 0);
        endTime.set(Calendar.MILLISECOND, 0);

        // 设置开始时间为昨天的同一时间点
        Calendar startTime = (Calendar) endTime.clone();
        startTime.add(Calendar.DAY_OF_MONTH, -1);

        // 转换为LocalDateTime，这是R2DBC更好支持的日期时间类型
        LocalDateTime startDateTime = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime endDateTime = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        // 构建时间范围查询条件
        Criteria criteria = Criteria.empty();
//              .where(UserConversationFieldEnum.created_at.name()).greaterThanOrEquals(startDateTime)
        criteria = criteria.and(UserConversationFieldEnum.created_at.name()).lessThan(endDateTime)
                .and(UserConversationFieldEnum.source.name()).is(SourcePlatform.HOTEL.getCode())
                .and(UserConversationFieldEnum.id.name()).greaterThan(idx);

        // 构建查询并执行
        return r2dbcEntityTemplate.select(
                        Query.query(criteria).sort(Sort.by(Sort.Direction.ASC, UserConversationFieldEnum.id.name())).limit(pageSize),
                        UserConversationEntity.class)
                .collectList();
    }

    /**
     * 将会话标记为无效
     */
    public Mono<Void> markConversationAsInvalid(UserConversationEntity entity) {
        entity.setRowStatus(0);
        return r2dbcEntityTemplate.update(entity)
                .doOnError(e -> log.error("更新会话状态异常, conversationId={}, error={}",
                        entity.getConversationId(), e.getMessage(), e))
                .then();
    }

    public Mono<List<UserConversationEntity>> findByHotelAndPosition(String hotelCode, String positionCode) {
        // 构建时间范围查询条件
        Criteria criteria = Criteria.where(UserConversationFieldEnum.hotel_code.name()).is(hotelCode)
                .and(UserConversationFieldEnum.position_code.name()).is(positionCode)
                .and(UserConversationFieldEnum.source.name()).is(SourcePlatform.HOTEL.getCode())
                .and("row_status").is(YesNoEnum.YES.getCode());
        return r2dbcEntityTemplate.select(Query.query(criteria), UserConversationEntity.class).collectList();
    }

    public Mono<Boolean> markConversationListAsInvalid(List<UserConversationEntity> conversation) {
        if (CollectionUtils.isEmpty( conversation)){
            return Mono.just(true);
        }
        List<Long> idList = conversation.stream().map(UserConversationEntity::getId).toList();
        Criteria criteria = Criteria.where(UserConversationFieldEnum.id.name()).in(idList);
        Update rowStatus = Update.update("row_status", YesNoEnum.NO.getCode());
        return r2dbcEntityTemplate.update(UserConversationEntity.class)
                .matching(Query.query(criteria))
                .apply(rowStatus)
                .map(updated -> updated > 0);
    }
}
