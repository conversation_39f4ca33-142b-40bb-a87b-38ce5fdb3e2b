package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.extra.spring.SpringUtil;
import com.google.common.base.Preconditions;
import com.wormhole.channel.consts.vo.RtcCallbackInfoVO;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.storage.config.BucketProperties;
import com.wormhole.hotelds.api.hotel.storage.constant.StorageConstants;
import com.wormhole.hotelds.api.hotel.util.SHAUtil;
import com.wormhole.hotelds.api.hotel.config.WechatProperties;
import com.wormhole.hotelds.api.hotel.constant.WechatConstant;
import com.wormhole.hotelds.api.hotel.web.dao.HdsChatUserDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDevicePositionDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsHotelInfoDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsWechatQrCodeDao;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsDevicePositionQO;
import com.wormhole.hotelds.api.hotel.web.model.req.LocationReq;
import com.wormhole.hotelds.api.hotel.web.model.req.WechatInitReq;
import com.wormhole.hotelds.api.hotel.web.model.res.WechatInitRes;
import com.wormhole.hotelds.api.hotel.web.model.res.WechatJsapiTicketRes;
import com.wormhole.hotelds.api.hotel.web.model.res.WechatLocationRes;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.core.utils.DeviceInitRtcUtil;
import com.wormhole.poi.req.AmapDistanceReq;
import com.wormhole.poi.service.AmapService;
import com.wormhole.storage.model.StorageParams;
import com.wormhole.storage.service.CosObjectStorageService;
import com.wormhole.storage.service.OssObjectStorageService;
import com.wormhole.wechat.constant.WechatMiniAppConstants;
import com.wormhole.wechat.req.WechatQRCodeReq;
import com.wormhole.wechat.res.WechatJsapiRes;
import com.wormhole.wechat.service.WechatMiniAppService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/4/9 14:58
 */
@Slf4j
@Service
public class WechatUserService {


    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private AmapService amapService;

    @Resource
    @Lazy
    private OssObjectStorageService objectStorageService;

    @Resource
    @Lazy
    private CosObjectStorageService cosObjectStorageService;

    @Resource
    private WechatMiniAppService wechatMiniAppService;

    @Resource
    private BucketProperties bucketProperties;

    @Resource
    private WechatProperties wechatProperties;

    @Resource
    private DeviceBindService deviceBindService;

    @Resource
    private HdsWechatQrCodeDao hdsWechatQrCodeDao;

    @Resource
    private HdsChatUserDao hdsChatUserDao;

    @Resource
    private HdsHotelInfoDao hdsHotelInfoDao;

    @Resource
    private HdsDevicePositionDao hdsDevicePositionDao;


    public Mono<WechatLocationRes> validateUserLocation(LocationReq request) {
        log.info("validateUserLocation request: {}", JacksonUtils.writeValueAsString(request));

        Preconditions.checkArgument(StringUtils.isNoneBlank(request.getHotelCode()), "hotelCode must not be blank");
        Preconditions.checkArgument(StringUtils.isNoneBlank(request.getLatitude()), "latitude must not be blank");
        Preconditions.checkArgument(StringUtils.isNoneBlank(request.getLongitude()), "longitude must not be blank");

        return hdsHotelInfoDao.queryByHotelCode(request.getHotelCode())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "酒店为空")))
                .flatMap(hotel -> {
                    return calculateDistance(request, hotel)
                            .map(distance -> buildLocationResponse(hotel, distance));
                });
    }

    private Mono<String> calculateDistance(LocationReq req, HdsHotelInfoEntity hotel) {
        if (StringUtils.isBlank(hotel.getGaodeLatitude()) || StringUtils.isBlank(hotel.getGaodeLongitude())) {
            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "酒店经纬度为空"));
        }

        String origins = req.getLongitude() + "," + req.getLatitude();
        String destinations = hotel.getGaodeLongitude() + "," + hotel.getGaodeLatitude();
        return amapService.queryDistance(AmapDistanceReq.builder()
                        .origins(origins)
                        .destination(destinations)
                        .build())
                .map(amapDistanceResp -> {
                    if (Objects.equals(amapDistanceResp.getStatus(), 0)) {
                        throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "高德地图服务异常");
                    }
                    return amapDistanceResp.getResults().get(0).getDistance();
                });
    }

    private WechatLocationRes buildLocationResponse(HdsHotelInfoEntity hotel, String distanceStr) {
        BigDecimal distance = new BigDecimal(distanceStr);
        BigDecimal maxAllowedDistance = new BigDecimal(wechatProperties.getDistance());
        boolean inHotelArea = distance.compareTo(maxAllowedDistance) <= 0;

        return WechatLocationRes.builder()
                .hotelCode(hotel.getHotelCode())
                .distance(distanceStr)
                .inHotelArea(inHotelArea)
                .hotelLongitude(hotel.getGaodeLongitude())
                .hotelLatitude(hotel.getGaodeLatitude())
                .build();
    }


    public Mono<Tuple2<String, String>> generateQRCodeAndUpload(String hotelCode, String positionCode) {
        log.info("generateQRCodeAndUpload request: {} {}", hotelCode, positionCode);

        Preconditions.checkArgument(StringUtils.isNoneBlank(hotelCode), "hotelCode must not be blank");
        Preconditions.checkArgument(StringUtils.isNoneBlank(positionCode), "positionCode must not be blank");

        return hdsWechatQrCodeDao.queryByHotelCode(hotelCode, positionCode)
                .switchIfEmpty(HeaderUtils.getHeaderInfo().flatMap(headerInfo ->
                        hdsWechatQrCodeDao.insertWechatQrCodeEntity(buildWechatQrCodeEntity(hotelCode, positionCode, headerInfo))))
                .flatMap(this::handleQRCodeIfNecessary);
    }
    private HdsWechatQrCodeEntity buildWechatQrCodeEntity(String hotelCode, String positionCode, HeaderUtils.HeaderInfo headerInfo) {
        HdsWechatQrCodeEntity qrCodeEntity = new HdsWechatQrCodeEntity();
        qrCodeEntity.setHotelCode(hotelCode);
        qrCodeEntity.setPositionCode(positionCode);
        qrCodeEntity.setCreatedAt(LocalDateTime.now());
        qrCodeEntity.setUpdatedAt(LocalDateTime.now());
        qrCodeEntity.setCreatedBy(StringUtils.isBlank(headerInfo.getUserId()) ? "system" : headerInfo.getUserId());
        qrCodeEntity.setCreatedByName(StringUtils.isBlank(headerInfo.getUsername()) ? "system" : headerInfo.getUsername());
        qrCodeEntity.setRowStatus(1);
        log.info("generateQRCodeAndUpload qrCodeEntity: {} headerInfo {}", JacksonUtils.writeValueAsString(qrCodeEntity), JacksonUtils.writeValueAsString(headerInfo));
        return qrCodeEntity;
    }

    private Mono<Tuple2<String, String>> handleQRCodeIfNecessary(HdsWechatQrCodeEntity entity) {
        // t1 微信二维码
        Mono<String> qrCodeUrlMono = StringUtils.isNotBlank(entity.getQrCodeUrl())
                ? Mono.just(cosObjectStorageService.getObjectUrl(
                StorageParams.builder()
                        .bucketName(bucketProperties.getCommonBucketName())
                        .objectKey(entity.getQrCodeUrl())
                        .build()))
                : generateAndUploadQRCode(entity)
                .map(meta -> cosObjectStorageService.getObjectUrl(meta));

        // t2 普通二维码
        Mono<String> normalQrCodeUrlMono = StringUtils.isNotBlank(entity.getNormalQrCodeUrl())
                ? Mono.just(cosObjectStorageService.getObjectUrl(
                StorageParams.builder()
                        .bucketName(bucketProperties.getCommonBucketName())
                        .objectKey(entity.getNormalQrCodeUrl())
                        .build()))
                : Mono.justOrEmpty(StringUtils.EMPTY);

        return Mono.zip(qrCodeUrlMono, normalQrCodeUrlMono);
    }

    private Mono<StorageParams> generateAndUploadQRCode(HdsWechatQrCodeEntity entity) {
        return Mono.just(determineEnvVersion())
                .flatMap(envVersion -> generateQRCode(entity.getId(), envVersion))
                .flatMap(qrCode -> processAndUploadQRCode(qrCode, entity));
    }

    private String determineEnvVersion() {
        String activeProfile = SpringUtil.getActiveProfile();
        if ("test".equals(activeProfile)) {
            return WechatMiniAppConstants.TRIAL_ENV_VERSION;
        }
        return WechatMiniAppConstants.RELEASE_ENV_VERSION;
    }

    private Mono<byte[]> generateQRCode(Long entityId, String envVersion) {
        WechatQRCodeReq request = WechatQRCodeReq.builder()
                .scene(WechatConstant.WECHAT_QR_CODE_URL_PREFIX + entityId.toString())
                .envVersion(envVersion)
                .build();
        // todo del
        log.info("generateQRCode request: {}", JacksonUtils.writeValueAsString(request));
        return wechatMiniAppService.createQRCode(request);
    }

    private Mono<StorageParams> processAndUploadQRCode(byte[] qrCode, HdsWechatQrCodeEntity entity) {
        if (isErrorResponse(qrCode)) {
            String errMsg = new String(qrCode);
            log.error("微信二维码生成失败，返回内容: {}", errMsg);
            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "微信二维码生成失败"));
        }

        InputStream inputStream = new ByteArrayInputStream(qrCode);
        return uploadQRCodeAndUpdateEntity(entity, inputStream);
    }

    private Mono<StorageParams> uploadQRCodeAndUpdateEntity(HdsWechatQrCodeEntity entity, InputStream qrCodeStream) {
        return putObject(entity.getHotelCode(), entity.getPositionCode(), qrCodeStream)
                .flatMap(storageParams -> {
                    entity.setQrCodeUrl(storageParams.getObjectKey());
                    return r2dbcEntityTemplate.update(entity).thenReturn(storageParams);
                });
    }

//    private Mono<String> generateAndUploadQRCode(HdsWechatQrCodeEntity entity) {
//        return wechatMiniAppService.createQRCode(WechatConstant.WECHAT_QR_CODE_URL_PREFIX+ entity.getId().toString())
//                .flatMap(qrCode -> {
//                    if (isErrorResponse(qrCode)) {
//                        String errMsg = new String(qrCode);
//                        log.error("微信二维码生成失败，返回内容: {}", errMsg);
//                        return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR,"微信二维码生成失败"));
//                    }
//                    InputStream inputStream = new ByteArrayInputStream(qrCode);
//                    return putObject(entity.getHotelCode(), entity.getPositionCode(), inputStream)
//                            .flatMap(url -> {
//                                entity.setQrCodeUrl(url.getObjectKey());
//                                return r2dbcEntityTemplate.update(entity).thenReturn(url);
//                            })
//                            .map(meta -> objectStorageService.generatePresignedUrl(meta));
//                });
//    }


    private Mono<StorageParams> putObject(String hotelCode, String positionCode, InputStream inputStream) {
        String fileName = String.format(StorageConstants.WECHAT_QR_CODE_URL,
                hotelCode, positionCode, System.currentTimeMillis());
        StorageParams params = StorageParams.builder()
                .bucketName(bucketProperties.getCommonBucketName())
                .objectKey(fileName)
                .build();
        return cosObjectStorageService.putObject(params, inputStream, null)
                .thenReturn(params);
    }

    private boolean isErrorResponse(byte[] data) {
        String content = new String(data);
        return content.contains("errcode");
    }


    public Mono<WechatInitRes> initWechat(WechatInitReq req) {
        log.info("initWechat request: {}", JacksonUtils.writeValueAsString(req));
        return hdsWechatQrCodeDao.findQrCodeByScene(req.getScene())
                .flatMap(qr -> updateUserWithQrCodeInfo(req.getUserId(), qr))
                .flatMap(res->enrichWechatInitRes(res,req.getLanguage()));
    }

    // 1. 查询二维码信息
    // 2. 查询用户并更新其 hotelCode、positionCode
    private Mono<WechatInitRes> updateUserWithQrCodeInfo(String userId, HdsWechatQrCodeEntity qrCode) {
        return hdsChatUserDao.queryById(userId)
                .switchIfEmpty(Mono.error(new BusinessException("HOTEL-DS-API-002", "用户不存在")))
                .flatMap(user -> {
                    user.setHotelCode(qrCode.getHotelCode());
                    user.setPositionCode(qrCode.getPositionCode());
                    return r2dbcEntityTemplate.update(user);
                })
                .flatMap(this::buildBaseWechatInitResFromUser);
    }

    private Mono<WechatInitRes> buildBaseWechatInitResFromUser(HdsChatUserEntity updated) {
        String rtcUserId = DeviceInitRtcUtil.getRtcUserId(DeviceTypeEnum.WECHAT_MINI_APP.getCode(), updated.getAccount());
        return buildPositionName(updated)
                .defaultIfEmpty("") // 没查到时兜底空字符串
                .map(positionName -> WechatInitRes.builder()
                        .deviceType(DeviceTypeEnum.WECHAT_MINI_APP.getCode())
                        .deviceId(updated.getAccount())
                        .hotelCode(updated.getHotelCode())
                        .positionCode(updated.getPositionCode())
                        .positionName(positionName)
                        .rtcUserId(rtcUserId)
                        .callTimeOutSeconds(30)
                        .build()
                );
    }

    private Mono<String> buildPositionName(HdsChatUserEntity updated) {
        HdsDevicePositionQO hdsDevicePositionQO = HdsDevicePositionQO.builder().positionCode(updated.getPositionCode()).build();
        return hdsDevicePositionDao.findOne(hdsDevicePositionQO)
                .map(HdsDevicePositionEntity::getPositionName)
                .switchIfEmpty(Mono.justOrEmpty(""))
                .onErrorResume(e -> {
                    log.info("buildPositionName failed for hotelCode={}, positionCode={}", updated.getHotelCode(), updated.getPositionCode());
                    return Mono.just("");
                });
    }

    // 4. 构建返回对象
    private Mono<WechatInitRes> enrichWechatInitRes(WechatInitRes res, String language) {
        return Mono.zip(deviceBindService.getRtcTokenInfo(res.getRtcUserId()), deviceBindService.checkAndGetHotel(res.getHotelCode()))
                        .map(tuple -> {
                            RtcCallbackInfoVO rtcInfo = tuple.getT1();
                            HdsHotelInfoEntity hotel = tuple.getT2();
                            res.setRtcToken(rtcInfo.getToken());
                            res.setRtcAppId(rtcInfo.getRtcAppId());
                            res.setSecret(rtcInfo.getSecret());
                            res.setServerCallBackUrl(rtcInfo.getCallBackUrl());
                            res.setRtcManufacturer("volcengine");
                            res.setBotCode(wechatProperties.getChatBot(language));
                            res.setSosSwitch(hotel.getSosSwitch());
                            res.setDialogSwitch(hotel.getDialogSwitch());
                            res.setCallCommand(hotel.getCallCommand());
                            res.setHotelName(hotel.getHotelName());
                            return res;
                        });
    }


    public Mono<WechatJsapiTicketRes> getJsapiTicket(String appId, String shareUrl) {
        log.info("getJsapiTicket request: {}", shareUrl);
        return wechatMiniAppService.getJSSDKConfig(appId)
                .flatMap(jsapiRes -> buildJsapiTicketRes(jsapiRes, shareUrl));
    }

    private Mono<WechatJsapiTicketRes> buildJsapiTicketRes(WechatJsapiRes jsapiRes, String shareUrl) {
        return Mono.justOrEmpty(jsapiRes.getTicket())
                .switchIfEmpty(Mono.error(new BusinessException("HOTEL-DS-API-003", "获取jsapi ticket失败")))
                .flatMap(ticket -> {
                    String timestamp = Long.toString(System.currentTimeMillis() / 1000);
                    String nonceStr = UUID.randomUUID().toString();

                    return Mono.fromCallable(() -> buildJSSDKSignature(ticket, timestamp, nonceStr, shareUrl))
                            .map(signature -> WechatJsapiTicketRes.builder()
                                    .jsapiTicket(ticket)
                                    .nonceStr(nonceStr)
                                    .timestamp(timestamp)
                                    .signature(signature)
                                    .build())
                            .onErrorMap(e -> new BusinessException("HOTEL-DS-API-004", "生成签名失败", e));
                });
    }

    public String buildJSSDKSignature(String ticket, String timestamp, String nonceStr, String url) throws Exception {
        String orderedString = "jsapi_ticket=" + ticket
                + "&noncestr=" + nonceStr
                + "&timestamp=" + timestamp
                + "&url=" + url;
        return SHAUtil.sha1(orderedString);
    }

}
