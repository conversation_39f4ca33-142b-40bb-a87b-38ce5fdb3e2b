package com.wormhole.hotelds.api.hotel.web.service.handler;

import cn.hutool.core.util.ObjectUtil;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.enums.RtcCallStatusEnum;
import com.wormhole.channel.consts.message.CallInfoMessage;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.constant.CallOperatorTypeEnum;
import com.wormhole.hotelds.api.hotel.constant.SystemConstant;
import com.wormhole.hotelds.api.hotel.web.dao.HdsHotelInfoDao;
import com.wormhole.hotelds.api.hotel.web.service.CallBackBizService;
import com.wormhole.hotelds.core.utils.DeviceInitRtcUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Objects;

@Component
@Slf4j
public class GuestRoomSosCallCancelHandler implements RtcCallbackHandler {

    @Autowired
    private CallBackBizService callBackBizService;

    @Autowired
    private HdsHotelInfoDao hdsHotelInfoDao;

    @Override
    public boolean canHandle(CallInfoMessage request) {
        return (Objects.equals(request.getCallStatus(), RtcCallStatusEnum.CANCELLED.getCode())
                || Objects.equals(request.getCallStatus(), RtcCallStatusEnum.REJECTED.getCode()))
                && Objects.equals(request.getInitiatorType(), CallOperatorTypeEnum.GUEST_ROOM.getCode())
                && !request.isHasAiParticipant() && !request.getRtcRoomId().startsWith(SystemConstant.RTC_ROOM_ID_PREFIX);
    }

    @Override
    public Mono<Void> handle(CallInfoMessage request) {
        log.info("GuestRoomSosCallCancelHandler.handle,request:{}", JacksonUtils.writeValueAsString(request));
        return handleSosTicket(request)
                .doOnError(e -> log.error("处理SOS工单异常", e))
                .doOnSuccess(aVoid -> log.info("处理SOS工单成功"))
                .onErrorResume(e -> Mono.empty());
    }

    private Mono<Void> handleSosTicket(CallInfoMessage request) {
        String hotelCode = DeviceInitRtcUtil.getHotelCodeFromRtcRoomId(request.getRtcRoomId());
        return hdsHotelInfoDao.queryByHotelCode(hotelCode)
                .flatMap(hotelInfoEntity -> {
                    if (hotelInfoEntity == null || ObjectUtil.equal(hotelInfoEntity.getCallCommand(), Instructions.TRANSFER_TO_HUMAN.getCode())){
                        return Mono.empty();
                    }
            return callBackBizService.sosCallCancelHandleTicket(request);
        });
    }

}