package com.wormhole.hotelds.plugin.config;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-05-12 16:00:02
 * @Description:
 */
@ConfigurationProperties(prefix = "wormhole.plugin.comment")
@Data
public class PluginCommentProperties {

    /**
     * 月度报表数据比较过去月份
     */
    private Integer monthsToCompare = 6;

    /**
     * 周报数据比较过去 4周
     */
    private Integer weeksToCompare = 4;

    private Integer yearToCompare = 1;


    private Integer priceCollectionInterval = 60;

    @NestedConfigurationProperty
    private OutCall outCall = new OutCall();

    @Data
    public static class OutCall {
        //暂不支持境外电话呼出，未消耗体力值
        private String tips = "暂不支持境外电话呼出";

        private Set<String> excludeNumberPrefix = CollUtil.newHashSet(
                "00"
        );

        /**
         * 白名单
         * 当有白名单的时候，就只能走白名单的
         */
        private Set<String> whilePhoneList = new HashSet<>();

        @NestedConfigurationProperty
        private ShuKe shuKe;

        @NestedConfigurationProperty
        private JdCloud jdCloud = new JdCloud();

        public boolean isExcludeNumberPrefix(String number) {
            for (String prefix : excludeNumberPrefix) {
                if (number.startsWith(prefix)) {
                    return true;
                }
            }
            return false;
        }

        public boolean canOutCall(String number) {
            if (CollUtil.isEmpty(whilePhoneList)) {
                return true;
            }
            return whilePhoneList.contains(number);
        }
    }

    @Data
    public static class JdCloud {

        private String userPin = "delonix_dev";

        /**
         * 公共的任务id，第一次呼叫使用
         */
        private Integer commonJobId = 6082618;

        /**
         * 最多重新呼叫5次
         */
        private Integer maxRecallCount = 5;
        /**
         * 重呼的任务id，重试时使用
         */
        private List<Integer> reCallJobIds = CollUtil.toList(
                6079667, 6079668, 6055825, 6103656, 6055831
        );

    }


    @Data
    public static class ShuKe {

        private String token = "98ae481b8df211eb900c00163e0ec7ee";

        private String domain = "https://ai21.ytcall.net";

        private Integer asrId = 647;

        private Integer batchId = 30918;

        private String callbackUrl = "https://dev-gateway.bwagent.net/hotelds-api/plugin/out_call/shu_ke/callback";


    }

}
