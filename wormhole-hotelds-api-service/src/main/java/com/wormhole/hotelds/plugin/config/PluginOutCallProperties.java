package com.wormhole.hotelds.plugin.config;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-08-11 14:00:36
 * @Description:
 */
@Data
@ConfigurationProperties(prefix = "wormhole.out-call")
public class PluginOutCallProperties {

    /**
     * 京东云外呼配置
     */
    @NestedConfigurationProperty
    private JdCloud jdCloud = new JdCloud();

    private Map<String, String> excludeNumberPrefixMap = Map.of(
            "00", "暂不支持境外电话呼出"
    );

    /**
     * 白名单
     * 当有白名单的时候，就只能走白名单的
     */
    private Set<String> whilePhoneList = new HashSet<>();


    public boolean isExcludeNumberPrefix(String number) {
        for (String prefix : excludeNumberPrefixMap.keySet()) {
            if (number.startsWith(prefix)) {
                return true;
            }
        }
        return false;
    }

    public boolean canOutCall(String number) {
        if (CollUtil.isEmpty(whilePhoneList)) {
            return true;
        }
        return whilePhoneList.contains(number);
    }


    @Data
    public static class JdCloud {

        private Integer botId = 5001982;

        private String userPin = "delonix_dev";

        /**
         * 公共的任务id，第一次呼叫使用
         */
        private Integer commonJobId = 6082618;

        /**
         * 最多重新呼叫5次
         */
        private Integer maxRecallCount = 5;

    }
}
