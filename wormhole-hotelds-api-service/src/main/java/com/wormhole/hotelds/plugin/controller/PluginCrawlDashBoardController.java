package com.wormhole.hotelds.plugin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.plugin.model.qo.IndicatorTrendQO;
import com.wormhole.hotelds.plugin.model.qo.PluginCrawlDataDetailQO;
import com.wormhole.hotelds.plugin.model.qo.PluginCrawlDataSummaryQO;
import com.wormhole.hotelds.plugin.model.qo.PluginDataCollectQO;
import com.wormhole.hotelds.plugin.model.vo.PluginCrawlDataDetailDataVO;
import com.wormhole.hotelds.plugin.model.vo.PluginCrawlDataIndicatorTrend;
import com.wormhole.hotelds.plugin.model.vo.PluginCrawlDataSummaryVO;
import com.wormhole.hotelds.plugin.service.PluginCrawlDataDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 插件采集数据仪表板控制器
 *
 * <AUTHOR>
 * @date 2025/8/26
 */
@RequestMapping(value = "/plugin/crawl_data_dashboard")
@RestController
@Slf4j
@RequiredArgsConstructor
public class PluginCrawlDashBoardController {

    private final PluginCrawlDataDashboardService pluginCrawlDataDashboardService;

    /**
     * 获取插件采集数据统计
     *
     * @param qo 查询条件
     * @return 统计结果
     */
    @PostMapping("/summary")
    public Mono<Result<PluginCrawlDataSummaryVO>> getSummary(@RequestBody PluginCrawlDataSummaryQO qo) {
        log.info("接收到获取插件采集数据统计请求: {}", qo);
        return pluginCrawlDataDashboardService.getSummaryStatistics(qo)
                .flatMap(Result::success)
                .doOnSuccess(result -> log.info("插件采集数据统计请求处理完成"))
                .doOnError(error -> log.error("插件采集数据统计请求处理失败: {}", qo, error));
    }

    @PostMapping("/detail_data")
    public Mono<Result<PageResult<PluginCrawlDataDetailDataVO>>> getDetailData(@RequestBody PluginCrawlDataDetailQO qo) {
        log.info("接收到获取插件采集数据详情请求: {}", qo);
        return pluginCrawlDataDashboardService.getDetailData(qo)
                .flatMap(Result::success)
                .doOnSuccess(result -> log.info("插件采集数据详情请求处理完成"))
                .doOnError(error -> log.error("插件采集数据详情请求处理失败: {}", qo, error));
    }
    @PostMapping("/get_indicator_trend")
    public Mono<Result<List<PluginCrawlDataIndicatorTrend>>> getIndicatorTrend(@RequestBody IndicatorTrendQO qo) {
        return pluginCrawlDataDashboardService.getIndicatorTrend(qo).flatMap(Result::success);
    }

    @PostMapping("/plugin_data_collect")
    public Mono<Result<Boolean>> pluginDataCollect(@RequestBody PluginDataCollectQO pluginDataCollectQO){
        return pluginCrawlDataDashboardService.pluginDataCollect(pluginDataCollectQO).flatMap(Result::success);
    }
}
