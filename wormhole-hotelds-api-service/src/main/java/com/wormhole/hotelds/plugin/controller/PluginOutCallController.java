package com.wormhole.hotelds.plugin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.plugin.model.dto.PluginOutCallDTO;
import com.wormhole.hotelds.plugin.model.qo.PluginOutCallQo;
import com.wormhole.hotelds.plugin.model.vo.PluginOrderTabVo;
import com.wormhole.hotelds.plugin.model.vo.PluginOutCallVO;
import com.wormhole.hotelds.plugin.service.PluginOutCallService;
import com.wormhole.outcall.core.enums.OutCallApiEnum;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/plugin/out_call")
public class PluginOutCallController {
    @Resource
    private PluginOutCallService pluginOutCallService;

    @PostMapping("/list")
    public Mono<Result<List<PluginOutCallVO>>> list(@RequestBody PluginOutCallDTO qo) {
        return pluginOutCallService.list(qo).flatMap(Result::success);
    }

    @PostMapping("/tab/list")
    public Mono<Result<List<PluginOrderTabVo>>> listTab(@RequestBody PluginOutCallDTO qo) {
        return pluginOutCallService.listTab(qo).flatMap(Result::success);
    }

    @PostMapping("/call")
    public Mono<Result<Boolean>> call(@RequestBody PluginOutCallQo qo) {
        return pluginOutCallService.call(qo).flatMap(Result::success);
    }

    @PostMapping("/{apiEnum}/callback")
    public Mono<Map<String,Object>> shuKeCallback(@PathVariable("apiEnum") OutCallApiEnum apiEnum, @RequestBody String json) {
        return pluginOutCallService.handleOutCallCallback(apiEnum, json);
    }


}
