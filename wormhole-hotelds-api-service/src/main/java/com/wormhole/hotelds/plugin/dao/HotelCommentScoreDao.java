package com.wormhole.hotelds.plugin.dao;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.hotelds.plugin.model.entity.HotelCommentScore;
import com.wormhole.hotelds.plugin.model.field.HotelCommentScoreField;
import com.wormhole.hotelds.plugin.model.qo.HotelCommentScoreQO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/24
 */
@Repository
public class HotelCommentScoreDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;




    public Mono<Long> setInValid(HotelCommentScoreQO hotelCommentScoreQO){
        Criteria criteria = getCriteria(hotelCommentScoreQO);
        Query query = Query.query(criteria);
        Update update = Update.update(HotelCommentScoreField.ROW_STATUS.getColumn(), RowStatusEnum.DELETE.getId());
        return r2dbcEntityTemplate.update(query,update,HotelCommentScore.class);
    }

    public Mono<HotelCommentScore> save(HotelCommentScore hotelCommentScore) {
        return r2dbcEntityTemplate.insert(hotelCommentScore);
    }

    public Mono<List<HotelCommentScore>> findList(HotelCommentScoreQO hotelCommentScoreQO){
        Criteria criteria = getCriteria(hotelCommentScoreQO);
        return r2dbcEntityTemplate.select(Query.query(criteria),HotelCommentScore.class).collectList();
    }

    private Criteria getCriteria(HotelCommentScoreQO hotelCommentScoreQO){
        Criteria criteria = Criteria.empty();
        if(StringUtils.isNotBlank(hotelCommentScoreQO.getHotelCode())){
            criteria =  criteria.and(Criteria.where(HotelCommentScoreField.HOTEL_CODE.getColumn()).is(hotelCommentScoreQO.getHotelCode()));
        }
        if(CollUtil.isNotEmpty(hotelCommentScoreQO.getHotelCodes())){
            criteria =  criteria.and(Criteria.where(HotelCommentScoreField.HOTEL_CODE.getColumn()).in(hotelCommentScoreQO.getHotelCodes()));
        }
        if(StringUtils.isNotBlank(hotelCommentScoreQO.getPlatform())) {
            criteria =  criteria.and(Criteria.where(HotelCommentScoreField.COMMENT_PLATFORM.getColumn()).is(hotelCommentScoreQO.getPlatform()));
        }
        if(StringUtils.isNotBlank(hotelCommentScoreQO.getChannel())) {
            criteria =  criteria.and(Criteria.where(HotelCommentScoreField.COMMENT_CHANNEL.getColumn()).is(hotelCommentScoreQO.getChannel()));
        }
        if(Objects.nonNull(hotelCommentScoreQO.getStartDate()) && Objects.nonNull(hotelCommentScoreQO.getEndDate())){
            criteria = criteria.and(Criteria.where(HotelCommentScoreField.CREATED_AT.getColumn()).greaterThan(hotelCommentScoreQO.getStartDate())
                    .and(HotelCommentScoreField.CREATED_AT.getColumn()).lessThan(hotelCommentScoreQO.getEndDate()));
        }


        if(!hotelCommentScoreQO.isIncludeDeleted()) {
            criteria = criteria.and(Criteria.where(HotelCommentScoreField.ROW_STATUS.getColumn()).is(RowStatusEnum.VALID.getId()));
        }


        return criteria;
    }
}
