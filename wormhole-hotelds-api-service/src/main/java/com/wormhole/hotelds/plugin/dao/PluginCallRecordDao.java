package com.wormhole.hotelds.plugin.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.wormhole.hotelds.plugin.model.entity.PluginCallRecord;
import com.wormhole.hotelds.plugin.repository.PluginCallRecordRepository;
import jakarta.annotation.Resource;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-07-14 10:57:36
 * @Description:
 */
@Repository
public class PluginCallRecordDao {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private PluginCallRecordRepository pluginCallRecordRepository;

    public Mono<Boolean> save(PluginCallRecord pluginCallRecord) {
        return pluginCallRecordRepository.save(pluginCallRecord)
                .map(Objects::nonNull);
    }

    public Mono<Boolean> upsert(PluginCallRecord pluginCallRecord) {
        if (Objects.isNull(pluginCallRecord.getId())) {
            return r2dbcEntityTemplate.insert(pluginCallRecord)
                    .map(Objects::nonNull);
        } else {
            return r2dbcEntityTemplate.update(pluginCallRecord)
                    .map(Objects::nonNull);
        }
    }

    public Mono<Boolean> upsert(Collection<PluginCallRecord> records) {
        if (records == null || records.isEmpty()) {
            return Mono.just(false); // 如果记录为空，则直接返回false
        }

        // 使用Flux.fromIterable将集合转换为Flux，并对每个元素调用upsert方法
        return Flux.fromIterable(records)
                .flatMap(this::upsert)
                .then(Mono.just(true)) // 所有upsert操作完成后返回Mono.just(true)，表示成功
                ;

    }


    public Mono<Boolean> save(List<PluginCallRecord> records) {
        return pluginCallRecordRepository.saveAll(records)
                .collectList()
                .thenReturn(true);

    }

    public Mono<PluginCallRecord> findByJobIdAndPhone(Integer jobId, String phone) {
        Criteria criteria = Criteria.where("job_id").is(jobId)
                .and("phone_number").is(phone);
        Query query = Query.query(criteria);
        query.limit(1);
        return r2dbcEntityTemplate
                .select(query, PluginCallRecord.class)
                .next();
    }

    public Mono<PluginCallRecord> findByJobIdAndEncryptPhone(Integer jobId, String encryptPhone) {
        Criteria criteria = Criteria.where("job_id").is(jobId)
                .and("encrypt_phone_number").is(encryptPhone);
        Query query = Query.query(criteria);
        query.limit(1);
        return r2dbcEntityTemplate
                .select(query, PluginCallRecord.class)
                .next();
    }

    public Mono<PluginCallRecord> findByOrderId(String orderId) {
        if (StrUtil.isBlank(orderId)) {
            return Mono.empty();
        }
        Criteria criteria = Criteria.where("order_id").is(orderId);
        Query query = Query.query(criteria);
        query.limit(1);
        return r2dbcEntityTemplate
                .select(query, PluginCallRecord.class)
                .next();
    }

    public Mono<List<PluginCallRecord>> findByOrderIds(Collection<String> orderIds) {
        if (CollUtil.isEmpty(orderIds)) {
            return Mono.empty();
        }
        Criteria criteria = Criteria.where("order_id").in(orderIds);
        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.select(query, PluginCallRecord.class)
                .collectList();
    }


    public Flux<PluginCallRecord> listByStatusAndCallDate(Collection<Integer> handleStatus, LocalDateTime callDate,int limit) {
        Criteria criteria = Criteria.where("handle_status").in(handleStatus)
                .and("call_date").lessThanOrEquals(callDate);

        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.select(query, PluginCallRecord.class)
                .take(limit)
                ;
    }


}
