package com.wormhole.hotelds.plugin.dao;

import com.wormhole.hotelds.plugin.model.entity.PluginCallRecordDetailEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-28 17:08:42
 * @Description:
 */
@Repository
public class PluginCallRecordDetailEntityDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;


    public Mono<Boolean> insert(List<PluginCallRecordDetailEntity> recordDetailEntities){
        List<Mono<PluginCallRecordDetailEntity>> list = recordDetailEntities
                .stream()
                .map(entity -> r2dbcEntityTemplate.insert(entity))
                .toList();
        return Flux.merge(list)
                .collectList()
                .map(list1 -> list1.size() == recordDetailEntities.size());

    }

}
