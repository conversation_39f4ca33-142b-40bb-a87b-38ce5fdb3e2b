package com.wormhole.hotelds.plugin.dao;

import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.hotelds.plugin.model.entity.PluginCollectionDetails;
import com.wormhole.hotelds.plugin.model.field.PluginCollectionDetailsField;
import com.wormhole.hotelds.plugin.model.qo.PluginCollectionDetailsQO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/8
 */
@Repository
@Slf4j
public class PluginCollectionDetailsDao {
    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public Mono<PluginCollectionDetails> save(PluginCollectionDetails pluginCollectionDetails){
        return r2dbcEntityTemplate.insert(pluginCollectionDetails);
    }

    /**
     * 根据条件查询采集详情列表
     */
    public Mono<List<PluginCollectionDetails>> findList(PluginCollectionDetailsQO qo) {
        Criteria criteria = buildCriteria(qo);
        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.select(query, PluginCollectionDetails.class).collectList();
    }

    /**
     * 根据条件查询采集详情列表（兼容旧方法）
     * @deprecated 建议使用 findList(PluginCollectionDetailsQO qo)
     */
    @Deprecated
    public Mono<List<PluginCollectionDetails>> findList(String hotelCode, LocalDate startDate, LocalDate endDate) {
        PluginCollectionDetailsQO qo = PluginCollectionDetailsQO.builder()
                .hotelCode(hotelCode)
                .startDate(startDate != null ? startDate.atStartOfDay() : null)
                .endDate(endDate != null ? endDate.atTime(23, 59, 59) : null)
                .build();
        return findList(qo);
    }

    /**
     * 构建查询条件
     */
    private Criteria buildCriteria(PluginCollectionDetailsQO qo) {
        Criteria criteria = Criteria.where(PluginCollectionDetailsField.ROW_STATUS.getColumn())
                .is(RowStatusEnum.VALID.getId());

        // 酒店编码条件
        if (StringUtils.hasText(qo.getHotelCode())) {
            criteria = criteria.and(PluginCollectionDetailsField.HOTEL_CODE.getColumn()).is(qo.getHotelCode());
        }

        // 酒店名称条件
        if (StringUtils.hasText(qo.getHotelName())) {
            criteria = criteria.and(PluginCollectionDetailsField.HOTEL_NAME.getColumn()).like("%" + qo.getHotelName() + "%");
        }

        // 采集类型条件
        if (StringUtils.hasText(qo.getCollectType())) {
            criteria = criteria.and(PluginCollectionDetailsField.COLLECT_TYPE.getColumn()).is(qo.getCollectType());
        }

        // 开始时间条件
        if (qo.getStartDate() != null) {
            criteria = criteria.and(PluginCollectionDetailsField.CREATED_AT.getColumn())
                    .greaterThanOrEquals(qo.getStartDate());
        }

        // 结束时间条件
        if (qo.getEndDate() != null) {
            criteria = criteria.and(PluginCollectionDetailsField.CREATED_AT.getColumn())
                    .lessThanOrEquals(qo.getEndDate());
        }

        return criteria;
    }
}
