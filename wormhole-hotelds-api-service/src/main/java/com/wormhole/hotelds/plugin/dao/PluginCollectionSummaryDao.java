package com.wormhole.hotelds.plugin.dao;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.hotelds.plugin.model.entity.PluginCollectionSummary;
import com.wormhole.hotelds.plugin.model.enums.PluginCollectType;
import com.wormhole.hotelds.plugin.model.field.PluginCollectionSummaryField;
import com.wormhole.hotelds.plugin.model.qo.*;
import com.wormhole.hotelds.plugin.model.vo.PluginCrawlDataDetailDataVO;
import com.wormhole.hotelds.plugin.model.vo.PluginCrawlDataSummaryVO;
import com.wormhole.hotelds.plugin.repository.PluginCollectionSummaryRepository;
import io.r2dbc.spi.Row;
import io.r2dbc.spi.RowMetadata;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/8/8
 */
@Repository
@Slf4j
public class PluginCollectionSummaryDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    @Autowired
    private PluginCollectionSummaryRepository pluginCollectionSummaryRepository;
    @Autowired
    private DatabaseClient databaseClient;

    /**
     * 保存汇总数据
     */
    public Mono<PluginCollectionSummary> save(PluginCollectionSummary summary) {
        return r2dbcEntityTemplate.insert(summary);
    }

    /**
     * 批量保存汇总数据
     */
    public Mono<List<PluginCollectionSummary>> saveAll(List<PluginCollectionSummary> summaryList) {
        if (summaryList == null || summaryList.isEmpty()) {
            return Mono.just(List.of());
        }
        return pluginCollectionSummaryRepository.saveAll(summaryList).collectList();
    }

    /**
     * 根据日期范围删除汇总数据
     */
    public Mono<Long> deleteByDateRange(LocalDate startDate, LocalDate endDate) {
        Criteria criteria = Criteria.where(PluginCollectionSummaryField.ROW_STATUS.getColumn())
                .is(RowStatusEnum.VALID.getId());

        if (startDate != null) {
            criteria = criteria.and(PluginCollectionSummaryField.STATISTICS_DATE.getColumn())
                    .greaterThanOrEquals(startDate);
        }

        if (endDate != null) {
            criteria = criteria.and(PluginCollectionSummaryField.STATISTICS_DATE.getColumn())
                    .lessThanOrEquals(endDate);
        }

        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.delete(query, PluginCollectionSummary.class)
                .doOnNext(result -> log.info("删除汇总数据，日期范围: {} - {}, 删除条数: {}", startDate, endDate, result));
    }

    /**
     * 先删除再保存（确保数据唯一性）
     * 即使 summaryList 为空也会执行删除操作来清除指定日期范围的数据
     */
    public Mono<List<PluginCollectionSummary>> deleteAndSaveAll(List<PluginCollectionSummary> summaryList,
                                                                LocalDate startDate,
                                                                LocalDate endDate) {
        int recordCount = (summaryList != null) ? summaryList.size() : 0;
        log.info("开始删除并保存汇总数据，日期范围: {} - {}, 待保存记录数: {}", startDate, endDate, recordCount);

        // 先删除指定日期范围的数据
        Mono<Long> deleteMono = deleteByDateRange(startDate, endDate);

        // 如果有数据需要保存，则保存；否则只执行删除
        if (summaryList != null && !summaryList.isEmpty()) {
            return deleteMono
                    .then(saveAll(summaryList))
                    .doOnSuccess(savedList -> log.info("成功删除并保存汇总数据，保存条数: {}", savedList.size()))
                    .doOnError(error -> log.error("删除并保存汇总数据失败，日期范围: {} - {}", startDate, endDate, error));
        } else {
            return deleteMono
                    .then(Mono.just(List.<PluginCollectionSummary>of()))
                    .doOnSuccess(emptyList -> log.info("成功删除汇总数据，无新数据需要保存"))
                    .doOnError(error -> log.error("删除汇总数据失败，日期范围: {} - {}", startDate, endDate, error));
        }
    }

    /**
     * 根据查询条件统计采集数据汇总
     */
    public Mono<PluginCrawlDataSummaryVO> getSummaryStatistics(PluginCrawlDataSummaryQO qo) {
        DatabaseClient databaseClient = r2dbcEntityTemplate.getDatabaseClient();

        // 构建统计SQL
        StringBuilder sqlBuilder = new StringBuilder("""
            SELECT 
                COALESCE(SUM(hotel_report_generated_count), 0) as hotel_report_generated_total_count,
                COALESCE(SUM(hotel_reviews_collected_count), 0) as hotel_reviews_collected_total_count,
                COALESCE(SUM(hotel_orders_collected_count), 0) as hotel_orders_collected_total_count,
                COALESCE(SUM(hotel_prices_collected_count), 0) as hotel_prices_collected_total_count,
                COALESCE(SUM(hotel_good_review_code_use_count), 0) as hotel_good_review_code_use_total_count,
                COALESCE(SUM(hotel_comment_reply_count), 0) as hotel_comment_reply_total_count,
                COALESCE(SUM(hotel_outbound_call_count), 0) as hotel_outbound_call_total_count
            FROM plugin_collection_summary
            WHERE row_status = :rowStatus
            AND statistics_date >= :startDate
            AND statistics_date <= :endDate
            """);

        // 动态添加酒店编码条件
        if (CollUtil.isNotEmpty(qo.getHotelCodes())) {
            sqlBuilder.append("AND hotel_code IN (:hotelCodes) ");
        }

        String sql = sqlBuilder.toString();
        log.info("执行统计查询SQL: {}", sql);

        // 构建查询
        DatabaseClient.GenericExecuteSpec executeSpec = databaseClient.sql(sql)
                .bind("rowStatus", RowStatusEnum.VALID.getId())
                .bind("startDate", qo.getStartDate())
                .bind("endDate", qo.getEndDate());

        // 绑定酒店编码参数
        if (CollUtil.isNotEmpty(qo.getHotelCodes())) {
            executeSpec = executeSpec.bind("hotelCodes", qo.getHotelCodes());
        }

        return executeSpec
                .map(this::mapToSummaryVO)
                .one()
                .doOnSuccess(result -> log.info("统计查询完成，结果: {}", result))
                .doOnError(error -> log.error("统计查询失败，查询条件: {}", qo, error));
    }

    /**
     * 获取详细数据
     */
    public Flux<PluginCrawlDataDetailDataVO> getDetailData(PluginCrawlDataDetailQO qo) {
        DatabaseClient databaseClient = r2dbcEntityTemplate.getDatabaseClient();

        // 获取排序字段
        PluginCollectType sortByMetric = Optional.ofNullable(qo.getSortPluginCollectType())
                .map(PluginCollectType::valueOf)
                .orElse(PluginCollectType.HOTEL_REPORT_GENERATED);

        // 构建详细查询SQL
        String sql = String.format("""
            SELECT 
                hotel_name,
                COALESCE(SUM(hotel_report_generated_count), 0) as hotel_report_generated_total_count,
                COALESCE(SUM(hotel_reviews_collected_count), 0) as hotel_reviews_collected_total_count,
                COALESCE(SUM(hotel_orders_collected_count), 0) as hotel_orders_collected_total_count,
                COALESCE(SUM(hotel_prices_collected_count), 0) as hotel_prices_collected_total_count,
                COALESCE(SUM(hotel_good_review_code_use_count), 0) as hotel_good_review_code_use_total_count,
                COALESCE(SUM(hotel_comment_reply_count), 0) as hotel_comment_reply_total_count,
                COALESCE(SUM(hotel_outbound_call_count), 0) as hotel_outbound_call_total_count
            FROM plugin_collection_summary
            WHERE row_status = :rowStatus
            AND statistics_date >= :startDate
            AND statistics_date <= :endDate
            GROUP BY hotel_name
            ORDER BY SUM(%s) DESC
            LIMIT :limit OFFSET :offset
            """, sortByMetric.getSummaryField());

        log.info("执行详细数据查询SQL: {}", sql);

        long offset = (long) (qo.getCurrentPage() - 1) * qo.getPageSize();

        return databaseClient.sql(sql)
                .bind("rowStatus", RowStatusEnum.VALID.getId())
                .bind("startDate", qo.getStartDate())
                .bind("endDate", qo.getEndDate())
                .bind("limit", qo.getPageSize())
                .bind("offset", offset)
                .map(this::mapToDetailDataVO)
                .all();
    }

    /**
     * 统计不同酒店数量
     */
    public Mono<Long> countDistinctHotels(PluginCrawlDataDetailQO qo) {
        DatabaseClient databaseClient = r2dbcEntityTemplate.getDatabaseClient();

        StringBuilder stringBuilder = new StringBuilder("""
                SELECT COUNT(DISTINCT hotel_name) as total_count
                FROM plugin_collection_summary
                WHERE row_status =  :rowStatus
                AND statistics_date >=  :startDate
                AND statistics_date <=  :endDate
                """);
        String sql = stringBuilder.toString();
        log.info("执行统计不同酒店数量查询SQL: {}", sql);

        DatabaseClient.GenericExecuteSpec executeSpec = databaseClient.sql(sql);
        executeSpec = executeSpec.bind("startDate", qo.getStartDate());
        executeSpec = executeSpec.bind("endDate", qo.getEndDate());
        executeSpec = executeSpec.bind("rowStatus", RowStatusEnum.VALID.getId());


        return executeSpec
                .map((row, metadata) -> Optional.ofNullable(row.get("total_count", Long.class)).orElse(0L))
                .one()
                .defaultIfEmpty(0L);
    }

    /**
     * 映射统计结果
     */
    private PluginCrawlDataSummaryVO mapToSummaryVO(Row row,
                                                    RowMetadata metadata) {
        return PluginCrawlDataSummaryVO.builder()
                .hotelReportGeneratedTotalCount(row.get("hotel_report_generated_total_count", Long.class))
                .hotelReviewsCollectedTotalCount(row.get("hotel_reviews_collected_total_count", Long.class))
                .hotelOrdersCollectedTotalCount(row.get("hotel_orders_collected_total_count", Long.class))
                .hotelPricesCollectedTotalCount(row.get("hotel_prices_collected_total_count", Long.class))
                .hotelGoodReviewCodeUseTotalCount(row.get("hotel_good_review_code_use_total_count", Long.class))
                .hotelCommentReplyTotalCount(row.get("hotel_comment_reply_total_count", Long.class))
                .hotelOutboundCallTotalCount(row.get("hotel_outbound_call_total_count", Long.class))
                .build();
    }

    /**
     * 映射详细数据结果
     */
    private PluginCrawlDataDetailDataVO mapToDetailDataVO(Row row,
                                                          RowMetadata metadata) {
        return PluginCrawlDataDetailDataVO.builder()
                .hotelName(row.get("hotel_name", String.class))
                .hotelReportGeneratedTotalCount(row.get("hotel_report_generated_total_count", Long.class))
                .hotelReviewsCollectedTotalCount(row.get("hotel_reviews_collected_total_count", Long.class))
                .hotelOrdersCollectedTotalCount(row.get("hotel_orders_collected_total_count", Long.class))
                .hotelPricesCollectedTotalCount(row.get("hotel_prices_collected_total_count", Long.class))
                .hotelGoodReviewCodeUseTotalCount(row.get("hotel_good_review_code_use_total_count", Long.class))
                .hotelCommentReplyTotalCount(row.get("hotel_comment_reply_total_count", Long.class))
                .hotelOutboundCallTotalCount(row.get("hotel_outbound_call_total_count", Long.class))
                .build();
    }

    public Mono<List<String>> getTopMetricHotelName(IndicatorTrendQO indicatorTrendQO){
        String initSql = """
                SELECT hotel_name
                FROM  plugin_collection_summary        
                where 
                row_status = :rowStatus
                AND statistics_date >= :startDate
                AND statistics_date <= :endDate  
                GROUP BY hotel_name    
                order by sum(:sortFiled)
                limit 0, :topN
                """;



        // 获取排序字段
        PluginCollectType sortByMetric = Optional.ofNullable(indicatorTrendQO.getSortPluginCollectType())
                .map(PluginCollectType::fromCode)
                .orElse(PluginCollectType.HOTEL_REPORT_GENERATED);

        return databaseClient.sql(initSql)
                .bind("rowStatus", RowStatusEnum.VALID.getId())
                .bind("startDate", indicatorTrendQO.getStartDate())
                .bind("endDate", indicatorTrendQO.getEndDate())
                .bind("topN", indicatorTrendQO.getTopN())
                .bind("sortFiled", sortByMetric.getSummaryField().getColumn())
                .map((row, metadata) -> row.get("hotel_name", String.class)).all().collectList();

    }

    public Mono<List<HotelMetricsVO>> getHotelMetrics(HotelMetricsQO hotelMetricsQO) {
        String initSql = """
                SELECT statistics_date, hotel_name,sum(%s) as total
                FROM  plugin_collection_summary        
                where 
                row_status = :rowStatus
                AND statistics_date >= :startDate
                AND statistics_date <= :endDate   
                AND hotel_name in (:hotelNames)      
                group by  statistics_date, hotel_name
                """;


        PluginCollectType sortByMetric = Optional.ofNullable(hotelMetricsQO.getSortPluginCollectType())
                .map(PluginCollectType::fromCode)
                .orElse(PluginCollectType.HOTEL_REPORT_GENERATED);

        String format = String.format(initSql, sortByMetric.getSummaryField().getColumn());
        return databaseClient.sql(format)
                .bind("hotelNames", hotelMetricsQO.getHotelNames())
                .bind("rowStatus", RowStatusEnum.VALID.getId())
                .bind("startDate", hotelMetricsQO.getStartDate())
                .bind("endDate", hotelMetricsQO.getEndDate())
                .map(this::toHotelMetricsVO).all().collectList();
    }

    private HotelMetricsVO toHotelMetricsVO(Row row,
                                            RowMetadata metadata){

       return   HotelMetricsVO.builder()
                .count(row.get("total", Long.class))
                .hotelName(row.get("hotel_name", String.class))
               .date(row.get("statistics_date", LocalDate.class)).build();
    }
}
