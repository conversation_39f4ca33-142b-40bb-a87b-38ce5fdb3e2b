package com.wormhole.hotelds.plugin.dao;

import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.hotelds.plugin.model.entity.PluginCollectionSummary;
import com.wormhole.hotelds.plugin.model.field.PluginCollectionSummaryField;
import com.wormhole.hotelds.plugin.repository.PluginCollectionSummaryRepository;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/8
 */
@Repository
@Slf4j
public class PluginCollectionSummaryDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    @Autowired
    private PluginCollectionSummaryRepository pluginCollectionSummaryRepository;

    /**
     * 保存汇总数据
     */
    public Mono<PluginCollectionSummary> save(PluginCollectionSummary summary) {
        return r2dbcEntityTemplate.insert(summary);
    }

    /**
     * 批量保存汇总数据
     */
    public Mono<List<PluginCollectionSummary>> saveAll(List<PluginCollectionSummary> summaryList) {
        if (summaryList == null || summaryList.isEmpty()) {
            return Mono.just(List.of());
        }
        return pluginCollectionSummaryRepository.saveAll(summaryList).collectList();
    }

    /**
     * 根据日期范围删除汇总数据
     */
    public Mono<Long> deleteByDateRange(LocalDate startDate, LocalDate endDate) {
        Criteria criteria = Criteria.where(PluginCollectionSummaryField.ROW_STATUS.getColumn())
                .is(RowStatusEnum.VALID.getId());

        if (startDate != null) {
            criteria = criteria.and(PluginCollectionSummaryField.STATISTICS_DATE.getColumn())
                    .greaterThanOrEquals(startDate);
        }

        if (endDate != null) {
            criteria = criteria.and(PluginCollectionSummaryField.STATISTICS_DATE.getColumn())
                    .lessThanOrEquals(endDate);
        }

        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.delete(query, PluginCollectionSummary.class)
                .doOnNext(result -> log.info("删除汇总数据，日期范围: {} - {}, 删除条数: {}", startDate, endDate, result));
    }

    /**
     * 先删除再保存（确保数据唯一性）
     * 即使 summaryList 为空也会执行删除操作来清除指定日期范围的数据
     */
    public Mono<List<PluginCollectionSummary>> deleteAndSaveAll(List<PluginCollectionSummary> summaryList,
                                                               LocalDate startDate,
                                                               LocalDate endDate) {
        int recordCount = (summaryList != null) ? summaryList.size() : 0;
        log.info("开始删除并保存汇总数据，日期范围: {} - {}, 待保存记录数: {}", startDate, endDate, recordCount);

        // 先删除指定日期范围的数据
        Mono<Long> deleteMono = deleteByDateRange(startDate, endDate);

        // 如果有数据需要保存，则保存；否则只执行删除
        if (summaryList != null && !summaryList.isEmpty()) {
            return deleteMono
                    .then(saveAll(summaryList))
                    .doOnSuccess(savedList -> log.info("成功删除并保存汇总数据，保存条数: {}", savedList.size()))
                    .doOnError(error -> log.error("删除并保存汇总数据失败，日期范围: {} - {}", startDate, endDate, error));
        } else {
            return deleteMono
                    .then(Mono.just(List.<PluginCollectionSummary>of()))
                    .doOnSuccess(emptyList -> log.info("成功删除汇总数据，无新数据需要保存"))
                    .doOnError(error -> log.error("删除汇总数据失败，日期范围: {} - {}", startDate, endDate, error));
        }
    }
}
