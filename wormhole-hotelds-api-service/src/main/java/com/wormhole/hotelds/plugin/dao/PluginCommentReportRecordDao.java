package com.wormhole.hotelds.plugin.dao;

import com.wormhole.hotelds.plugin.model.entity.PluginCommentReportRecord;
import com.wormhole.hotelds.plugin.model.field.PluginCommentReportRecordField;
import com.wormhole.hotelds.plugin.model.qo.PluginCommentReportRecordQO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/8/6
 */
@Repository
public class PluginCommentReportRecordDao {
    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public Mono<List<PluginCommentReportRecord>> findList(PluginCommentReportRecordQO pluginCommentReportRecordQO){
        return r2dbcEntityTemplate.select(Query.query(getCriteria(pluginCommentReportRecordQO)), PluginCommentReportRecord.class).collectList();

    }

    public Criteria getCriteria(PluginCommentReportRecordQO pluginCommentReportRecordQO){
        Criteria criteria =  Criteria.empty();
        if(Objects.nonNull(pluginCommentReportRecordQO.getHotelCode())) {
            criteria.and(Criteria.where(PluginCommentReportRecordField.HOTEL_CODE.getColumn()).is(pluginCommentReportRecordQO.getHotelCode()));
        }
        if(Objects.nonNull(pluginCommentReportRecordQO.getStartDate()) && Objects.nonNull(pluginCommentReportRecordQO.getEndDate())){
            criteria.and(Criteria.where(PluginCommentReportRecordField.CREATED_AT.getColumn())
                    .between(pluginCommentReportRecordQO.getStartDate(), pluginCommentReportRecordQO.getEndDate()));
        }
        return criteria;
    }
}
