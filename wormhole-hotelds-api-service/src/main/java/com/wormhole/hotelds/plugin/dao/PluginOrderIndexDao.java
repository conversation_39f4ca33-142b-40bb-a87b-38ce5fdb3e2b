package com.wormhole.hotelds.plugin.dao;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderIndexQO;
import com.wormhole.task.model.entity.filed.PluginOrderField;
import com.wormhole.task.model.entity.index.PluginOrderIndex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.query.Criteria;
import org.springframework.data.elasticsearch.core.query.CriteriaQuery;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/8/6
 */
@Repository
public class PluginOrderIndexDao {
    @Autowired
    private ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;


    public Mono<List<PluginOrderIndex>> findList(PluginOrderIndexQO pluginOrderIndexQO) {
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(pluginOrderIndexQO.getHotelCode())) {
            criteria.and(PluginOrderField.HOTEL_CODE.getColumn()).is(pluginOrderIndexQO.getHotelCode());
        }
        if(Objects.nonNull(pluginOrderIndexQO.getStartDate()) && Objects.nonNull(pluginOrderIndexQO.getEndDate()))
        {
            criteria.and(PluginOrderField.UPDATED_AT.getColumn())
                    .between(pluginOrderIndexQO.getStartDate(), pluginOrderIndexQO.getEndDate());
        }


        CriteriaQuery query = new CriteriaQuery(criteria);
        return reactiveElasticsearchTemplate.search(query, PluginOrderIndex.class)
                .map(SearchHit::getContent)
                .collectList();
    }
}
