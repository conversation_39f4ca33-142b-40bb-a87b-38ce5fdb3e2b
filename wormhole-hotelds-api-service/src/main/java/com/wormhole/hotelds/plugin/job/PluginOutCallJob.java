package com.wormhole.hotelds.plugin.job;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.hotelds.plugin.config.PluginOutCallProperties;
import com.wormhole.hotelds.plugin.dao.PluginCallRecordDao;
import com.wormhole.hotelds.plugin.model.enums.PluginOutCallHandleStatusEnum;
import com.wormhole.hotelds.plugin.service.PluginOutCallService;
import com.wormhole.outcall.core.client.OutCallRobotApiClient;
import com.wormhole.outcall.core.req.OutCallGetJobDetailsRequest;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;
import reactor.core.scheduler.Schedulers;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-07-29 15:05:14
 * @Description:
 */
@Component
@Slf4j
@ConditionalOnBean(OutCallRobotApiClient.class)
public class PluginOutCallJob {

    @Resource(name = "jdCloudOutCallClient")
    private OutCallRobotApiClient jdCloudOutCallClient;

    @Resource
    private PluginCallRecordDao pluginCallRecordDao;

    @Resource
    private PluginOutCallProperties pluginOutCallProperties;

    @Resource
    private PluginOutCallService pluginOutCallService;

    @XxlJob("jdCloudOutCallQueryJob")
    public void jdCloudOutCallJob() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endDateTime = now.minusMinutes(10);

        List<Integer> statusList = CollUtil.toList(
                PluginOutCallHandleStatusEnum.OUT_CALLING.getValue()
        );
        pluginCallRecordDao.listByStatusAndCallDate(statusList, endDateTime, 20)
                .flatMap(pluginCallRecord -> {
                    OutCallGetJobDetailsRequest request = new OutCallGetJobDetailsRequest();
                    PluginOutCallProperties.JdCloud jdCloud = pluginOutCallProperties.getJdCloud();
                    request.setBotId(jdCloud.getBotId());
                    request.setJobId(jdCloud.getCommonJobId());
//                    request.setNoOutbound("true");
                    request.setSkip(0L);
                    request.setLimit(10);
                    request.setCustomerNameList(CollUtil.toList(pluginCallRecord.getPhoneNumber()));
                    request.setUserPin(jdCloud.getUserPin());

                    return jdCloudOutCallClient.getJobDetails(request)
                            .map(resp -> resp.getJobDetails()
                                    .stream()
                                    .filter(e -> Objects.equals(e.getCustomerPhone(), pluginCallRecord.getEncryptPhoneNumber()))
                                    .findFirst());
                })
                .collectList()
                .doOnNext(jobDetailDtos -> jobDetailDtos
                        .forEach(jobDetailDto -> jobDetailDto.ifPresent(pluginOutCallService::handleOutCallCallback))
                )
                .subscribeOn(Schedulers.boundedElastic())
                .subscribe();
        ;

    }


}
