package com.wormhole.hotelds.plugin.manager;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.util.LockUtils;
import com.wormhole.hotelds.plugin.config.PluginOutCallProperties;
import com.wormhole.hotelds.plugin.dao.PluginCallRecordDao;
import com.wormhole.hotelds.plugin.dao.PluginCallRecordDetailEntityDao;
import com.wormhole.hotelds.plugin.model.dto.PluginOutCallContext;
import com.wormhole.hotelds.plugin.model.dto.PluginOutCallExecuteCallBackMsgResult;
import com.wormhole.hotelds.plugin.model.dto.PluginOutCallResult;
import com.wormhole.hotelds.plugin.model.entity.PluginCallRecord;
import com.wormhole.hotelds.plugin.model.entity.PluginCallRecordDetailEntity;
import com.wormhole.hotelds.plugin.model.enums.PluginOutCallHandleStatusEnum;
import com.wormhole.outcall.autoconfigure.factory.OutCallClientFactory;
import com.wormhole.outcall.core.client.OutCallRobotApiClient;
import com.wormhole.outcall.core.dto.JobDetailDto;
import com.wormhole.outcall.core.dto.OutCallCustomerDto;
import com.wormhole.outcall.core.enums.OutCallApiEnum;
import com.wormhole.outcall.core.enums.OutCallExecuteStatusEnum;
import com.wormhole.outcall.core.enums.OutCallStatusEnum;
import com.wormhole.outcall.core.req.OutCallAppendCustomerRequest;
import com.wormhole.outcall.core.req.OutCallStartJobRequest;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-07-11 10:52:27
 * @Description:
 */
@Component
@Slf4j
public class PluginOutCallManager {


    private OutCallRobotApiClient outCallRobotApiClient;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private OutCallClientFactory outCallClientFactory;

    @Resource
    private PluginOutCallProperties pluginOutCallProperties;

    @Resource
    private PluginCallRecordDao pluginCallRecordDao;

    @Resource
    private PluginCallRecordDetailEntityDao pluginCallRecordDetailEntityDao;


    @PostConstruct
    public void init() {
        outCallRobotApiClient = outCallClientFactory.getClient(OutCallApiEnum.JD);
    }


    /**
     * 处理外呼结果
     * 落库等处理
     */
    public Mono<PluginOutCallExecuteCallBackMsgResult> executeOutCallMsg(JobDetailDto jobDetailDto) {
        OutCallExecuteStatusEnum executeStatus = jobDetailDto.getExecuteStatus();
        Set<OutCallExecuteStatusEnum> needHandleStatus = Set.of(
                OutCallExecuteStatusEnum.FINISHED,
                OutCallExecuteStatusEnum.FAILED
        );
        if (!needHandleStatus.contains(executeStatus)) {
            return Mono.empty();
        }

        Integer jobId = jobDetailDto.getJobId();
        String customerPhone = jobDetailDto.getCustomerPhone();
        OutCallStatusEnum outCallStatusEnum = jobDetailDto.getOutCallStatusEnum();
        // 用户是否接听 呼出是否成功，有可能虚拟号用户没接听
        boolean callSuccess = Objects.equals(outCallStatusEnum.getCode(), OutCallStatusEnum.SUCCESS.getCode());
        boolean userAnswer = callSuccess && jobDetailDto.isUserAnswer();


        return pluginCallRecordDao.findByJobIdAndEncryptPhone(jobId, customerPhone)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "数据不存在")))
                .flatMap(pluginCallRecord -> {
                    pluginCallRecord.setHandleStatus(PluginOutCallHandleStatusEnum.HANDLE_CALL_BACKING.getValue());
                    return pluginCallRecordDao.upsert(pluginCallRecord)
                            .thenReturn(pluginCallRecord);
                })
                .flatMap(pluginCallRecord -> userAnswer
                        ? successExecuteOutCallMsg(pluginCallRecord, jobDetailDto)
                        : failExecuteOutCallMsg(pluginCallRecord, jobDetailDto)
                );

    }

    private Mono<PluginOutCallExecuteCallBackMsgResult> failExecuteOutCallMsg(PluginCallRecord pluginCallRecord, JobDetailDto jobDetailDto) {
        Integer jobId = jobDetailDto.getJobId();
        String customerPhone = jobDetailDto.getCustomerPhone();
        pluginCallRecord.setHandleStatus(PluginOutCallHandleStatusEnum.HANDLE_CALL_BACK_FAIL.getValue());
        return pluginCallRecordDao.upsert(pluginCallRecord)
                .then(Mono.just(PluginOutCallExecuteCallBackMsgResult.buildFailResult(jobId, customerPhone, pluginCallRecord.getOrderId(), jobDetailDto.getOutCallStatusEnum())));
    }

    private Mono<PluginOutCallExecuteCallBackMsgResult> successExecuteOutCallMsg(PluginCallRecord pluginCallRecord, JobDetailDto jobDetailDto) {
        Integer jobId = jobDetailDto.getJobId();
        String customerPhone = jobDetailDto.getCustomerPhone();
        List<PluginCallRecordDetailEntity> list = jobDetailDto
                .getChatTextItems()
                .stream()
                .map(e -> {
                    PluginCallRecordDetailEntity pluginCallRecordDetailEntity = new PluginCallRecordDetailEntity();
                    pluginCallRecordDetailEntity.setRecordId(pluginCallRecord.getId());
                    pluginCallRecordDetailEntity.setMsg(e.getMsg());
//                                    pluginCallRecordDetailEntity.setRecord(e.get);
//                                    pluginCallRecordDetailEntity.setPath();
                    pluginCallRecordDetailEntity.setUser(e.getUserType());
                    pluginCallRecordDetailEntity.setOrder(e.getOrder());
                    return pluginCallRecordDetailEntity;
                })
                .sorted(Comparator.comparingInt(PluginCallRecordDetailEntity::getOrder))
                .toList();
        pluginCallRecord.setHandleStatus(PluginOutCallHandleStatusEnum.HANDLE_CALL_BACK_SUCCESS.getValue());
        Mono<Boolean> save = pluginCallRecordDetailEntityDao.insert(list);
        Mono<Boolean> upsert = pluginCallRecordDao.upsert(pluginCallRecord);

        return Mono.zip(upsert, save)
                .then(Mono.just(PluginOutCallExecuteCallBackMsgResult.buildSuccessResult(jobId, customerPhone, pluginCallRecord.getOrderId(), list)));
    }


    public JobDetailDto convertCallbackData(OutCallApiEnum apiEnum, String json) {
        return outCallRobotApiClient.convertToJobDetail(json);
    }


    /**
     * 外呼/重呼
     * 虚拟号统一为 英文逗号分隔
     */
    public Mono<PluginOutCallResult> outCall(PluginOutCallContext outCallContext) {
        List<PluginOutCallContext.OutCallItem> outCallItems = outCallContext.getOutCallItems();
        PluginOutCallResult result = new PluginOutCallResult();

        if (CollUtil.isEmpty(outCallItems)) {
            return Mono.just(result);
        }
        // todo lock

        return buildPluginCallRecords(outCallContext)
                .flatMap(pluginCallRecords -> {
                    if (CollUtil.isEmpty(pluginCallRecords)) {
                        return Mono.just(result);
                    }
                    // 哪些可以调用 哪些不可以调用
                    Triple<List<PluginCallRecord>, List<PluginCallRecord>, List<PluginCallRecord>> recordTriple = groupByPluginCallRecords(pluginCallRecords);
                    List<PluginCallRecord> canOutCallRecords = recordTriple.getLeft();
                    List<PluginCallRecord> invalidRecords = recordTriple.getMiddle();
                    List<PluginCallRecord> noOutCallRecords = recordTriple.getRight();

                    canOutCallRecords.forEach(e -> {
                        Integer jobId = pluginOutCallProperties.getJdCloud().getCommonJobId();
                        e.setCanOutCallVariable(jobId, outCallContext.getDate(), outCallContext.getBizId());
                    });

                    noOutCallRecords.forEach(e -> e.setCanNotOutCall("不可外呼"));

                    result.addFailOrderId(getRecordOrderIds(noOutCallRecords));
                    result.addSuccessOrderId(getRecordOrderIds(canOutCallRecords));
                    result.addInvalidOrderId(getRecordOrderIds(invalidRecords));
                    pluginCallRecords.forEach(e -> {
                        String phoneNumber = e.getPhoneNumber()
                                .replaceAll(",", "-")
                                .replaceAll("，", "-");
                        String encryptPhoneNumber = outCallRobotApiClient.encryptPhoneNumber(phoneNumber);
                        e.setEncryptPhoneNumber(encryptPhoneNumber);
                    });


                    return pluginCallRecordDao.upsert(pluginCallRecords)
                            .flatMap(e -> this.doJdOutCall(pluginOutCallProperties.getJdCloud().getCommonJobId(), canOutCallRecords))
                            .thenReturn(result)
                            ;
                });
    }


    private List<String> getRecordOrderIds(List<PluginCallRecord> records) {
        return records.stream().map(PluginCallRecord::getOrderId).toList();
    }

    /**
     * 对外呼记录进行分组
     * 1. 可以外呼
     * 2. 无效的外呼记录，不需要外呼
     * 3. 无法外呼
     */
    private Triple<List<PluginCallRecord>, List<PluginCallRecord>, List<PluginCallRecord>> groupByPluginCallRecords(List<PluginCallRecord> records) {
        // 无效的外呼状态
        Set<Integer> invalidOutCallStatusSet = CollUtil.newHashSet(
                PluginOutCallHandleStatusEnum.OUT_CALLING.getValue(), PluginOutCallHandleStatusEnum.HANDLE_CALL_BACK_SUCCESS.getValue(),
                PluginOutCallHandleStatusEnum.HANDLE_CALL_BACKING.getValue()
        );
        Map<Boolean, List<PluginCallRecord>> invalidMap = records.stream()
                .collect(Collectors.partitioningBy(e -> invalidOutCallStatusSet.contains(e.getHandleStatus())));

        List<PluginCallRecord> invalidRecords = invalidMap.getOrDefault(Boolean.TRUE, new ArrayList<>(1));
        List<PluginCallRecord> validRecords = invalidMap.getOrDefault(Boolean.FALSE, new ArrayList<>(1));

        Map<Boolean, List<PluginCallRecord>> canOutCallMap = validRecords.stream()
                .collect(Collectors.partitioningBy(this::canOutCall));

        List<PluginCallRecord> noOutCallRecords = canOutCallMap.getOrDefault(Boolean.FALSE, new ArrayList<>(1));
        List<PluginCallRecord> outCallRecords = canOutCallMap.getOrDefault(Boolean.TRUE, new ArrayList<>(1));

        return Triple.of(outCallRecords, invalidRecords, noOutCallRecords);
    }


    /**
     * 有的是第一次调用外呼，所以统一构建
     * 没有就初始化
     */
    private Mono<List<PluginCallRecord>> buildPluginCallRecords(PluginOutCallContext outCallContext) {
        Map<String, PluginOutCallContext.OutCallItem> itemMap = outCallContext.getOutCallItems()
                .stream()
                .collect(Collectors.toMap(PluginOutCallContext.OutCallItem::getOrderId, Function.identity(), (e1, e2) -> e2));
        Set<String> orderIds = itemMap.keySet();

        return pluginCallRecordDao.findByOrderIds(orderIds)
                .switchIfEmpty(Mono.just(new ArrayList<>(1)))
                .map(recordList -> {
                    Map<String, PluginCallRecord> recordMap = recordList.stream()
                            .collect(Collectors.toMap(PluginCallRecord::getOrderId, Function.identity(), (e1, e2) -> e2));

                    itemMap.forEach((k, v) -> {
                        PluginCallRecord pluginCallRecord = recordMap.get(k);
                        if (Objects.isNull(pluginCallRecord)) {
                            PluginCallRecord init = PluginCallRecord.init(v.getOrderId(), v.getPhoneNumber(), v.getName()
                                    , JacksonUtils.writeValueAsString(v.getVariable()), outCallContext.getDate(), outCallContext.getBizId());
                            recordMap.put(k, init);
                        }
                    });
                    return new ArrayList<>(recordMap.values());
                });
    }


    private Mono<Boolean> doJdOutCall(Integer jobId, List<PluginCallRecord> records) {
        if (CollUtil.isEmpty(records)) {
            return Mono.just(Boolean.TRUE);
        }
        String userPin = pluginOutCallProperties.getJdCloud().getUserPin();

        List<OutCallCustomerDto> outCallCustomerDtoList = records.stream()
                .map(e -> {
                    OutCallCustomerDto outCallCustomerDto = new OutCallCustomerDto();
                    outCallCustomerDto.setPhone(e.getEncryptPhoneNumber());
                    outCallCustomerDto.setName(e.getPhoneNumber());
                    outCallCustomerDto.setVariableMap(e.getVariableMap());
                    return outCallCustomerDto;
                }).toList();

        OutCallAppendCustomerRequest outCallAppendCustomerRequest = new OutCallAppendCustomerRequest();
        outCallAppendCustomerRequest.setJobId(jobId);
        outCallAppendCustomerRequest.setUserList(outCallCustomerDtoList);
        outCallAppendCustomerRequest.setUserPin(userPin);

        return outCallRobotApiClient.appendCustomer(outCallAppendCustomerRequest)
                .flatMap(outCallAppendCustomerResponse -> {
                    OutCallStartJobRequest outCallStartJobRequest = new OutCallStartJobRequest();
                    outCallStartJobRequest.setJobId(jobId);
                    outCallStartJobRequest.setUserPin(userPin);

                    return outCallRobotApiClient.startJob(outCallStartJobRequest)
                            .onErrorResume(e -> Mono.just(Boolean.FALSE));
                })
                ;
    }


    private boolean canOutCall(PluginCallRecord callRecord) {
        Long id = callRecord.getId();

        PluginOutCallProperties.JdCloud jdCloud = pluginOutCallProperties.getJdCloud();

        Integer callCount = callRecord.getCallCount();
        Integer maxRecallCount = jdCloud.getMaxRecallCount();
        log.info("outCallConfig:{}", JacksonUtils.writeValueAsString(pluginOutCallProperties));
        // 最多重试5次
        if (callCount >= maxRecallCount + 1) {
            log.info("can not call out,id:{} callCount:{}", id, callCount);
            return false;
        }

        String contactNumber = callRecord.getPhoneNumber();

        // 黑名单
        if (!pluginOutCallProperties.canOutCall(contactNumber)) {
            log.info("hit exclude number number:{} excludeNumbers:{}", contactNumber, pluginOutCallProperties.getWhilePhoneList());
            return false;
        }

        // 号码前缀过滤
        if (pluginOutCallProperties.isExcludeNumberPrefix(contactNumber)) {
            log.info("hit exclude number prefix  number:{} excludeConfig:{}", contactNumber, pluginOutCallProperties.getExcludeNumberPrefixMap().keySet());
            return false;
        }

        return true;

    }


}
