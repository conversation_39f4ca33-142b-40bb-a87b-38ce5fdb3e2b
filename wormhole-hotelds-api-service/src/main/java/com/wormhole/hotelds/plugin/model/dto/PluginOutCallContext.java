package com.wormhole.hotelds.plugin.model.dto;

import cn.hutool.core.date.SystemClock;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-07-28 16:03:47
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PluginOutCallContext {

    private LocalDateTime date;

    private String bizId;

    private List<OutCallItem> outCallItems;

    public String getBizId() {
        if (StrUtil.isNotBlank(this.bizId)) {
            return bizId;
        }
        String suffix = Optional.ofNullable(outCallItems)
                .map(list -> list
                        .stream()
                        .map(OutCallItem::getOrderId)
                        .collect(Collectors.joining(StrUtil.COMMA))
                )
                .map(DigestUtils::md5Hex)
                .orElse(StrUtil.EMPTY);
        return String.format("%s-%s-%s", "outCall", SystemClock.now(), suffix);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OutCallItem {

        private String orderId;

        private String phoneNumber;

        private String name;

        private Map<String, String> variable;

    }


}
