package com.wormhole.hotelds.plugin.model.dto;

import com.wormhole.hotelds.plugin.model.entity.PluginCallRecordDetailEntity;
import com.wormhole.outcall.core.enums.OutCallStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-29 16:24:56
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PluginOutCallExecuteCallBackMsgResult {

    private Integer jobId;

    private String phoneNumber;

    private String orderId;

    private List<PluginCallRecordDetailEntity> recordDetailEntities;


    private Boolean outCallSuccess;

    private OutCallStatusEnum outCallStatusEnum;


    public static PluginOutCallExecuteCallBackMsgResult buildSuccessResult(Integer jobId,String phoneNumber,String orderId,List<PluginCallRecordDetailEntity> recordDetailEntities){
        PluginOutCallExecuteCallBackMsgResult pluginOutCallExecuteCallBackMsgResult = new PluginOutCallExecuteCallBackMsgResult();
        pluginOutCallExecuteCallBackMsgResult.setJobId(jobId);
        pluginOutCallExecuteCallBackMsgResult.setPhoneNumber(phoneNumber);
        pluginOutCallExecuteCallBackMsgResult.setOrderId(orderId);
        pluginOutCallExecuteCallBackMsgResult.setRecordDetailEntities(recordDetailEntities);
        pluginOutCallExecuteCallBackMsgResult.setOutCallSuccess(Boolean.TRUE);
        pluginOutCallExecuteCallBackMsgResult.setOutCallStatusEnum(OutCallStatusEnum.SUCCESS);
        return pluginOutCallExecuteCallBackMsgResult;
    }


    public static PluginOutCallExecuteCallBackMsgResult buildFailResult(Integer jobId,String phoneNumber,String orderId,OutCallStatusEnum outCallStatusEnum){
        PluginOutCallExecuteCallBackMsgResult pluginOutCallExecuteCallBackMsgResult = new PluginOutCallExecuteCallBackMsgResult();
        pluginOutCallExecuteCallBackMsgResult.setJobId(jobId);
        pluginOutCallExecuteCallBackMsgResult.setPhoneNumber(phoneNumber);
        pluginOutCallExecuteCallBackMsgResult.setOrderId(orderId);
        pluginOutCallExecuteCallBackMsgResult.setOutCallSuccess(Boolean.FALSE);
        pluginOutCallExecuteCallBackMsgResult.setOutCallStatusEnum(outCallStatusEnum);
        return pluginOutCallExecuteCallBackMsgResult;
    }
}
