package com.wormhole.hotelds.plugin.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-07-29 00:16:16
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PluginOutCallResult {
    private Set<String> failOrderIds = new HashSet<>();

    private Set<String> successOrderIds = new HashSet<>();

    /**
     * 无效的orderId 不需要重试
     * 比如说是在外呼中的，直接抛弃不管 也不需要改状态
     */
    private Set<String> invalidOrderIds = new HashSet<>();


    public void addFailOrderId(String orderId) {
        failOrderIds.add(orderId);
    }

    public void addFailOrderId(Collection<String> orderId) {
        failOrderIds.addAll(orderId);
    }

    public void addSuccessOrderId(String orderId) {
        successOrderIds.add(orderId);
    }

    public void addSuccessOrderId(Collection<String> orderId) {
        successOrderIds.addAll(orderId);
    }

    public void addInvalidOrderId(String orderId) {
        invalidOrderIds.add(orderId);
    }

    public void addInvalidOrderId(Collection<String> orderId) {
        invalidOrderIds.addAll(orderId);
    }

    public boolean isSuccess(String orderId) {
        return successOrderIds.contains(orderId);
    }

    public boolean isFail(String orderId) {
        return failOrderIds.contains(orderId);
    }

    public boolean isInvalid(String orderId) {
        return invalidOrderIds.contains(orderId);
    }


}
