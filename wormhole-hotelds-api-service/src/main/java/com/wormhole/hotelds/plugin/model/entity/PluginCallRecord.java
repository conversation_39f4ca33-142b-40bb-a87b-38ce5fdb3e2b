package com.wormhole.hotelds.plugin.model.entity;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wormhole.common.model.entity.BaseEntity;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.plugin.model.enums.PluginOutCallHandleStatusEnum;
import com.wormhole.task.model.constant.PluginOutCallStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("plugin_call_record")
public class PluginCallRecord extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    @Column("id")
    private Long id;

    /**
     * 数据ID（info表的id，唯一）
     */
    @Column("order_id")
    private String orderId;

    /**
     * 呼叫状态
     *
     * @see PluginOutCallStatusEnum
     */
    @Column("handle_status")
    private Integer handleStatus;

    /**
     * 呼叫时间
     */
    @Column("call_date")
    private LocalDateTime callDate;

    /**
     * 手机号,跟order表里面的保持一致
     * 各个厂商再去适配
     */
    @Column("phone_number")
    private String phoneNumber;

    @Column("encrypt_phone_number")
    private String encryptPhoneNumber;

    @Column("name")
    private String name;

    /**
     * 完整通话录音 URL
     */
    @Column("record_path")
    private String recordPath;

    /**
     * 通话时长（秒）
     */
    @Column("billsec")
    private BigDecimal billsec;

    /**
     * 拨打次数
     */
    @Column("call_count")
    private Integer callCount;

    @Column("remarks")
    private String remarks;

    @Column("job_id")
    private Integer jobId;

    /**
     * JSON串
     */
    @Column("variable")
    private String variable;

    @Column("biz_id")
    private String bizId;


    public static PluginCallRecord init(String orderId, String contactNumber, String name, String variable, LocalDateTime callDate, String bizId) {
        PluginCallRecord pluginCallRecord = new PluginCallRecord();
        pluginCallRecord.setOrderId(orderId);
        pluginCallRecord.setHandleStatus(PluginOutCallHandleStatusEnum.INIT.getValue());
        pluginCallRecord.setCallDate(callDate);
        pluginCallRecord.setPhoneNumber(contactNumber.replaceAll("#", ","));
        pluginCallRecord.setCallCount(0);
        pluginCallRecord.setName(name);
        pluginCallRecord.setVariable(variable);
        pluginCallRecord.setBizId(bizId);
        pluginCallRecord.setRowStatus(1);
        return pluginCallRecord;
    }

    public void setCanNotOutCall(String remarks) {
        this.handleStatus = PluginOutCallHandleStatusEnum.OUT_CALL_FAIL.getValue();
        this.jobId = -1;
        this.remarks = StrUtil.join(StrUtil.COMMA, this.remarks, remarks);
    }

    public void setCanOutCallVariable(Integer jobId, LocalDateTime callDate, String bizId) {

        this.jobId = jobId;
        this.handleStatus = PluginOutCallHandleStatusEnum.OUT_CALLING.getValue();
        this.callDate = callDate;
        this.bizId = bizId;
        this.callCount = this.callCount + 1;
    }

    public Map<String, String> getVariableMap() {
        if (StrUtil.isBlank(variable)) {
            return new HashMap<>();
        }
        if (!JSONUtil.isTypeJSON(variable)) {
            return new HashMap<>();
        }
        return JacksonUtils.readValue(this.variable, new TypeReference<Map<String, String>>() {
        });

    }
}

