package com.wormhole.hotelds.plugin.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("plugin_call_record_detail")
public class PluginCallRecordDetailEntity extends BaseEntity {

    /** 主键ID */
    @Id
    @Column("id")
    private Long id;

    @Column("record_id")
    private Long recordId;

    /** 聊天内容 */
    @Column("msg")
    private String msg;

    /** 聊天录音路径（相对路径） */
    @Column("record")
    private String record;

    /** 聊天录音短URL路径 */
    @Column("path")
    private String path;

    /** 发言者 (0: 机器人, 1: 客户) */
    @Column("user")
    private Integer user;

    /**
     * 顺序
     */
    @Column("`order`")
    private Integer order;

    /** 聊天时间 */
    @Column("create_time")
    private LocalDateTime createTime;
}