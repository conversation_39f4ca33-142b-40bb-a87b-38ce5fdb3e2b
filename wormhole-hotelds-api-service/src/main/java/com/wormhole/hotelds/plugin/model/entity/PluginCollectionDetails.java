package com.wormhole.hotelds.plugin.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;
import software.amazon.awssdk.services.bedrockruntime.endpoints.internal.Value;

/**
 * <AUTHOR>
 * @date 2025/8/8
 */
@Data
@Table("plugin_collection_details")
public class PluginCollectionDetails  extends BaseEntity {
    @Id
    private Long id;
    private String hotelCode;
    private String hotelName;
    private String collectType;
    private Integer collectCount;


    public static PluginCollectionDetails build(String hotelCode, String hotelName, String collectType, Integer collectCount){
        PluginCollectionDetails pluginCollectionDetails = new PluginCollectionDetails();
        pluginCollectionDetails.setHotelCode(hotelCode);
        pluginCollectionDetails.setHotelName(hotelName);
        pluginCollectionDetails.setCollectType(collectType);
        pluginCollectionDetails.setCollectCount(collectCount);
        return pluginCollectionDetails;
    }
}
