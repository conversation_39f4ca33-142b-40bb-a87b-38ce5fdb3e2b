package com.wormhole.hotelds.plugin.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/8/8
 */
@Data
@Table("plugin_collection_summary")
public class PluginCollectionSummary extends BaseEntity {
    @Id
    private Long id;

    private static final long serialVersionUID = 1L;

    private String hotelCode;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 统计日期
     */
    private LocalDate statisticsDate;

    /**
     * 酒店生成报告数量
     * 对应 HOTEL_REPORT_GENERATED
     */
    private Integer hotelReportGeneratedCount;

    /**
     * 酒店抓取评论数量
     * 对应 HOTEL_REVIEWS_COLLECTED
     */
    private Integer hotelReviewsCollectedCount;

    /**
     * 酒店抓取订单数量
     * 对应 HOTEL_ORDERS_COLLECTED
     */
    private Integer hotelOrdersCollectedCount;

    /**
     * 酒店抓取价格数量
     * 对应 HOTEL_PRICES_COLLECTED
     */
    private Integer hotelPricesCollectedCount;

    /**
     * 酒店抓取评论总分次数
     * 对应 HOTEL_COMMENT_RATING_COLLECTED
     */
    private Integer hotelCommentRatingCollectedCount;
}
