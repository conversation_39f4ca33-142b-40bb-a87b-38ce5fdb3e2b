package com.wormhole.hotelds.plugin.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Table;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
@Table("plugin_hotel_data_statistics")
public class PluginHotelDataStatisticsEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 外部酒店代码
     */
    private String externalHotelId;

    private String hotelCode;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 统计日期
     */
    private LocalDate statisticsDate;

    /**
     * 每天生成报告数量
     */
    private Integer dailyReportCount;

    /**
     * 每天回复点评数量
     */
    private Integer dailyReviewReplyCount;

    /**
     * 抓取点评数量
     */
    private Integer crawledReviewCount;

    /**
     * 抓取订单数量
     */
    private Integer crawledOrderCount;

    /**
     * 价格抓取条数
     */
    private Integer priceCrawlCount;

    /**
     * 评论总分抓取次数
     */
    private Integer reviewScoreCrawlCount;

}