package com.wormhole.hotelds.plugin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/8/8
 */
@AllArgsConstructor
@Getter
public enum PluginCollectType {

    /**
     * 酒店生成报告数
     */
    HOTEL_REPORT_GENERATED("HOTEL_REPORT_GENERATED", "酒店生成报告"),

    /**
     * 酒店抓多少评论
     */
    HOTEL_REVIEWS_COLLECTED("HOTEL_REVIEWS_COLLECTED", "酒店抓取评论"),

    /**
     * 酒店抓多少订单
     */
    HOTEL_ORDERS_COLLECTED("HOTEL_ORDERS_COLLECTED", "酒店抓取订单"),

    /**
     * 酒店抓价格条数
     */
    HOTEL_PRICES_COLLECTED("HOTEL_PRICES_COLLECTED", "酒店抓取价格"),

    /**
     * 酒店评论总分抓取次数
     */
    HOTEL_COMMENT_RATING_COLLECTED("HOTEL_COMMENT_RATING_COLLECTED", "酒店抓取评论总分");

    private final String code;
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static PluginCollectType fromCode(String code) {
        for (PluginCollectType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown PluginCollectType code: " + code);
    }
}
