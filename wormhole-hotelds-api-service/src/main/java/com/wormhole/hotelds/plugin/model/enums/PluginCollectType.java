package com.wormhole.hotelds.plugin.model.enums;

import com.wormhole.hotelds.plugin.model.field.PluginCollectionSummaryField;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/8/8
 */
@AllArgsConstructor
@Getter
public enum PluginCollectType {

    /**
     * 酒店生成报告数
     */
    HOTEL_REPORT_GENERATED("HOTEL_REPORT_GENERATED", "酒店生成报告", PluginCollectionSummaryField.HOTEL_REPORT_GENERATED_COUNT),

    /**
     * 酒店抓多少评论
     */
    HOTEL_REVIEWS_COLLECTED("HOTEL_REVIEWS_COLLECTED", "酒店抓取评论", PluginCollectionSummaryField.HOTEL_REVIEWS_COLLECTED_COUNT),

    /**
     * 酒店抓多少订单
     */
    HOTEL_ORDERS_COLLECTED("HOTEL_ORDERS_COLLECTED", "酒店抓取订单", PluginCollectionSummaryField.HOTEL_ORDERS_COLLECTED_COUNT),

    /**
     * 酒店抓价格条数
     */
    HOTEL_PRICES_COLLECTED("HOTEL_PRICES_COLLECTED", "酒店抓取价格", PluginCollectionSummaryField.HOTEL_PRICES_COLLECTED_COUNT),

    /**
     * 酒店评论总分抓取次数
     */
    HOTEL_COMMENT_RATING_COLLECTED("HOTEL_COMMENT_RATING_COLLECTED", "酒店抓取评论总分", PluginCollectionSummaryField.HOTEL_COMMENT_RATING_COLLECTED_COUNT),

    /**
     * 好评码使用
     */
    HOTEL_GOOD_REVIEW_CODE_USE("HOTEL_GOOD_REVIEW_CODE_USE","好评码使用", PluginCollectionSummaryField.HOTEL_GOOD_REVIEW_CODE_USE_COUNT),

    /**
     * 点评回复
     */
    HOTEL_COMMENT_REPLY("HOTEL_COMMENT_REPLY","点评回复", PluginCollectionSummaryField.HOTEL_COMMENT_REPLY_COUNT),

    /**
     * 外呼
     */
    HOTEL_OUT_CALL("HOTEL_OUTBOUND_CALL","外呼", PluginCollectionSummaryField.HOTEL_OUTBOUND_CALL_COUNT),
    ;

    private final String code;
    private final String description;
    private final PluginCollectionSummaryField summaryField;

    /**
     * 根据代码获取枚举
     */
    public static PluginCollectType fromCode(String code) {
        for (PluginCollectType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
