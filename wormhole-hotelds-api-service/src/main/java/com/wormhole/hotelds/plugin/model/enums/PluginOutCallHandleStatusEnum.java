package com.wormhole.hotelds.plugin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-08-12 11:13:52
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum PluginOutCallHandleStatusEnum {

    INIT(0,"初始化"),

    HANDLE_CALL_BACKING(1, "回调处理中"),

    OUT_CALLING(2, "外呼中"),

    HANDLE_CALL_BACK_SUCCESS(3, "处理回调成功"),

    HANDLE_CALL_BACK_FAIL(4, "处理回调失败"),

    OUT_CALL_FAIL(5, "外呼失败"),
    ;

    private final int value;

    private final String desc;
}
