package com.wormhole.hotelds.plugin.model.field;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/8/8
 */
@AllArgsConstructor
@Getter
public enum PluginCollectionDetailsField {


    ID("id", "主键ID"),
    ROW_STATUS("row_status", "行状态：1-有效，0-无效"),
    CREATED_BY("created_by", "创建人ID"),
    CREATED_BY_NAME("created_by_name", "创建人姓名"),
    UPDATED_BY("updated_by", "更新人ID"),
    UPDATED_BY_NAME("updated_by_name", "更新人姓名"),
    CREATED_AT("created_at", "创建时间"),
    UPDATED_AT("updated_at", "更新时间"),
    HOTEL_CODE("hotel_code", "酒店 code，关联酒店的标识"),
    HOTEL_NAME("hotel_name", "酒店名称"),
    COLLECT_TYPE("collect_type", "采集类型"),
    COLLECT_COUNT("collect_count", "采集数量");
    private final String column;
    private final String desc;

}
