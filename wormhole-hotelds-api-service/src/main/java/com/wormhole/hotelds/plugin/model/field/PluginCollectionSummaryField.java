package com.wormhole.hotelds.plugin.model.field;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/8/8
 *
 */
@AllArgsConstructor
@Getter
public enum PluginCollectionSummaryField {
    ID("id", "主键ID"),
    ROW_STATUS("row_status", "行状态：1-有效，0-无效"),
    CREATED_BY("created_by", "创建人ID"),
    CREATED_BY_NAME("created_by_name", "创建人姓名"),
    UPDATED_BY("updated_by", "更新人ID"),
    UPDATED_BY_NAME("updated_by_name", "更新人姓名"),
    CREATED_AT("created_at", "创建时间"),
    UPDATED_AT("updated_at", "更新时间"),
    HOTEL_CODE("hotel_code", "酒店 code，关联酒店的标识"),
    HOTEL_NAME("hotel_name", "酒店名称"),
    STATISTICS_DATE("statistics_date", "统计日期"),
    HOTEL_REPORT_GENERATED_COUNT("hotel_report_generated_count", "酒店生成报告数量"),
    HOTEL_REVIEWS_COLLECTED_COUNT("hotel_reviews_collected_count", "酒店抓取评论数量"),
    HOTEL_ORDERS_COLLECTED_COUNT("hotel_orders_collected_count", "酒店抓取订单数量"),
    HOTEL_PRICES_COLLECTED_COUNT("hotel_prices_collected_count", "酒店抓取价格数量"),
    HOTEL_COMMENT_RATING_COLLECTED_COUNT("hotel_comment_rating_collected_count", "酒店抓取评论总分次数"),
    HOTEL_GOOD_REVIEW_CODE_USE_COUNT("hotel_good_review_code_use_count", "酒店使用好评码次数"),
    HOTEL_OUTBOUND_CALL_COUNT("hotel_outbound_call_count", "酒店外呼次数"),
    HOTEL_COMMENT_REPLY_COUNT("hotel_comment_reply_count", "酒店评论回复次数");

    private final String column;
    private final String desc;
}
