package com.wormhole.hotelds.plugin.model.qo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HdsLowestPriceCollectRecordQO implements Serializable {
    private List<String> externalHotelIds;
    private List<LocalDate> priceDates;

    private LocalDateTime startDate;
    private LocalDateTime endDate;

}
