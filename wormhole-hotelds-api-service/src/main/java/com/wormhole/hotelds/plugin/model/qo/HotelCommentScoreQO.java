package com.wormhole.hotelds.plugin.model.qo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HotelCommentScoreQO implements Serializable {
    private String hotelCode;
    private List<String> hotelCodes;
    private String channel;
    private String platform;
    private boolean includeDeleted = false;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
}
