package com.wormhole.hotelds.plugin.model.qo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.relational.core.mapping.Column;

import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HotelCompetitorQO implements Serializable {
    private String hotelCode;
    private Collection<String> channels;
    private String competitorHotelId;
    private Integer selectedStatus; // 0: 未选中, 1: 已选中
    private boolean includeDeleted = false;

}
