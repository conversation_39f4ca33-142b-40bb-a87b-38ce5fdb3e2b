package com.wormhole.hotelds.plugin.model.qo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/8/26
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class IndicatorTrendQO implements Serializable {
    private String startDate;
    private String endDate;
    private Integer topN;
    /**
     * 排序指标
     */
    private String sortPluginCollectType;


}
