package com.wormhole.hotelds.plugin.model.qo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 插件采集详情查询对象
 * 
 * <AUTHOR>
 * @date 2025/8/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PluginCollectionDetailsQO {

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 采集类型
     */
    private String collectType;

    /**
     * 开始时间
     */
    private LocalDateTime startDate;

    /**
     * 结束时间
     */
    private LocalDateTime endDate;
}
