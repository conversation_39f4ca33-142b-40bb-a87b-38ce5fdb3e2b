package com.wormhole.hotelds.plugin.model.qo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import software.amazon.awssdk.services.bedrockruntime.endpoints.internal.Value;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/8/27
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginCrawlDataDetailQO implements Serializable {
    private String startDate;
    private String endDate;
    private Integer pageSize;
    private Integer currentPage;
    private String sortPluginCollectType;
}
