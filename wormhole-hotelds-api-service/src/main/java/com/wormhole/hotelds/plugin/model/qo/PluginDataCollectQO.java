package com.wormhole.hotelds.plugin.model.qo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/8/27
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginDataCollectQO implements Serializable {
    private String hotelCode;
    private String hotelName;
    private String collectType;
    private Integer count;
}
