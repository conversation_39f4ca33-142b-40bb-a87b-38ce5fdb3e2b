package com.wormhole.hotelds.plugin.model.qo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.plugin.model.enums.PluginOrderTabEnum;
import com.wormhole.task.model.constant.PluginOrderMoveStatusEnum;
import com.wormhole.task.model.entity.index.PluginOrderIndex;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Sort;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginOrderQo {
    private String id;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 渠道来源，如：携程
     */
    private String channel;

    /**
     * 渠道来源，如：携程
     */
    private String platform;

    /**
     * 订单状态，如：已确认
     */
    private Collection<Integer> orderStatus;

    private String startDateTime;

    private String endDateTime;

    /**
     * 搬单状态 {@link PluginOrderMoveStatusEnum}
     */
    private List<Integer> moveOrderStatusList;

    /**
     * 是否被修改过
     */
    private Integer updateFlag;

    private Integer pageNumber;

    private Integer pageSize = 10;

    private Sort sort;

    private PluginOrderTabEnum tabEnum;

    private PluginOrderIndex cursor;

    private LocalDate checkInDate;

    private List<Integer> outCallStatusList;

    private Collection<String> orderNoList;

    private Integer minCallOutCount;

    private Integer maxCallOutCount;
}
