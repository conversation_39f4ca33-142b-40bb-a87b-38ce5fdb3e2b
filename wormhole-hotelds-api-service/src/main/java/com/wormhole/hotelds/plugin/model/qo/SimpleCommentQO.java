package com.wormhole.hotelds.plugin.model.qo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@Data
@Builder
public class SimpleCommentQO implements Serializable {
    private List<String> businessIds;
    private String externalHotelId;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private Long id;
}
