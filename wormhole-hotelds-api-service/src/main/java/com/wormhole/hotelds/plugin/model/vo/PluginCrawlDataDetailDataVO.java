package com.wormhole.hotelds.plugin.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 插件采集数据详情数据
 *
 * <AUTHOR>
 * @date 2025/8/27
 */
@Data
@Builder
public class PluginCrawlDataDetailDataVO {

    @JsonProperty("hotel_code")
    private String hotelCode;

    @JsonProperty("hotel_name")
    private String hotelName;

    @JsonProperty("hotel_report_generated_total_count")
    private Long hotelReportGeneratedTotalCount;

    @JsonProperty("hotel_reviews_collected_total_count")
    private Long hotelReviewsCollectedTotalCount;

    @JsonProperty("hotel_orders_collected_total_count")
    private Long hotelOrdersCollectedTotalCount;

    @JsonProperty("hotel_prices_collected_total_count")
    private Long hotelPricesCollectedTotalCount;

    @JsonProperty("hotel_good_review_code_use_total_count")
    private Long hotelGoodReviewCodeUseTotalCount;

    @JsonProperty("hotel_comment_reply_total_count")
    private Long hotelCommentReplyTotalCount;

    @JsonProperty("hotel_outbound_call_total_count")
    private Long hotelOutboundCallTotalCount;
}
