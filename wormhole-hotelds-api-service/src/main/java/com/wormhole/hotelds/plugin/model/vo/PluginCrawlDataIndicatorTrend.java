package com.wormhole.hotelds.plugin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/26
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginCrawlDataIndicatorTrend implements Serializable {
    private String date;
    private List<MetricsData> metricsDataList;


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class MetricsData implements Serializable{
        private String hotelName;
        private Long count;
    }

}
