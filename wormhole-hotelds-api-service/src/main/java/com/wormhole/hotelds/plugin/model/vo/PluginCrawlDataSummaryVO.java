package com.wormhole.hotelds.plugin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 插件采集数据统计响应对象
 * 
 * <AUTHOR>
 * @date 2025/8/26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginCrawlDataSummaryVO {

    /**
     * 酒店生成报告总数
     */
    private Long hotelReportGeneratedTotalCount;

    /**
     * 抓取评论总数
     */
    private Long hotelReviewsCollectedTotalCount;

    /**
     * 抓取订单总数
     */
    private Long hotelOrdersCollectedTotalCount;

    /**
     * 抓取价格条数
     */
    private Long hotelPricesCollectedTotalCount;

    /**
     * 好评码使用次数
     */
    private Long hotelGoodReviewCodeUseTotalCount;

    /**
     * 评论回复总数
     */
    private Long hotelCommentReplyTotalCount;

    /**
     * 外呼总数
     */
    private Long hotelOutboundCallTotalCount;
}
