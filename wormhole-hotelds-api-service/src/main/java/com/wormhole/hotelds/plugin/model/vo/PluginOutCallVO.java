package com.wormhole.hotelds.plugin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginOutCallVO {

    private String orderNo;

    private String bookerName;

    private RemarksInfo remarksInfo;

    private String platform;

    private String channel;

    private List<OutCallRecord> outCallRecordList;

    private Integer outCallStatus;

    private ArrivalTimeInfo arrivalTimeInfo;

    private String contactNumber;

    private String tips;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ArrivalTimeInfo {
        private String arrivalTime;

        private Integer updateFlag;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class RemarksInfo {
        private String remarks;

        private Integer updateFlag;

    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class OutCallRecord {
        private String time;
        private Integer outCallStatus;
        private String outCallStatusDesc;

    }


}
