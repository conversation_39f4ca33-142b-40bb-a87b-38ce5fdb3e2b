package com.wormhole.hotelds.plugin.service;

import com.wormhole.hotelds.plugin.dao.PluginCollectionDetailsDao;
import com.wormhole.hotelds.plugin.model.entity.PluginCollectionDetails;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/8/8
 */
@Service
@Slf4j
public class PluginCollectionDetailsService {
    @Autowired
    private PluginCollectionDetailsDao pluginCollectionDetailsDao;

    public Mono<Boolean> save(PluginCollectionDetails pluginCollectionDetails){
        return pluginCollectionDetailsDao.save(pluginCollectionDetails).map(Objects::nonNull);
    }

}
