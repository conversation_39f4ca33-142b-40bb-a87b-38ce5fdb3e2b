package com.wormhole.hotelds.plugin.service;

import com.wormhole.hotelds.plugin.dao.PluginCollectionDetailsDao;
import com.wormhole.hotelds.plugin.dao.PluginCollectionSummaryDao;
import com.wormhole.hotelds.plugin.model.entity.PluginCollectionDetails;
import com.wormhole.hotelds.plugin.model.entity.PluginCollectionSummary;
import com.wormhole.hotelds.plugin.model.enums.PluginCollectType;
import com.wormhole.hotelds.plugin.model.qo.PluginCollectionDetailsQO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 插件采集汇总服务
 * 负责从 PluginCollectionDetails 表中统计数据并保存到 PluginCollectionSummary 表
 *
 * <AUTHOR>
 * @date 2025/8/8
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PluginCollectionSummaryService {

    private final PluginCollectionSummaryDao pluginCollectionSummaryDao;
    private final PluginCollectionDetailsDao pluginCollectionDetailsDao;

    /**
     * 执行酒店数据统计
     * 从 PluginCollectionDetails 表中查询数据，统计后保存到 PluginCollectionSummary 表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param hotelCode 外部酒店ID（可选，为空则统计所有酒店）
     * @return 执行结果
     */
    public Mono<Boolean> executeHotelDataStatistics(LocalDate startDate, LocalDate endDate, String hotelCode) {
        log.info("开始执行酒店采集数据统计，日期范围: {} - {}, 外部酒店ID: {}", startDate, endDate, hotelCode);

        return collectDetailsData(hotelCode, startDate, endDate)
                .flatMap(detailsList -> processAndSaveStatistics(detailsList, startDate, endDate))
                .doOnSuccess(result -> log.info("酒店采集数据统计执行完成，结果: {}", result))
                .doOnError(error -> log.error("酒店采集数据统计执行失败", error))
                .onErrorReturn(false);
    }

    /**
     * 收集详情数据
     */
    private Mono<List<PluginCollectionDetails>> collectDetailsData(String hotelCode, LocalDate startDate, LocalDate endDate) {
        // 构建查询条件
        PluginCollectionDetailsQO qo = PluginCollectionDetailsQO.builder()
                .hotelCode(hotelCode)
                .startDate(startDate != null ? startDate.atStartOfDay() : null)
                .endDate(endDate != null ? endDate.atTime(23, 59, 59) : null)
                .build();

        return pluginCollectionDetailsDao.findList(qo)
                .doOnSuccess(detailsList -> log.info("查询到采集详情数据 {} 条，查询条件: hotelCode={}, startDate={}, endDate={}",
                        detailsList.size(), hotelCode, startDate, endDate));
    }

    /**
     * 处理并保存统计数据
     */
    private Mono<Boolean> processAndSaveStatistics(List<PluginCollectionDetails> detailsList,
                                                   LocalDate startDate,
                                                   LocalDate endDate) {
        if (detailsList.isEmpty()) {
            log.info("没有找到采集详情数据，跳过统计");
            return Mono.just(true);
        }

        // 按酒店和日期分组统计
        List<PluginCollectionSummary> summaryList = buildSummaryList(detailsList);

        if (summaryList.isEmpty()) {
            log.info("没有生成汇总数据，跳过保存");
            return Mono.just(true);
        }

        // 保存汇总数据
        return pluginCollectionSummaryDao.deleteAndSaveAll(summaryList, startDate, endDate)
                .map(savedList -> !savedList.isEmpty())
                .doOnSuccess(result -> log.info("汇总数据保存完成，结果: {}", result));
    }

    /**
     * 构建汇总数据列表
     * 按酒店编码和创建日期分组，统计各种采集类型的数量
     */
    private List<PluginCollectionSummary> buildSummaryList(List<PluginCollectionDetails> detailsList) {
        // 按酒店编码和日期分组
        Map<String, Map<LocalDate, List<PluginCollectionDetails>>> groupedData = detailsList.stream()
                .filter(detail -> detail.getHotelCode() != null && detail.getCreatedAt() != null)
                .collect(Collectors.groupingBy(
                        PluginCollectionDetails::getHotelCode,
                        Collectors.groupingBy(
                                detail -> detail.getCreatedAt().toLocalDate()
                        )
                ));

        List<PluginCollectionSummary> summaryList = new ArrayList<>();

        // 为每个酒店的每个日期生成汇总数据
        for (Map.Entry<String, Map<LocalDate, List<PluginCollectionDetails>>> hotelEntry : groupedData.entrySet()) {
            String hotelCode = hotelEntry.getKey();
            Map<LocalDate, List<PluginCollectionDetails>> dateMap = hotelEntry.getValue();

            for (Map.Entry<LocalDate, List<PluginCollectionDetails>> dateEntry : dateMap.entrySet()) {
                LocalDate statisticsDate = dateEntry.getKey();
                List<PluginCollectionDetails> dayDetails = dateEntry.getValue();

                PluginCollectionSummary summary = buildSummaryForHotelAndDate(
                        hotelCode, statisticsDate, dayDetails);
                summaryList.add(summary);
            }
        }

        log.info("构建汇总数据完成，共生成 {} 条汇总记录", summaryList.size());
        return summaryList;
    }

    /**
     * 为特定酒店和日期构建汇总数据
     */
    private PluginCollectionSummary buildSummaryForHotelAndDate(String hotelCode,
                                                                LocalDate statisticsDate,
                                                                List<PluginCollectionDetails> dayDetails) {
        PluginCollectionSummary summary = new PluginCollectionSummary();
        summary.setHotelCode(hotelCode);
        summary.setStatisticsDate(statisticsDate);

        // 获取酒店名称（取第一个非空的酒店名称）
        String hotelName = dayDetails.stream()
                .map(PluginCollectionDetails::getHotelName)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
        summary.setHotelName(hotelName);

        // 按采集类型统计数量
        Map<String, Integer> typeCountMap = dayDetails.stream()
                .filter(detail -> detail.getCollectType() != null && detail.getCollectCount() != null)
                .collect(Collectors.groupingBy(
                        PluginCollectionDetails::getCollectType,
                        Collectors.summingInt(detail ->
                                detail.getCollectCount() != null ? detail.getCollectCount() : 0)
                ));

        // 根据采集类型设置对应的统计字段
        summary.setHotelReportGeneratedCount(typeCountMap.getOrDefault(PluginCollectType.HOTEL_REPORT_GENERATED.getCode(), 0));
        summary.setHotelReviewsCollectedCount(typeCountMap.getOrDefault(PluginCollectType.HOTEL_REVIEWS_COLLECTED.getCode(), 0));
        summary.setHotelOrdersCollectedCount(typeCountMap.getOrDefault(PluginCollectType.HOTEL_ORDERS_COLLECTED.getCode(), 0));
        summary.setHotelPricesCollectedCount(typeCountMap.getOrDefault(PluginCollectType.HOTEL_PRICES_COLLECTED.getCode(), 0));
        summary.setHotelCommentRatingCollectedCount(typeCountMap.getOrDefault(PluginCollectType.HOTEL_COMMENT_RATING_COLLECTED.getCode(), 0));
        summary.setHotelGoodReviewCodeUseCount(typeCountMap.getOrDefault(PluginCollectType.HOTEL_GOOD_REVIEW_CODE_USE.getCode(), 0));
        summary.setHotelCommentReplyCount(typeCountMap.getOrDefault(PluginCollectType.HOTEL_COMMENT_REPLY.getCode(), 0));
        summary.setHotelOutboundCallCount(typeCountMap.getOrDefault(PluginCollectType.HOTEL_OUT_CALL.getCode(), 0));
        log.debug("为酒店 {} 日期 {} 构建汇总数据: 报告={}, 评论={}, 订单={}, 价格={}, 评分={}",
                hotelCode, statisticsDate,
                summary.getHotelReportGeneratedCount(), summary.getHotelReviewsCollectedCount(),
                summary.getHotelOrdersCollectedCount(), summary.getHotelPricesCollectedCount(),
                summary.getHotelCommentRatingCollectedCount());

        return summary;
    }

}
