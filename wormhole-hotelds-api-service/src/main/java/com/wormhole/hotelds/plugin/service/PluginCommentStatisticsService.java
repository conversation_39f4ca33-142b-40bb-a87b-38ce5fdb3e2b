package com.wormhole.hotelds.plugin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.func.Consumer3;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.wormhole.agent.ai.core.service.ChatClientService;
import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.core.context.ModelContext;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.chat.ChatRole;
import com.wormhole.agent.knowledge.model.dto.OperatorInfo;
import com.wormhole.agent.knowledge.query.Metadata;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.model.openai.OpenAiChatParams;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.constant.RedisConstant;
import com.wormhole.hotelds.api.hotel.util.LockUtils;
import com.wormhole.hotelds.api.hotel.web.dao.HdsHotelInfoDao;
import com.wormhole.hotelds.core.model.entity.HdsHotelInfoEntity;
import com.wormhole.hotelds.plugin.config.PluginCommentProperties;
import com.wormhole.hotelds.plugin.config.nacos.HoteldsApiModelProperties;
import com.wormhole.hotelds.plugin.config.nacos.TemplateEnum;
import com.wormhole.hotelds.plugin.config.nacos.TemplateService;
import com.wormhole.hotelds.plugin.dao.HdsHotelMappingDao;
import com.wormhole.hotelds.plugin.dao.HotelCommentScoreDao;
import com.wormhole.hotelds.plugin.model.dto.PluginCommentCountIntervalStatsDto;
import com.wormhole.hotelds.plugin.model.dto.PluginCommentScoreIntervalStatsDto;
import com.wormhole.hotelds.plugin.model.dto.PluginReportBadCommentInfoDto;
import com.wormhole.hotelds.plugin.model.dto.ScoreItem;
import com.wormhole.hotelds.plugin.model.entity.HotelCommentScore;
import com.wormhole.hotelds.plugin.model.entity.PluginCollectionDetails;
import com.wormhole.hotelds.plugin.model.entity.PluginCommentReportRecord;
import com.wormhole.hotelds.plugin.model.enums.PluginCollectType;
import com.wormhole.hotelds.plugin.model.enums.PluginCommentSentimentEnum;
import com.wormhole.hotelds.plugin.model.enums.PluginCommentStatisticsIntervalEnum;
import com.wormhole.hotelds.plugin.model.enums.PluginCommentStatisticsRecordStateEnum;
import com.wormhole.hotelds.plugin.model.param.PluginCommentReportParams;
import com.wormhole.hotelds.plugin.model.param.PluginCommentSearchParams;
import com.wormhole.hotelds.plugin.model.param.PluginSimpleCommentSearchParams;
import com.wormhole.hotelds.plugin.model.qo.HdsHotelMappingQO;
import com.wormhole.hotelds.plugin.model.qo.HotelCommentScoreQO;
import com.wormhole.hotelds.plugin.model.qo.SentimentLabelsQO;
import com.wormhole.hotelds.plugin.model.vo.*;
import com.wormhole.hotelds.plugin.repository.PluginCommentReportRecordRepository;
import com.wormhole.hotelds.plugin.repository.TaskScheduleDetailRepository;
import com.wormhole.task.model.constant.CommentChannelEnum;
import com.wormhole.task.model.constant.TaskScheduleJobTypeEnum;
import com.wormhole.task.model.dto.PluginInitializationReportDTO;
import com.wormhole.task.model.entity.TaskScheduleDetailEntity;
import com.wormhole.task.model.entity.index.PluginCommentIndex;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonReactiveClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple5;
import reactor.util.function.Tuples;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-05-13 14:11:29
 * @Description:
 */
@Service
@Slf4j
public class PluginCommentStatisticsService {

    @Resource
    private PluginCommentIndexService pluginCommentIndexService;

    @Resource
    private PluginSimpleCommentIndexService pluginSimpleCommentIndexService;


    @Resource
    private PluginCommentProperties pluginCommentProperties;

    @Resource
    private HotelCompetitorService hotelCompetitorService;

    @Resource
    private ChatClientService chatClientService;
    @Resource
    private TemplateService templateService;

    @Resource
    private HoteldsApiModelProperties hoteldsApiModelProperties;

    @Resource
    private PluginCommentReportRecordRepository pluginCommentReportRecordRepository;

    @Resource
    private PluginCommentReportRecordService pluginCommentReportRecordService;

    @Resource
    private HdsHotelMappingDao hotelMappingEntityDao;

    @Resource
    private HotelCommentScoreDao hotelCommentScoreDao;

    @Resource
    private RedissonReactiveClient redissonReactiveClient;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private PluginTaskService pluginTaskService;

    @Resource
    private TaskScheduleDetailRepository taskScheduleDetailRepository;
    @Autowired
    private HdsHotelInfoDao hdsHotelInfoDao;
    @Autowired
    private PluginCollectionDetailsService pluginCollectionDetailsService;

    /**
     * 查询月报数据
     */
    public Mono<PluginCommentStatisticsMonthReportVo> searchMonthReportData(PluginCommentReportParams param) {
        param.setReportIntervalType(PluginCommentStatisticsIntervalEnum.MONTH.getValue());
        param.initDateTimeRange();
        Pair<LocalDateTime, LocalDateTime> localDateTime = param.toLocalDateTime();
        String channels = Optional.ofNullable(param.getChannels()).map(e -> String.join(",", e)).orElse("");
        return pluginCommentReportRecordService.findOneByTimeRangeAndState(
                        param.getHotelCode(),
                        channels,
                        param.getReportIntervalType(),
                        localDateTime.getFirst(),
                        localDateTime.getSecond(),
                        PluginCommentStatisticsRecordStateEnum.GENERATE_SUCCESS.getValue()
                )
                .map(e -> JacksonUtils.readValue(e.getResult(), PluginCommentStatisticsMonthReportVo.class))
                .switchIfEmpty(Mono.just(new PluginCommentStatisticsMonthReportVo()))
                ;
//                return pluginCommentReportRecordService.monthlyReport(param);
//        param.setReportIntervalType(PluginCommentStatisticsIntervalEnum.MONTH.getValue());
//        return this.buildMonthReportData(param);


    }

    /**
     * 查询周报数据
     */
    public Mono<PluginCommentStatisticsWeekReportVo> searchWeekReportData(PluginCommentReportParams param) {
        param.setReportIntervalType(PluginCommentStatisticsIntervalEnum.WEEK.getValue());
        param.initDateTimeRange();
        Pair<LocalDateTime, LocalDateTime> localDateTime = param.toLocalDateTime();
        String channels = Optional.ofNullable(param.getChannels()).map(e -> String.join(",", e)).orElse("");
        return pluginCommentReportRecordService.findOneByTimeRangeAndState(
                        param.getHotelCode(),
                        channels,
                        param.getReportIntervalType(),
                        localDateTime.getFirst(),
                        localDateTime.getSecond(),
                        PluginCommentStatisticsRecordStateEnum.GENERATE_SUCCESS.getValue()
                )
                .map(e -> JacksonUtils.readValue(e.getResult(), PluginCommentStatisticsWeekReportVo.class))
                .switchIfEmpty(Mono.just(new PluginCommentStatisticsWeekReportVo()))
                ;
//        param.setReportIntervalType(PluginCommentStatisticsIntervalEnum.WEEK.getValue());
//        return this.buildWeekReportData(param);
    }


    public Mono<PluginCommentStatisticsMonthReportVo> buildMonthReportData(PluginCommentReportParams param) {
        int monthsToCompare = pluginCommentProperties.getMonthsToCompare();
        int yearToCompare = pluginCommentProperties.getYearToCompare();

        // 根据年月设置开始、结束时间
        param.initDateTimeRange();

        PluginCommentStatisticsMonthReportVo reportVo = new PluginCommentStatisticsMonthReportVo();
        reportVo.setDateRange(param.toDateRange(DatePattern.NORM_DATE_PATTERN));
        reportVo.setType(PluginCommentStatisticsIntervalEnum.MONTH.getDesc());

        // 构造今年 近6月查询
        PluginCommentSearchParams thisYearSearchParams = param.cloneWithOffsetStartDateTime(-(monthsToCompare - 1), DateField.MONTH);
        // 构造往年查询
        PluginCommentSearchParams previousYearSearchParams = thisYearSearchParams.cloneWithOffsetDateTime(-yearToCompare, DateField.YEAR);
        // 平均得分对比
        return assembleCommentScoreInterval(thisYearSearchParams, PluginCommentStatisticsIntervalEnum.MONTH
                , PluginCommentStatisticsMonthReportVo::setThisYearMonthlyScore, reportVo)
                .flatMap(result -> assembleCommentScoreInterval(previousYearSearchParams, PluginCommentStatisticsIntervalEnum.MONTH
                        , PluginCommentStatisticsMonthReportVo::setPreYearMonthlyScore, reportVo)
                )
                // 好评 差评数
                .flatMap(result -> assembleCommentCountInterval(thisYearSearchParams, PluginCommentStatisticsIntervalEnum.MONTH
                        , PluginCommentStatisticsMonthReportVo::setThisYearCommentCountRange, reportVo)
                )
                .flatMap(result -> assembleCommentCountInterval(previousYearSearchParams, PluginCommentStatisticsIntervalEnum.MONTH
                        , PluginCommentStatisticsMonthReportVo::setPreYearCommentCountRange, reportVo)
                )
                // 点评内容分析
                .flatMap(result -> assembleCommentContentAnalysis(param, result))

                .flatMap(result -> assembleCompetitorAnalysis(param, result))

                // 差评TOP
                .flatMap(result -> {
                    PluginSimpleCommentSearchParams pluginSimpleCommentSearchParams = buildPluginSimpleCommentSearchParams(param, PluginCommentStatisticsIntervalEnum.MONTH.getValue());
                    return getBadCommentInfo(pluginSimpleCommentSearchParams).flatMap(badCommentInfo -> {
                        result.setBadCommentInfo(badCommentInfo);
                        return Mono.just(result);
                    });
                })
                .doOnError(error -> {
                    log.error("error ", error);
                })

                ;

    }

    public Mono<PluginCommentStatisticsWeekReportVo> buildWeekReportData(PluginCommentReportParams param) {
        Integer weeksToCompare = pluginCommentProperties.getWeeksToCompare();

        param.initDateTimeRange();

        // 构造今年 近4周查询
        PluginCommentSearchParams searchParams = param.cloneWithOffsetStartDateTime(-(weeksToCompare - 1), DateField.WEEK_OF_YEAR);

        PluginCommentStatisticsWeekReportVo reportVo = new PluginCommentStatisticsWeekReportVo();
        reportVo.setDateRange(param.toDateRange(DatePattern.NORM_DATE_PATTERN));
        reportVo.setType(PluginCommentStatisticsIntervalEnum.WEEK.getDesc());
        // 差评详情
        return assembleCommentScoreInterval(searchParams, PluginCommentStatisticsIntervalEnum.WEEK
                , PluginCommentStatisticsWeekReportVo::setWeeklyScore, reportVo)
                // 好评 差评数
                .flatMap(result -> assembleCommentCountInterval(searchParams, PluginCommentStatisticsIntervalEnum.WEEK
                        , PluginCommentStatisticsWeekReportVo::setWeeklyCommentCountRange, reportVo)
                )
                .flatMap(result -> {
                    PluginSimpleCommentSearchParams pluginSimpleCommentSearchParams = buildPluginSimpleCommentSearchParams(param, PluginCommentStatisticsIntervalEnum.WEEK.getValue());
                    return getBadCommentInfo(pluginSimpleCommentSearchParams).flatMap(badCommentInfo -> {
                        reportVo.setBadCommentInfo(badCommentInfo);
                        return Mono.just(reportVo);
                    });
                })
                .flatMap(result -> aiInsight(param).flatMap(aiInsight -> {
                    reportVo.setAiInsight(buildAiInsightVO(aiInsight));
                    return Mono.just(reportVo);
                }));

    }

    private List<AiInsightVO> buildAiInsightVO(AiInsight aiInsight) {
        List<AiInsightVO> list = new ArrayList<>();
        // 提取正向和负向的评论数据
        Map<String, Long> positiveMap = aiInsight.getPositive();
        Map<String, Long> negativeMap = aiInsight.getNegative();
        Map<String, Long> posLast2WeekMap = aiInsight.getPosLast2Week();
        Map<String, Long> negLast2WeekMap = aiInsight.getNegLast2Week();

        if (!positiveMap.isEmpty()) {
            AiInsightVO aiInsightVO = new AiInsightVO();
            aiInsightVO.setTitle(aiInsight.getTopPositiveLabel() + aiInsight.getTopPositiveDegree() + aiInsight.getTopPositiveTrend());
            aiInsightVO.setLabels(getTopLabels(positiveMap, 3));
            aiInsightVO.setLabelCount(aiInsight.getTopPositiveLabelCount());
            aiInsightVO.setPercentage(calculatePercentage(aiInsight.getTopPositiveLabelCount(), positiveMap));
            aiInsightVO.setComparisonWithLastWeek(aiInsight.getTopPositiveLabelCountChange() > 0 ? "+" + aiInsight.getTopPositiveLabelCountChange() : String.valueOf(aiInsight.getTopPositiveLabelCountChange()));
            aiInsightVO.setCommentAnalysis(aiInsight.getPositiveCommentAnalysis());
            aiInsightVO.setCommentSuggestion(aiInsight.getPositiveCommentSuggestion());
            aiInsightVO.setSentiment(PluginCommentSentimentEnum.POSITIVE.getCode());
            list.add(aiInsightVO);
        }
        if (!negativeMap.isEmpty()) {
            AiInsightVO aiInsightVO = new AiInsightVO();
            aiInsightVO.setTitle(aiInsight.getTopNegativeLabel() + aiInsight.getTopNegativeDegree() + aiInsight.getTopPositiveTrend());
            aiInsightVO.setLabels(getTopLabels(negativeMap, 3));
            Long topNegativeLabelCount = Optional
                    .ofNullable(aiInsight.getTopNegativeLabelCount())
                    .orElse(0L);
            aiInsightVO.setLabelCount(topNegativeLabelCount);
            aiInsightVO.setPercentage(calculatePercentage(topNegativeLabelCount, negativeMap));
            aiInsightVO.setComparisonWithLastWeek(aiInsight.getTopNegativeLabelCountChange() > 0 ? "+" + aiInsight.getTopNegativeLabelCountChange() : String.valueOf(aiInsight.getTopNegativeLabelCountChange()));
            aiInsightVO.setCommentAnalysis(aiInsight.getNegativeCommentAnalysis());
            aiInsightVO.setCommentSuggestion(aiInsight.getNegativeCommentSuggestion());
            aiInsightVO.setSentiment(PluginCommentSentimentEnum.NEGATIVE.getCode());
            list.add(aiInsightVO);
        }
        return list;

    }

    private double calculatePercentage(Long labelCount, Map<String, Long> map) {
        if (Objects.isNull(labelCount) || labelCount == 0L) {
            return 0;
        }
        double total = map.values().stream().mapToLong(Long::longValue).sum();

//        return total > 0 ? labelCount * 100.0 / total : 0.0;
        return total > 0 ? BigDecimal.valueOf(labelCount * 100.00)
                .divide(BigDecimal.valueOf(total), 2, RoundingMode.HALF_UP).doubleValue()
                : 0;
    }

    /**
     * 获取出现次数最多的标签（前 topN 个）
     *
     * @param commentMap 评论数据映射
     * @param topN       要取的标签个数
     * @return 出现次数最多的前 topN 标签
     */
    private static List<String> getTopLabels(Map<String, Long> commentMap, int topN) {
        return commentMap.entrySet().stream()
                .sorted((e1, e2) -> Long.compare(e2.getValue(), e1.getValue())) // 按值降序排序
                .limit(topN) // 取前N个
                .map(Map.Entry::getKey) // 只取标签
                .collect(Collectors.toList());
    }

    public Mono<AiInsight> aiInsight(PluginCommentReportParams param) {
        PluginCommentSearchParams lastWeekParams = buildSearchParams(param);
        PluginCommentSearchParams last2WeekParams = lastWeekParams.cloneWithOffsetDateTime(-1, DateField.WEEK_OF_YEAR);

        // Step 1: 并行获取四组 label 数据
        Mono<Map<String, Long>> posLastWeekMono = getLabelMap(lastWeekParams, PluginCommentSentimentEnum.POSITIVE);
        Mono<Map<String, Long>> negLastWeekMono = getLabelMap(lastWeekParams, PluginCommentSentimentEnum.NEGATIVE);
        Mono<Map<String, Long>> posLast2WeekMono = getLabelMap(last2WeekParams, PluginCommentSentimentEnum.POSITIVE);
        Mono<Map<String, Long>> negLast2WeekMono = getLabelMap(last2WeekParams, PluginCommentSentimentEnum.NEGATIVE);

        return Mono.zip(posLastWeekMono, negLastWeekMono, posLast2WeekMono, negLast2WeekMono)
                .flatMap(tuple -> {
                    Map<String, Long> posLastWeek = tuple.getT1();
                    Map<String, Long> negLastWeek = tuple.getT2();
                    Map<String, Long> posLast2Week = tuple.getT3();
                    Map<String, Long> negLast2Week = tuple.getT4();

                    AiInsight insight = buildAiInsight(posLastWeek, negLastWeek, posLast2Week, negLast2Week);

                    // Step 2: 根据 top label 再去查询评论
                    Mono<List<PluginCommentIndex>> posCommentsMono = StringUtils.isNotBlank(insight.getTopPositiveLabel())
                            ? pluginCommentIndexService.getCommentsByLabels(
                            lastWeekParams, insight.getTopPositiveLabel())
                            : Mono.just(Lists.newArrayList());

                    Mono<List<PluginCommentIndex>> negCommentsMono = StringUtils.isNotBlank(insight.getTopNegativeLabel())
                            ? pluginCommentIndexService.getCommentsByLabels(
                            lastWeekParams, insight.getTopNegativeLabel())
                            : Mono.just(Lists.newArrayList());

                    return Mono.zip(posCommentsMono, negCommentsMono)
                            .flatMap(commentTuple -> enrichWithAnalysis(insight, commentTuple.getT1(), commentTuple.getT2()))
                            .thenReturn(insight);
                });
    }

    private Mono<Map<String, Long>> getLabelMap(PluginCommentSearchParams params, PluginCommentSentimentEnum sentiment) {
        return pluginCommentIndexService.getLabelsWithSentiment(params, sentiment.getDesc())
                .map(list -> list.stream()
                        .collect(Collectors.toMap(Pair::getFirst, Pair::getSecond, (a, b) -> a))
                );
    }

    private Mono<Void> enrichWithAnalysis(AiInsight insight,
                                          List<PluginCommentIndex> positiveComments,
                                          List<PluginCommentIndex> negativeComments) {
        String positiveCommentsString = positiveComments.stream()
                .map(PluginCommentIndex::getCommentContent)
                .collect(Collectors.joining(";"));
        String negativeCommentsString = negativeComments.stream()
                .map(PluginCommentIndex::getCommentContent)
                .collect(Collectors.joining(";"));

        Mono<String> positiveMono = Mono.empty();
        Mono<String> negativeMono = Mono.empty();

        if (insight.getTopPositiveLabel() != null && !positiveCommentsString.isBlank()) {
            String label = insight.getTopPositiveLabel();
            String lastWeekCount = String.valueOf(insight.getPositive().getOrDefault(label, 0L));
            String twoWeekAgoCount = String.valueOf(insight.getPosLast2Week().getOrDefault(label, 0L));
            Map<String, String> positiveLlmMap = Map.of(
                    "label", label,
                    "last_week_count", lastWeekCount,
                    "two_week_age_count", twoWeekAgoCount,
                    "content", positiveCommentsString
            );
            positiveMono = llmApi(positiveLlmMap, PluginCommentSentimentEnum.POSITIVE.getCode());
        }

        if (insight.getTopNegativeLabel() != null && !negativeCommentsString.isBlank()) {
            String label = insight.getTopNegativeLabel();
            String lastWeekCount = String.valueOf(insight.getNegative().getOrDefault(label, 0L));
            String twoWeekAgoCount = String.valueOf(insight.getNegLast2Week().getOrDefault(label, 0L));
            Map<String, String> negativeLlmMap = Map.of(
                    "label", label,
                    "last_week_count", lastWeekCount,
                    "two_week_age_count", twoWeekAgoCount,
                    "content", negativeCommentsString
            );
            negativeMono = llmApi(negativeLlmMap, PluginCommentSentimentEnum.NEGATIVE.getCode());
        }

        return Mono.zip(positiveMono.defaultIfEmpty(""), negativeMono.defaultIfEmpty(""), (pos, neg) -> {
            if (!pos.isBlank()) {
                Map<String, Object> positiveMap = JacksonUtils.readValue(pos);
                insight.setPositiveCommentAnalysis(String.valueOf(positiveMap.get("comment_analysis")));
                insight.setPositiveCommentSuggestion((List<String>) positiveMap.get("comment_suggestion"));
            }
            if (!neg.isBlank()) {
                Map<String, Object> negativeMap = JacksonUtils.readValue(neg);
                insight.setNegativeCommentAnalysis(String.valueOf(negativeMap.get("comment_analysis")));
                insight.setNegativeCommentSuggestion((List<String>) negativeMap.get("comment_suggestion"));
            }
            return insight;
        }).then();
    }

    private PluginCommentSearchParams buildSearchParams(PluginCommentReportParams param) {
        PluginCommentSearchParams params = new PluginCommentSearchParams();
        params.setHotelCode(param.getHotelCode());
        params.setEndDateTime(param.getEndDateTime());
        params.setStartDateTime(param.getStartDateTime());
        params.setChannels(List.of(
                CommentChannelEnum.Ctrip.getChannel(),
                CommentChannelEnum.Qunar.getChannel(),
                CommentChannelEnum.Elong.getChannel(),
                CommentChannelEnum.ZhiXing.getChannel()
        ));
        return params;
    }

    private AiInsight buildAiInsight(
            Map<String, Long> posLastWeek,
            Map<String, Long> negLastWeek,
            Map<String, Long> posLast2Week,
            Map<String, Long> negLast2Week
    ) {
        AiInsight insight = new AiInsight();
        insight.setPositive(posLastWeek);
        insight.setNegative(negLastWeek);

        if (!posLastWeek.isEmpty()) {
            Map<String, Long> topLabelMap = getTopLabel(posLastWeek);
            if (!topLabelMap.isEmpty()) {
                String topPositiveLabel = topLabelMap.keySet().iterator().next();
                Long topPositiveLabelCount = topLabelMap.get(topPositiveLabel);
                insight.setTopPositiveLabel(topPositiveLabel);
                insight.setTopPositiveLabelCount(topPositiveLabelCount);
                if (topPositiveLabel != null) {
                    long last = posLastWeek.getOrDefault(topPositiveLabel, 0L);
                    long prev = posLast2Week.getOrDefault(topPositiveLabel, 0L);
                    insight.setTopPositiveLabelCountChange(last - prev);
                    insight.setTopPositiveTrend(getTrend(last, prev));
                    insight.setTopPositiveDegree(getDegree(last, prev));
                }
            }

        }

        if (!negLastWeek.isEmpty()) {
            Map<String, Long> topNegativeLabelMap = getTopLabel(negLastWeek);
            if (!topNegativeLabelMap.isEmpty()) {
                String topNegativeLabel = topNegativeLabelMap.keySet().iterator().next();
                Long topNegativeLabelCount = topNegativeLabelMap.get(topNegativeLabel);
                insight.setTopNegativeLabel(topNegativeLabel);
                insight.setTopNegativeLabelCount(topNegativeLabelCount);
                long last = negLastWeek.getOrDefault(topNegativeLabel, 0L);
                long prev = negLast2Week.getOrDefault(topNegativeLabel, 0L);
                insight.setTopNegativeLabelCountChange(last - prev);
                insight.setTopNegativeTrend(getTrend(last, prev));
                insight.setTopNegativeDegree(getDegree(last, prev));
            }
        }
        insight.setPosLast2Week(posLast2Week);
        insight.setNegLast2Week(negLast2Week);


        return insight;
    }

    private Map<String, Long> getTopLabel(Map<String, Long> labelMap) {
        return labelMap.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(entry -> Map.of(entry.getKey(), entry.getValue())) // 将单个键值对转换为 Map
                .orElse(Map.of()); // 如果为空，返回空 Map
    }


    private String getTrend(long current, long previous) {
        if (current > previous) {
            return "上升";
        } else if (current < previous) {
            return "下降";
        } else {
            return "持平";
        }
    }

    private String getDegree(long current, long previous) {
        if (previous == 0) {
            return "显著";
        }
        long difference = current - previous;
        if (difference == 0) {
            return ""; // 新增：差值为0时返回空字符串
        }
        double rate = Math.abs(difference) / (double) previous; // 注意：调整类型转换位置，避免整数除法
        return rate > 0.3 ? "显著" : "小幅";
    }

    public Mono<String> llmApi(Map<String, String> llmMap, String sentiment) {
        String userTemplate;
        String systemTemplate;

        if (PluginCommentSentimentEnum.POSITIVE.getCode().equals(sentiment)) {
            userTemplate = templateService.getTemplate(TemplateEnum.positive_comment_user_prompt);
            systemTemplate = templateService.getTemplate(TemplateEnum.positive_comment_system_prompt);
        } else {
            userTemplate = templateService.getTemplate(TemplateEnum.negative_comment_user_prompt);
            systemTemplate = templateService.getTemplate(TemplateEnum.negative_comment_system_prompt);
        }
        String userPrompt = userTemplate;
        String systemPrompt = com.wormhole.agent.core.util.FreeMarkerTemplateCacheUtils.processTemplateIntoString(systemTemplate, llmMap);

        Metadata metadata = Metadata.builder()
                .userMessage(userPrompt)
                .systemPrompt(systemPrompt)
                .build();


        OpenAiChatMessage systemMessage = OpenAiChatMessage.builder()
                .role(ChatRole.SYSTEM.getValue())
                .content(metadata.getSystemPrompt())
                .build();

        OpenAiChatMessage userMessage = OpenAiChatMessage.builder()
                .role(ChatRole.USER.getValue())
                .content(metadata.getUserMessage())
                .build();

        List<OpenAiChatMessage> recentMessageList = metadata.getRecentMessageList();
        List<OpenAiChatMessage> chatMessageList = Lists.newArrayList();
        chatMessageList.add(systemMessage);
        if (CollectionUtils.isNotEmpty(recentMessageList)) {
            chatMessageList.addAll(recentMessageList);
        }
        chatMessageList.add(userMessage);

        ModelInfo modelInfo = ModelInfo.builder()
                .model(Optional.ofNullable(hoteldsApiModelProperties.getLlmModel())
                        .orElse(UnifiedModelEnum.DOUBAO_PRO_256K.getModel()))
                .build();


        OpenAiChatParams openAiChatParams = OpenAiChatParams.builder()
                .stream(false)
                .model(modelInfo.getModel())
                .temperature(Optional.ofNullable(modelInfo).map(ModelInfo::getTemperature).orElse(0.3d))
                .messages(chatMessageList)
                .build();

        ModelContext modelContext = ModelContext.builder()
                .openAiChatParams(openAiChatParams)
                .modelLogContext(ModelContext.ModelLogContext.builder().build())
                .build();
        return chatClientService.chatCompletions(modelContext)
                .next()
                .map(chatCompletionsDTO -> modelContext.getAnswer());

    }


    private PluginSimpleCommentSearchParams buildPluginSimpleCommentSearchParams(PluginCommentReportParams param, int value) {
        PluginSimpleCommentSearchParams pluginSimpleCommentSearchParams = new PluginSimpleCommentSearchParams();
        pluginSimpleCommentSearchParams.setHotelCode(param.getHotelCode());

        pluginSimpleCommentSearchParams.setStartDate(param.getStartDateTime());
        pluginSimpleCommentSearchParams.setEndDate(param.getEndDateTime());
        pluginSimpleCommentSearchParams.setTopN(PluginCommentStatisticsIntervalEnum.WEEK.getValue() == value ? 3 : 6);
        return pluginSimpleCommentSearchParams;
    }

    private Mono<PluginReportBadCommentInfoDto> getBadCommentInfo(PluginSimpleCommentSearchParams param) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)
                .withZone(ZoneId.systemDefault());
        return pluginCommentIndexService.negativeReviewDetail(param)
                .flatMap(pluginSimpleCommentIndexList -> {
                    PluginReportBadCommentInfoDto dto = new PluginReportBadCommentInfoDto();
                    dto.setCount(pluginSimpleCommentIndexList.size());
                    dto.setCommentList(pluginSimpleCommentIndexList.stream().map(pluginSimpleCommentIndex -> {
                        PluginReportBadCommentInfoDto.Comment comment = new PluginReportBadCommentInfoDto.Comment();
                        comment.setCommentId(pluginSimpleCommentIndex.getId());
                        comment.setCommentDate(dateTimeFormatter.format(pluginSimpleCommentIndex.getCommentDate()));
                        comment.setContent(pluginSimpleCommentIndex.getCommentContent());
                        comment.setCommentScore(pluginSimpleCommentIndex.getCommentScore());
                        comment.setUsername(pluginSimpleCommentIndex.getUsername());
                        comment.setLabels(buildLabels(pluginSimpleCommentIndex.getLabelAnalyses()));
                        comment.setHasReply(!StringUtils.isBlank(pluginSimpleCommentIndex.getHotelReplyContent()));
//                        comment.setTravelType(pluginSimpleCommentIndex.getTravelType());
                        comment.setRoomType(pluginSimpleCommentIndex.getRoomType());
                        comment.setHotelReplyContent(pluginSimpleCommentIndex.getHotelReplyContent());
                        comment.setReplyDate(dateTimeFormatter.format(pluginSimpleCommentIndex.getReplyDate()));
                        comment.setReplyStaff(pluginSimpleCommentIndex.getReplyStaff());
                        return comment;
                    }).toList());
                    return Mono.just(dto);
                });
    }

    private List<PluginReportBadCommentInfoDto.CommentLabel> buildLabels(List<PluginCommentIndex.LabelAnalysis> labelAnalyses) {
        return labelAnalyses.stream()
                .filter(labelAnalysis -> labelAnalysis.getLabels() != null)
                .flatMap(labelAnalysis -> labelAnalysis.getLabels().stream())
                .filter(label -> label.getLevel() != null && label.getLevel() == 3)
                .map(label -> {
                    PluginReportBadCommentInfoDto.CommentLabel commentLabel = new PluginReportBadCommentInfoDto.CommentLabel();
                    commentLabel.setName(label.getName());
                    commentLabel.setSentiment(label.getName());
                    return commentLabel;
                })
                .collect(Collectors.toList());
    }

    private Mono<PluginCommentStatisticsMonthReportVo> assembleCommentContentAnalysis(PluginCommentReportParams param, PluginCommentStatisticsMonthReportVo resultVo) {
        return Mono.zip(
                        pluginCommentIndexService.getLabelsWithSentiment(param, PluginCommentSentimentEnum.POSITIVE.getDesc()),
                        pluginCommentIndexService.getLabelsWithSentiment(param, PluginCommentSentimentEnum.NEGATIVE.getDesc())
                )
                .map(tuple -> {
                    List<Pair<String, Long>> positiveLabels = tuple.getT1();
                    List<Pair<String, Long>> negativeLabels = tuple.getT2();


                    resultVo.setPositiveCommentContentAnalysis(buildCommentContentAnalysisItem(positiveLabels));
                    resultVo.setNegativeCommentContentAnalysis(buildCommentContentAnalysisItem(negativeLabels));

                    return resultVo;
                });
    }

    private Mono<PluginCommentStatisticsMonthReportVo> assembleCompetitorAnalysis(
            PluginCommentReportParams param,
            PluginCommentStatisticsMonthReportVo resultVo) {

        return hotelCompetitorService.listCompetitorsByChannel(param.getHotelCode(), param.getChannels())
                .flatMap(competitorList -> buildCompetitorAnalysis(param, resultVo, competitorList))
                .flatMap(monthReportVo -> enrichWithScoreData(param, monthReportVo))
                .onErrorReturn(resultVo);
    }

    /**
     * 构建竞争对手分析数据
     */
    private Mono<PluginCommentStatisticsMonthReportVo> buildCompetitorAnalysis(
            PluginCommentReportParams param,
            PluginCommentStatisticsMonthReportVo resultVo,
            List<HotelCompetitorMultichannelVO> competitorList) {

        // 构建查询参数
        PluginCommentSearchParams searchParams = getSearchParams(param);

        // 获取本酒店标签数据
        Mono<List<Pair<String, Long>>> ownPositiveMono = pluginCommentIndexService
                .getLabelsWithSentiment(searchParams, PluginCommentSentimentEnum.POSITIVE.getDesc());
        Mono<List<Pair<String, Long>>> ownNegativeMono = pluginCommentIndexService
                .getLabelsWithSentiment(searchParams, PluginCommentSentimentEnum.NEGATIVE.getDesc());

        // 获取本酒店TOP5关键词
        Mono<List<String>> ownTopPositiveKeywordsMono = getTop5Tag(ownPositiveMono);
        Mono<List<String>> ownTopNegativeKeywordsMono = getTop5Tag(ownNegativeMono);

        // 获取竞争对手数据
        Mono<List<Tuple2<HotelCompetitorMultichannelVO, Tuple2<List<Pair<String, Long>>, List<Pair<String, Long>>>>>> competitorDataMono =
                getCompetitorLabelsData(param, competitorList);

        return Mono.zip(
                ownPositiveMono,
                ownNegativeMono,
                ownTopPositiveKeywordsMono,
                ownTopNegativeKeywordsMono,
                competitorDataMono
        ).map(tuple -> buildLabelAnalysis(param, resultVo, tuple));
    }

    /**
     * 获取竞争对手标签数据
     */
    private Mono<List<Tuple2<HotelCompetitorMultichannelVO, Tuple2<List<Pair<String, Long>>, List<Pair<String, Long>>>>>> getCompetitorLabelsData(
            PluginCommentReportParams param,
            List<HotelCompetitorMultichannelVO> competitorList) {

        SentimentLabelsQO queryParams = buildSentimentLabelsQO(param);

        List<Mono<Tuple2<HotelCompetitorMultichannelVO, Tuple2<List<Pair<String, Long>>, List<Pair<String, Long>>>>>> competitorDataMonos =
                competitorList.stream()
                        .map(competitor -> getCompetitorSentimentData(queryParams, competitor))
                        .collect(Collectors.toList());

        return Flux.fromIterable(competitorDataMonos)
                .flatMap(mono -> mono)
                .collectList();
    }

    /**
     * 获取单个竞争对手的情感数据
     */
    private Mono<Tuple2<HotelCompetitorMultichannelVO, Tuple2<List<Pair<String, Long>>, List<Pair<String, Long>>>>> getCompetitorSentimentData(
            SentimentLabelsQO queryParams,
            HotelCompetitorMultichannelVO competitor) {

        queryParams.setHotelCode(competitor.getCompetitorHotelId());

        Mono<List<Pair<String, Long>>> compPositiveMono = pluginSimpleCommentIndexService
                .getLabelsWithSentiment(queryParams, PluginCommentSentimentEnum.POSITIVE.getDesc());
        Mono<List<Pair<String, Long>>> compNegativeMono = pluginSimpleCommentIndexService
                .getLabelsWithSentiment(queryParams, PluginCommentSentimentEnum.NEGATIVE.getDesc());

        return Mono.zip(compPositiveMono, compNegativeMono)
                .map(tuple -> Tuples.of(competitor, tuple));
    }

    /**
     * 丰富评分数据
     */
    private Mono<PluginCommentStatisticsMonthReportVo> enrichWithScoreData(
            PluginCommentReportParams param,
            PluginCommentStatisticsMonthReportVo monthReportVo) {
        String channel = CollUtil.getFirst(param.getChannels());
        HdsHotelMappingQO mappingQuery = HdsHotelMappingQO.builder()
                .channel(channel)
                .hotelCode(param.getHotelCode())
                .build();

        return hotelMappingEntityDao.findOne(mappingQuery)
                .flatMap(entity -> {
                    if (Objects.isNull(entity)) {
                        return Mono.just(monthReportVo);
                    }
                    return fillHotelCommentScores(monthReportVo, entity.getExternalId(), channel);
                })
                .switchIfEmpty(Mono.just(monthReportVo));
    }

    /**
     * 更新酒店评分数据
     */
    private Mono<PluginCommentStatisticsMonthReportVo> fillHotelCommentScores(
            PluginCommentStatisticsMonthReportVo monthReportVo,
            String ourHotelExternalId,
            String channel) {

        List<String> hotelCodes = extractCompetitorHotelCodes(monthReportVo);
        hotelCodes.add(ourHotelExternalId);

        HotelCommentScoreQO scoreQuery = HotelCommentScoreQO.builder()
                .hotelCodes(hotelCodes)
                .channel(channel)
                .build();

        return hotelCommentScoreDao.findList(scoreQuery)
                .map(hotelCommentScores -> {
                    fillHotelAnalysisWithScores(monthReportVo, hotelCommentScores, ourHotelExternalId);
                    return monthReportVo;
                });
    }

    /**
     * 提取竞争对手酒店代码
     */
    private List<String> extractCompetitorHotelCodes(PluginCommentStatisticsMonthReportVo monthReportVo) {
        return monthReportVo.getCompetitorAnalysis().getHotelAnalysis()
                .stream()
                .filter(e -> !e.getOurHotel())
                .map(PluginCommentStatisticsMonthReportVo.HotelAnalysis::getCode)
                .collect(Collectors.toList());
    }

    /**
     * 更新酒店分析数据中的评分信息
     */
    private void fillHotelAnalysisWithScores(
            PluginCommentStatisticsMonthReportVo monthReportVo,
            List<HotelCommentScore> hotelCommentScores,
            String ourHotelExternalId) {

        Map<String, HotelCommentScore> hotelCode2CommentScore = hotelCommentScores
                .stream()
                .collect(Collectors.toMap(
                        HotelCommentScore::getHotelCode,
                        Function.identity(),
                        (existing, replacement) -> existing));

        monthReportVo.getCompetitorAnalysis().getHotelAnalysis().forEach(hotelAnalysis -> {
            String externalId = hotelAnalysis.getOurHotel() ? ourHotelExternalId : hotelAnalysis.getCode();
            PluginCommentStatisticsMonthReportVo.CategoryScoreTrends categoryScoreTrends =
                    new PluginCommentStatisticsMonthReportVo.CategoryScoreTrends();

            Optional.ofNullable(hotelCode2CommentScore.get(externalId))
                    .ifPresent(hotelCommentScore -> populateScoreTrends(categoryScoreTrends, hotelCommentScore));

            hotelAnalysis.setCategoryScoreTrends(categoryScoreTrends);
        });
    }

    /**
     * 填充评分趋势数据
     */
    private void populateScoreTrends(
            PluginCommentStatisticsMonthReportVo.CategoryScoreTrends categoryScoreTrends,
            HotelCommentScore hotelCommentScore) {

        BigDecimal defaultScoreValue = BigDecimal.ZERO;
        BigDecimal hygieneScore = Optional.ofNullable(hotelCommentScore.getHygieneScore())
                .orElse(defaultScoreValue);
        BigDecimal facilitiesScore = Optional.ofNullable(hotelCommentScore.getFacilitiesScore())
                .orElse(defaultScoreValue);
        BigDecimal serviceScore = Optional.ofNullable(hotelCommentScore.getServiceScore())
                .orElse(defaultScoreValue);
        BigDecimal environmentScore = Optional.ofNullable(hotelCommentScore.getEnvironmentScore())
                .orElse(defaultScoreValue);

        categoryScoreTrends.setHygieneScore(hygieneScore);
        categoryScoreTrends.setFacilitiesScore(facilitiesScore);
        categoryScoreTrends.setServiceScore(serviceScore);
        categoryScoreTrends.setEnvironmentScore(environmentScore);
        categoryScoreTrends.setScoreCollectionTime(hotelCommentScore.getScoreCollectionTime());
    }

    /**
     * 构建搜索参数
     */
    private PluginCommentSearchParams getSearchParams(PluginCommentReportParams param) {
        return PluginCommentSearchParams.builder()
                .hotelCode(param.getHotelCode())
                .channels(param.getChannels())
                .startDateTime(param.getStartDateTime())
                .endDateTime(param.getEndDateTime())
                .build();
    }

    /**
     * 构建情感标签查询对象
     */
    private SentimentLabelsQO buildSentimentLabelsQO(PluginCommentReportParams param) {
        SentimentLabelsQO queryParams = new SentimentLabelsQO();
        Pair<LocalDate, LocalDate> localDate = param.toLocalDate();
        queryParams.setStartDateTime(localDate.getFirst().toString());
        queryParams.setEndDateTime(localDate.getSecond().toString());
        queryParams.setChannels(param.getChannels());
        return queryParams;
    }

    @NotNull
    private PluginCommentStatisticsMonthReportVo buildLabelAnalysis(PluginCommentReportParams param, PluginCommentStatisticsMonthReportVo resultVo
            , Tuple5<List<Pair<String, Long>>, List<Pair<String, Long>>, List<String>, List<String>, List<Tuple2<HotelCompetitorMultichannelVO, Tuple2<List<Pair<String, Long>>, List<Pair<String, Long>>>>>> tuple) {
        List<Pair<String, Long>> ownPositive = tuple.getT1();
        List<Pair<String, Long>> ownNegative = tuple.getT2();
        List<String> topPositiveKeywords = tuple.getT3();
        List<String> topNegativeKeywords = tuple.getT4();
        List<Tuple2<HotelCompetitorMultichannelVO, Tuple2<List<Pair<String, Long>>, List<Pair<String, Long>>>>> competitorData = tuple.getT5();

        // 5. 构建 CompetitorAnalysis
        PluginCommentStatisticsMonthReportVo.CompetitorAnalysis competitorAnalysis =
                new PluginCommentStatisticsMonthReportVo.CompetitorAnalysis();

        List<PluginCommentStatisticsMonthReportVo.HotelAnalysis> hotelAnalysisList = new ArrayList<>();


        PluginCommentStatisticsMonthReportVo.HotelAnalysis ownHotelAnalysis =
                createHotelAnalysis("我的酒店", param.getHotelCode(), true,
                        ownPositive, ownNegative,
                        topPositiveKeywords, topNegativeKeywords);
        hotelAnalysisList.add(ownHotelAnalysis);

        // 添加竞争对手分析
        for (Tuple2<HotelCompetitorMultichannelVO, Tuple2<List<Pair<String, Long>>, List<Pair<String, Long>>>> compData : competitorData) {
            HotelCompetitorMultichannelVO competitor = compData.getT1();
            List<Pair<String, Long>> compPositiveLabels = compData.getT2().getT1();
            List<Pair<String, Long>> compNegativeLabels = compData.getT2().getT2();

            // 过滤出包含本酒店TOP5关键词的标签
            List<Pair<String, Long>> filteredPositiveLabels = compPositiveLabels.stream()
                    .filter(label -> topPositiveKeywords.contains(label.getFirst()))
                    .toList();

            List<Pair<String, Long>> filteredNegativeLabels = compNegativeLabels.stream()
                    .filter(label -> topNegativeKeywords.contains(label.getFirst()))
                    .toList();

            PluginCommentStatisticsMonthReportVo.HotelAnalysis competitorAnalysisItem =
                    createHotelAnalysis(
                            competitor.getCompetitorHotelName(),
                            competitor.getCompetitorHotelId(),
                            false,
                            CollUtil.isEmpty(filteredNegativeLabels) ? Collections.emptyList() : compPositiveLabels,
                            CollUtil.isEmpty(filteredPositiveLabels) ? Collections.emptyList() : compNegativeLabels,
                            topPositiveKeywords,
                            topNegativeKeywords
                    );

            hotelAnalysisList.add(competitorAnalysisItem);

        }

        competitorAnalysis.setHotelAnalysis(hotelAnalysisList);
        resultVo.setCompetitorAnalysis(competitorAnalysis);

        return resultVo;
    }

    private Mono<List<String>> getTop5Tag(Mono<List<Pair<String, Long>>> tagMono) {
        return tagMono.map(labels -> labels.stream()
                .sorted((a, b) -> Long.compare(b.getSecond(), a.getSecond())) // 按数量降序排序
                .limit(5) // 取前5个
                .map(Pair::getFirst) // 提取标签名称
                .collect(Collectors.toList()));
    }

    /**
     * 创建酒店分析（通用方法）
     */
    private PluginCommentStatisticsMonthReportVo.HotelAnalysis createHotelAnalysis(
            String hotelName,
            String hotelCode,
            Boolean isOurHotel,
            List<Pair<String, Long>> positiveLabels,
            List<Pair<String, Long>> negativeLabels,
            List<String> topPositiveKeywords,
            List<String> topNegativeKeywords) {

        PluginCommentStatisticsMonthReportVo.HotelAnalysis hotelAnalysis =
                new PluginCommentStatisticsMonthReportVo.HotelAnalysis();
        hotelAnalysis.setName(hotelName);
        hotelAnalysis.setCode(hotelCode);
        hotelAnalysis.setOurHotel(isOurHotel);

        // 创建标签分析
        PluginCommentStatisticsMonthReportVo.LabelAnalysis labelAnalysis =
                new PluginCommentStatisticsMonthReportVo.LabelAnalysis();
        labelAnalysis.setPositiveLabels(convertToLabels(positiveLabels, topPositiveKeywords));
        labelAnalysis.setNegativeLabels(convertToLabels(negativeLabels, topNegativeKeywords));

        hotelAnalysis.setLabelAnalysis(labelAnalysis);
        return hotelAnalysis;
    }

    /**
     * 创建本酒店分析
     */
    private PluginCommentStatisticsMonthReportVo.HotelAnalysis createOwnHotelAnalysis(
            String hotelCode,
            List<Pair<String, Long>> ownPositive,
            List<Pair<String, Long>> ownNegative,
            List<String> topPositiveKeywords,
            List<String> topNegativeKeywords) {

        PluginCommentStatisticsMonthReportVo.HotelAnalysis ownHotelAnalysis =
                new PluginCommentStatisticsMonthReportVo.HotelAnalysis();
        ownHotelAnalysis.setName("我的酒店");
        ownHotelAnalysis.setCode(hotelCode);
        ownHotelAnalysis.setOurHotel(true);

        // 创建标签分析
        PluginCommentStatisticsMonthReportVo.LabelAnalysis labelAnalysis =
                new PluginCommentStatisticsMonthReportVo.LabelAnalysis();
        labelAnalysis.setPositiveLabels(convertToLabels(ownPositive, topPositiveKeywords));
        labelAnalysis.setNegativeLabels(convertToLabels(ownNegative, topNegativeKeywords));

        ownHotelAnalysis.setLabelAnalysis(labelAnalysis);
        return ownHotelAnalysis;
    }

    /**
     * 创建竞争对手酒店分析
     */
    private PluginCommentStatisticsMonthReportVo.HotelAnalysis createCompetitorHotelAnalysis(
            HotelCompetitorMultichannelVO competitor,
            List<Pair<String, Long>> compPositiveLabels,
            List<Pair<String, Long>> compNegativeLabels,
            List<String> topPositiveKeywords,
            List<String> topNegativeKeywords) {

        PluginCommentStatisticsMonthReportVo.HotelAnalysis competitorAnalysisItem =
                new PluginCommentStatisticsMonthReportVo.HotelAnalysis();
        competitorAnalysisItem.setName(competitor.getCompetitorHotelName());
        competitorAnalysisItem.setCode(competitor.getCompetitorHotelId());
        competitorAnalysisItem.setOurHotel(false);

        // 创建标签分析
        PluginCommentStatisticsMonthReportVo.LabelAnalysis labelAnalysis =
                new PluginCommentStatisticsMonthReportVo.LabelAnalysis();
        labelAnalysis.setPositiveLabels(convertToLabels(compPositiveLabels, topPositiveKeywords));
        labelAnalysis.setNegativeLabels(convertToLabels(compNegativeLabels, topNegativeKeywords));

        competitorAnalysisItem.setLabelAnalysis(labelAnalysis);
        return competitorAnalysisItem;
    }


    private List<PluginCommentStatisticsMonthReportVo.Label> convertToLabels(
            List<Pair<String, Long>> labelPairs,
            List<String> myHotelTop5Keywords) {

        return myHotelTop5Keywords.stream()
                .map(keyword -> {
                    PluginCommentStatisticsMonthReportVo.Label label = new PluginCommentStatisticsMonthReportVo.Label();
                    label.setName(keyword);

                    Long count = labelPairs.stream()
                            .filter(pair -> pair.getFirst().equals(keyword))
                            .map(Pair::getSecond)
                            .findFirst()
                            .orElse(0L); // 如果没有找到，设置为0

                    label.setCount(count);
                    return label;
                })
                .collect(Collectors.toList());
    }


    private PluginCommentStatisticsMonthReportVo.CommentContentAnalysisItem buildCommentContentAnalysisItem(List<Pair<String, Long>> positiveLabels) {

        long sum = positiveLabels.stream().mapToLong(Pair::getSecond).sum();
        int top = 5;
        long otherCount = 0;
        List<PluginCommentStatisticsMonthReportVo.Label> labels = new ArrayList<>();
        List<Pair<String, Long>> sortList = CollUtil.sort(positiveLabels, (o1, o2) -> o2.getSecond().compareTo(o1.getSecond()));

        for (int i = 0; i < sortList.size(); i++) {
            Pair<String, Long> e = sortList.get(i);

            if (i < top) {
                PluginCommentStatisticsMonthReportVo.Label label = new PluginCommentStatisticsMonthReportVo.Label();
                label.setName(e.getFirst());
                label.setLevel(3);
                Long count = e.getSecond();
                label.setCount(count);
                label.setProportion((int) (count * 1.0 / sum * 100));
                labels.add(label);
            } else {
                otherCount += e.getSecond();
            }
        }

        if (otherCount > 0) {
            PluginCommentStatisticsMonthReportVo.Label label = new PluginCommentStatisticsMonthReportVo.Label();
            label.setName("其他");
            label.setLevel(3);
            label.setCount(otherCount);
            label.setProportion((int) (otherCount / sum * 100));
            labels.add(label);
        }

        return new PluginCommentStatisticsMonthReportVo.CommentContentAnalysisItem(labels);
    }

    private <T> Mono<T> assembleCommentCountInterval(PluginCommentSearchParams params, PluginCommentStatisticsIntervalEnum intervalEnum
            , Consumer3<T, List<ScoreItem>, List<ScoreItem>> commentScoreConsumer, T resultVo) {

        return pluginCommentIndexService.countCommentCountsByInterval(params, intervalEnum)
                .map(dto -> {
                    String startDateTime = params.getStartDateTime();
                    String endDateTime = params.getEndDateTime();
                    List<Pair<String, String>> dateList = rangToList(startDateTime, endDateTime, intervalEnum.getDateField(), DatePattern.NORM_DATE_PATTERN);

                    Pair<List<ScoreItem>, List<ScoreItem>> commentCountList = buildCommentCountList(dateList, dto.getIntervals());
                    commentScoreConsumer.accept(resultVo, commentCountList.getFirst(), commentCountList.getSecond());
                    return resultVo;
                });
    }

    private <T> Mono<T> assembleCommentScoreInterval(PluginCommentSearchParams params, PluginCommentStatisticsIntervalEnum intervalEnum
            , BiConsumer<T, List<ScoreItem>> commentScoreConsumer, T resultVo) {

        return pluginCommentIndexService.avgCommentScoresByInterval(params, intervalEnum)
                .map(commentScoreIntervalStatsDto -> {
                    String startDateTime = params.getStartDateTime();
                    String endDateTime = params.getEndDateTime();
                    List<Pair<String, String>> dateList = rangToList(startDateTime, endDateTime, intervalEnum.getDateField(), DatePattern.NORM_DATE_PATTERN);

                    List<PluginCommentScoreIntervalStatsDto.TimeIntervalScore> intervals = commentScoreIntervalStatsDto.getIntervals();

                    List<ScoreItem> commentScoreList = buildCommentScoreList(dateList, intervals);
                    commentScoreConsumer.accept(resultVo, commentScoreList);
                    return resultVo;
                });
    }


    private List<Pair<String, String>> rangToList(String startDate, String endDate, DateField field, String datePattern) {
        return rangeToList(DateUtil.parse(startDate)
                , DateUtil.parse(endDate), field)
                .stream()
                .map(e -> Pair.of(
                        DateUtil.format(e.getFirst(), datePattern)
                        , DateUtil.format(e.getSecond(), datePattern))
                ).toList();
    }

    private List<Pair<Date, Date>> rangeToList(Date startDate, Date endDate, DateField field) {
        List<Pair<Date, Date>> resultList = new ArrayList<>();


        Date cursorStartDate = startDate;


        while (cursorStartDate.before(endDate) || cursorStartDate.equals(endDate)) {
            Calendar cursorStartCalendar = DateUtil.calendar(cursorStartDate);
            cursorStartCalendar.setFirstDayOfWeek(Calendar.MONDAY);

            //改为周期起始时间
            cursorStartCalendar = DateUtil.truncate(cursorStartCalendar, field);
            cursorStartDate = cursorStartCalendar.getTime();

            Calendar cursorEndCalendar = DateUtil.ceiling(cursorStartCalendar, field);
            Date cursorEndDate = cursorEndCalendar.getTime();

//            if (cursorEndDate.after(endDate)) {
//                cursorEndDate = endDate;
//            }

            resultList.add(Pair.of(cursorStartDate, cursorEndDate));

            cursorStartDate = DateUtil.offset(cursorStartDate, field, 1);
        }
        return resultList;
    }


    private Pair<List<ScoreItem>, List<ScoreItem>> buildCommentCountList(List<Pair<String, String>> dateList, List<PluginCommentCountIntervalStatsDto.TimeIntervalStats> intervals) {
        List<ScoreItem> positiveList = new ArrayList<>(intervals.size());
        List<ScoreItem> negativeList = new ArrayList<>(intervals.size());

        Map<String, PluginCommentCountIntervalStatsDto.TimeIntervalStats> map = intervals.stream()
                .collect(Collectors.toMap(PluginCommentCountIntervalStatsDto.TimeIntervalStats::getIntervalStart
                        , Function.identity(), (e1, e2) -> e2));

        dateList
                .forEach(datePair -> {
                    String startDate = datePair.getFirst();
                    String endDate = datePair.getSecond();

                    PluginCommentCountIntervalStatsDto.TimeIntervalStats timeIntervalStats = map.get(startDate);

                    Double negativeCommentCount = Optional.ofNullable(timeIntervalStats)
                            .map(PluginCommentCountIntervalStatsDto.TimeIntervalStats::getNegativeCommentCount)
                            .map(Double::valueOf)
                            .orElse(0D);

                    Double positiveCommentCount = Optional.ofNullable(timeIntervalStats)
                            .map(PluginCommentCountIntervalStatsDto.TimeIntervalStats::getPositiveCommentCount)
                            .map(Double::valueOf)
                            .orElse(0D);

                    ScoreItem positive = new ScoreItem(positiveCommentCount, startDate, endDate);
                    positiveList.add(positive);

                    ScoreItem negative = new ScoreItem(negativeCommentCount, startDate, endDate);
                    negativeList.add(negative);
                });


        return Pair.of(positiveList, negativeList);
    }


    private List<ScoreItem> buildCommentScoreList(List<Pair<String, String>> dateList, List<PluginCommentScoreIntervalStatsDto.TimeIntervalScore> intervals) {
        Map<String, PluginCommentScoreIntervalStatsDto.TimeIntervalScore> map = intervals
                .stream()
                .collect(Collectors.toMap(PluginCommentScoreIntervalStatsDto.TimeIntervalScore::getIntervalStart
                        , Function.identity(), (e1, e2) -> e2)
                );

        return dateList.stream()
                .map(datePair -> {
                    String startDate = datePair.getFirst();
                    String endDate = datePair.getSecond();
                    PluginCommentScoreIntervalStatsDto.TimeIntervalScore timeIntervalScore = map.get(startDate);

                    Double value = Optional.ofNullable(timeIntervalScore)
                            .map(PluginCommentScoreIntervalStatsDto.TimeIntervalScore::getAvgScore)
                            .orElse(0D);

                    ScoreItem scoreItem = new ScoreItem();
                    scoreItem.setValue(value);
                    scoreItem.setStartDate(startDate);
                    scoreItem.setEndDate(endDate);
                    return scoreItem;
                }).toList();
    }

    private PluginCommentReportRecord buildCommentInitReportRecord(PluginCommentReportParams params) {

        PluginCommentStatisticsIntervalEnum reportIntervalType = PluginCommentStatisticsIntervalEnum
                .ofValue(params.getReportIntervalType())
                .orElse(PluginCommentStatisticsIntervalEnum.MONTH);

        Pair<LocalDateTime, LocalDateTime> localDateTime = params.toLocalDateTime();

        PluginCommentReportRecord pluginCommentReportRecord = new PluginCommentReportRecord();

        pluginCommentReportRecord.setReportIntervalType(reportIntervalType.getValue());
        pluginCommentReportRecord.setState(PluginCommentStatisticsRecordStateEnum.GENERATING.getValue());
        pluginCommentReportRecord.setStartDate(localDateTime.getFirst());
        pluginCommentReportRecord.setEndDate(localDateTime.getSecond());
        pluginCommentReportRecord.setHotelCode(params.getHotelCode());
        pluginCommentReportRecord.setChannels(Optional.ofNullable(params.getChannels())
                .map(e -> String.join(",", e))
                .orElse(""));
        pluginCommentReportRecord.setResult(StrUtil.EMPTY_JSON);

        return pluginCommentReportRecord;
    }


    public Mono<Boolean> createReportTask(PluginCommentReportParams params) {
        Preconditions.checkNotNull(params.getHotelCode(), "酒店code不能为空");
        if (Objects.isNull(params.getYear()) && Objects.isNull(params.getMonth()) && Objects.isNull(params.getWeek())) {
            LocalDateTime now = LocalDateTime.now().minusMonths(1);
            params.setYear(now.getYear());
            params.setMonth(now.getMonthValue());
        }
        params.initDateTimeRange();
        PluginCommentStatisticsIntervalEnum reportIntervalType = PluginCommentStatisticsIntervalEnum
                .ofValue(params.getReportIntervalType())
                .orElse(PluginCommentStatisticsIntervalEnum.MONTH);

        PluginCommentReportRecord pluginCommentReportRecord = buildCommentInitReportRecord(params);

        String lockName = RedisConstant.PluginRedisKeyConstant.getCreateReportLockKey(reportIntervalType, pluginCommentReportRecord.getHotelCode()
                , pluginCommentReportRecord.getChannels(), pluginCommentReportRecord.getStartDate(), pluginCommentReportRecord.getEndDate()
        );

        return lockUtils.lock(
                lockName,
                60 * 1000,
                () -> {
                    // 是否已经存在
                    return pluginCommentReportRecordService.existReportRecord(
                                    pluginCommentReportRecord.getReportIntervalType(), pluginCommentReportRecord.getHotelCode()
                                    , pluginCommentReportRecord.getChannels(), pluginCommentReportRecord.getStartDate(), pluginCommentReportRecord.getEndDate()
                            )
                            .flatMap(exist -> {
                                if (exist) {
                                    return Mono.just(Boolean.TRUE);
                                } else {
                                    return hdsHotelInfoDao.queryByHotelCode(params.getHotelCode())
                                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "酒店信息不存在，无法生成报告。")))
                                            .flatMap(hotelInfoEntity -> canCreateReport(hotelInfoEntity, params)
                                                    .filter(Boolean::booleanValue)
                                                    .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "数据未抓取完整，无法生成报告。")))
                                                    // 保存 构建报告的任务
                                                    .flatMap(e -> pluginCommentReportRecordRepository.save(pluginCommentReportRecord))
                                                    .flatMap(e -> createCommentDataToEsTask(e.getId(), Objects.equals(reportIntervalType, PluginCommentStatisticsIntervalEnum.MONTH) ?
                                                                    params.cloneWithOffsetStartDateTime(-6, DateField.MONTH) : params, hotelInfoEntity
                                                            , TaskScheduleJobTypeEnum.real_time_save_inner_hotel_index).then(Mono.just(e))
                                                    )
                                                    .flatMap(e -> createCommentDataToEsTask(e.getId(), params, hotelInfoEntity, TaskScheduleJobTypeEnum.real_time_save_competing_index)
                                                            .then(Mono.just(e))
                                                    )
                                                    .flatMap(e -> createCommentDataToEsTask(e.getId(), params, hotelInfoEntity, TaskScheduleJobTypeEnum.do_create_initialization_report))
                                                    .flatMap(e -> pluginCollectionDetailsService.save(PluginCollectionDetails.build(params.getHotelCode(), hotelInfoEntity.getHotelName(), PluginCollectType.HOTEL_REPORT_GENERATED.getCode(), 1)))
                                            );

                                }
                            });
                }
        );
    }

    private Mono<Boolean> createEbkToEsMonthTask(Long recordId, PluginCommentReportParams params, HdsHotelInfoEntity hotelInfo) {
        int monthsToCompare = pluginCommentProperties.getMonthsToCompare();
        int yearToCompare = pluginCommentProperties.getYearToCompare();
        PluginCommentSearchParams thisYearSearchParams = params.cloneWithOffsetStartDateTime(-(monthsToCompare - 1), DateField.MONTH);
        // 构造往年查询
        PluginCommentSearchParams previousYearSearchParams = thisYearSearchParams.cloneWithOffsetDateTime(-yearToCompare, DateField.YEAR);

        return createCommentDataToEsTask(recordId, thisYearSearchParams, hotelInfo, TaskScheduleJobTypeEnum.real_time_save_ebk_comment_index)
                .filter(Boolean::booleanValue)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "近六月ebk数据落库到es任务创建失败")))

                .flatMap(e -> createCommentDataToEsTask(recordId, previousYearSearchParams, hotelInfo, TaskScheduleJobTypeEnum.real_time_save_ebk_comment_index))
                .filter(Boolean::booleanValue)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "往年同期ebk数据落库到es任务创建失败")))
                .doOnError(error -> log.error("创建ebk落库到es任务失败", error));

    }

    private Mono<Boolean> createEbkToEsWeekTask(Long recordId, PluginCommentReportParams params, HdsHotelInfoEntity hotelInfo) {
        Integer weeksToCompare = pluginCommentProperties.getWeeksToCompare();

        // 构造今年 近4周查询
        PluginCommentSearchParams searchParams = params.cloneWithOffsetStartDateTime(-(weeksToCompare - 1), DateField.WEEK_OF_YEAR);

        return createCommentDataToEsTask(recordId, searchParams, hotelInfo, TaskScheduleJobTypeEnum.real_time_save_ebk_comment_index);
    }


    private Mono<Boolean> createCommentDataToEsTask(Long recordId, PluginCommentSearchParams params, HdsHotelInfoEntity hotelInfo, TaskScheduleJobTypeEnum jobTypeEnum) {
        Pair<LocalDateTime, LocalDateTime> localDateTime = params.toLocalDateTime();
        // ebk数据 -> es 任务
        PluginInitializationReportDTO pluginInitializationReportDTO = new PluginInitializationReportDTO(
                params.getHotelCode(), hotelInfo.getHotelName(), localDateTime.getFirst(), localDateTime.getSecond(), recordId, null
        );
        TaskScheduleDetailEntity taskScheduleDetailEntity = new TaskScheduleDetailEntity();
        taskScheduleDetailEntity.setJobType(jobTypeEnum.getJobType());
        taskScheduleDetailEntity.setJobName(hotelInfo.getHotelName());
        return HeaderUtils.getHeaderInfo()
                .map(headerInfo -> pluginTaskService.buildTaskScheduleDetailEntity(
                                pluginInitializationReportDTO, taskScheduleDetailEntity, headerInfo
                        )
                )
                .flatMap(taskDetailEntity -> taskScheduleDetailRepository.save(taskDetailEntity))
                .map(Objects::nonNull);
    }


    /**
     * 是否可以生成报告
     */
    private Mono<Boolean> canCreateReport(HdsHotelInfoEntity hotelInfo, PluginCommentReportParams params) {
        Pair<LocalDateTime, LocalDateTime> localDateTime = params.toLocalDateTime();
        String hotelCode = params.getHotelCode();
        LocalDateTime endDateTime = localDateTime.getSecond();

        Mono<Long> lastCaptureCompetingHotelsTimestamp = pluginTaskService.getHotelLastCollectionTime(hotelCode);
        return lastCaptureCompetingHotelsTimestamp
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "竞品酒店数据未抓取，无法生成报告。")))
                .map(timestamp -> {
                    long epochMilli = LocalDateTimeUtil.toEpochMilli(endDateTime);
                    log.info("canCreate report lastCaptureCompetingHotelsTimestamp:{} epochMilli:{} ", timestamp, epochMilli);
                    return timestamp > epochMilli;
                }) .filter(Boolean::booleanValue)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "竞品酒店数据未抓取，无法生成报告。")));

    }

    public Mono<PluginCommentStatisticsReportVo> getReportInfo(PluginCommentReportParams params) {
        Preconditions.checkNotNull(params.getHotelCode(), "酒店code不能为空");
        params.initDateTimeRange();

        Pair<LocalDateTime, LocalDateTime> localDateTime = params.toLocalDateTime();

        PluginCommentStatisticsReportVo pluginCommentStatisticsReportVo = new PluginCommentStatisticsReportVo();
        pluginCommentStatisticsReportVo.setHotelCode(params.getHotelCode());
        pluginCommentStatisticsReportVo.setMonth(params.getMonth());
        pluginCommentStatisticsReportVo.setWeek(params.getWeek());
        pluginCommentStatisticsReportVo.setReportIntervalType(params.getReportIntervalType());

        return pluginCommentReportRecordService.findStateByTimeRange(
                        params.getHotelCode(), params.getReportIntervalType(), localDateTime.getFirst(), localDateTime.getSecond()
                )
                .switchIfEmpty(Mono.just(PluginCommentStatisticsRecordStateEnum.INIT.getValue()))
                .map(e -> {
                    pluginCommentStatisticsReportVo.setState(e);
                    return pluginCommentStatisticsReportVo;
                })
                ;
    }

    public Mono<PluginCommentStatisticsReportVo> monthReportInitInfo(PluginCommentReportParams params) {
        Preconditions.checkNotNull(params.getHotelCode(), "酒店code不能为空");
        return pluginCommentReportRecordService.findInitReportRecord(params.getHotelCode(), params.getReportIntervalType())
                .map(reportRecord -> {
                    PluginCommentStatisticsReportVo pluginCommentStatisticsReportVo = new PluginCommentStatisticsReportVo();
                    pluginCommentStatisticsReportVo.setHotelCode(reportRecord.getHotelCode());
                    LocalDateTime startDate = reportRecord.getStartDate();
                    pluginCommentStatisticsReportVo.setMonth(startDate.getMonthValue());
                    int weekOfYear = startDate.get(WeekFields.ISO.weekOfWeekBasedYear());
                    pluginCommentStatisticsReportVo.setWeek(weekOfYear);
                    pluginCommentStatisticsReportVo.setReportIntervalType(reportRecord.getReportIntervalType());
                    pluginCommentStatisticsReportVo.setState(reportRecord.getState());
                    return pluginCommentStatisticsReportVo;
                })
                .switchIfEmpty(Mono.just(PluginCommentStatisticsReportVo.buildEmpty(PluginCommentStatisticsRecordStateEnum.GENERATE_FAIL.getValue())))
                ;
    }
}
