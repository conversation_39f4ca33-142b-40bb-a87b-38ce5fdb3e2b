package com.wormhole.hotelds.plugin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.*;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.plugin.dao.PluginCollectionSummaryDao;
import com.wormhole.hotelds.plugin.model.entity.PluginCollectionDetails;
import com.wormhole.hotelds.plugin.model.enums.PluginCollectType;
import com.wormhole.hotelds.plugin.model.qo.*;
import com.wormhole.hotelds.plugin.model.vo.PluginCrawlDataDetailDataVO;
import com.wormhole.hotelds.plugin.model.vo.PluginCrawlDataIndicatorTrend;
import com.wormhole.hotelds.plugin.model.vo.PluginCrawlDataSummaryVO;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 插件采集数据仪表板服务
 *
 * <AUTHOR>
 * @date 2025/8/26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PluginCrawlDataDashboardService {

    private final PluginCollectionSummaryDao pluginCollectionSummaryDao;
    private final PluginCollectionDetailsService pluginCollectionDetailsService;

    /**
     * 获取插件采集数据统计
     *
     * @param qo 查询条件
     * @return 统计结果
     */
    public Mono<PluginCrawlDataSummaryVO>  getSummaryStatistics(PluginCrawlDataSummaryQO qo) {
        log.info("开始获取插件采集数据统计，查询条件: {}", qo);

        // 参数校验
        if (qo.getStartDate() == null || qo.getEndDate() == null) {
            log.error("开始日期和结束日期不能为空");
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,"开始日期和结束日期不能为空"));
        }

        if (qo.getStartDate().isAfter(qo.getEndDate())) {
            log.error("开始日期不能晚于结束日期");
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,"开始日期不能晚于结束日期"));
        }

        return pluginCollectionSummaryDao.getSummaryStatistics(qo)
                .doOnSuccess(result -> log.info("插件采集数据统计获取成功: {}", result))
                .doOnError(error -> log.error("插件采集数据统计获取失败，查询条件: {}", qo, error));
    }

    /**
     * 获取插件采集数据详情
     *
     * @param qo 查询条件
     * @return 分页和排序后的详情数据
     */
    public Mono<PageResult<PluginCrawlDataDetailDataVO>> getDetailData(PluginCrawlDataDetailQO qo) {
        log.info("开始获取插件采集数据详情，查询条件: {}", qo);

        if (qo.getStartDate() == null || qo.getEndDate() == null) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,"开始日期和结束日期不能为空"));
        }


        Mono<Long> totalMono = pluginCollectionSummaryDao.countDistinctHotels(qo)
                .doOnError(e -> log.error("查询详情总数失败, qo: {}", qo, e));

        Mono<List<PluginCrawlDataDetailDataVO>> dataMono = pluginCollectionSummaryDao.getDetailData(qo)
                .collectList()
                .doOnError(e -> log.error("查询详情数据列表失败, qo: {}", qo, e));

        return Mono.zip(totalMono, dataMono, (total, dataList) -> {
            PageResult<PluginCrawlDataDetailDataVO> pageResult = new PageResult<>();
            pageResult.setTotal(total);
            pageResult.setDataList(dataList);
            pageResult.setPageSize(qo.getPageSize());
            pageResult.setCurrentPage(qo.getCurrentPage());
            log.info("获取插件采集数据详情成功，返回 {} 条数据，总计 {} 条", dataList.size(), total);
            return pageResult;
        });
    }

   public   Mono<List<PluginCrawlDataIndicatorTrend>> getIndicatorTrend(IndicatorTrendQO qo) {
        if (Objects.isNull(qo.getStartDate()) || Objects.isNull(qo.getEndDate())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,"开始日期和结束日期不能为空"));
        }


        return pluginCollectionSummaryDao.getTopMetricHotelName(qo).flatMap(
                totalHotelNames -> {
                    if(CollUtil.isEmpty(totalHotelNames)) {
                        return Mono.just(Collections.<HotelMetricsVO>emptyList());
                    }

                    HotelMetricsQO hotelMetricsQO = new HotelMetricsQO();
                    hotelMetricsQO.setStartDate(qo.getStartDate());
                    hotelMetricsQO.setEndDate(qo.getEndDate());
                    hotelMetricsQO.setHotelNames(totalHotelNames);
                    hotelMetricsQO.setSortPluginCollectType(qo.getSortPluginCollectType());

                    return pluginCollectionSummaryDao.getHotelMetrics(hotelMetricsQO);
                }
        ).map(
                result -> {

                    DateTime start = DateUtil.parse(qo.getStartDate(), DatePattern.NORM_DATE_PATTERN);
                    DateTime end = DateUtil.parse(qo.getEndDate(), DatePattern.NORM_DATE_PATTERN);
                    List<DateTime> dateTimes = DateUtil.rangeToList(start, end, DateField.DAY_OF_MONTH);
                    return dateTimes.stream().map(
                            dateTime -> {
                                PluginCrawlDataIndicatorTrend indicatorTrend = new PluginCrawlDataIndicatorTrend();
                                indicatorTrend.setDate(dateTime.toString(DatePattern.NORM_DATE_PATTERN));
                                List<PluginCrawlDataIndicatorTrend.MetricsData> collect1 = result
                                        .stream()
                                        .filter(Objects::nonNull)
                                        .filter(e -> Objects.equals(e.getDate().toString(), dateTime.toString(DatePattern.NORM_DATE_PATTERN)))
                                        .map(e -> new PluginCrawlDataIndicatorTrend.MetricsData(e.getHotelName(), e.getCount())).collect(Collectors.toList());
                                indicatorTrend.setMetricsDataList(collect1);

                                return indicatorTrend;
                            }
                    ).collect(Collectors.toList());
                }

        );

    }
    public Mono<Boolean> pluginDataCollect(PluginDataCollectQO pluginDataCollectQO) {
        log.info("开始保存指标采集数据，参数: {}", JacksonUtils.writeValueAsString(pluginDataCollectQO));

        PluginCollectType pluginCollectType = PluginCollectType.valueOf(pluginDataCollectQO.getCollectType());
        if (Objects.isNull(pluginCollectType) || Objects.isNull(pluginDataCollectQO.getCount()) || pluginDataCollectQO.getCount() <= 0) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "插件采集类型错误"));
        }

        PluginCollectionDetails build = PluginCollectionDetails.build(pluginDataCollectQO.getHotelCode(), pluginDataCollectQO.getHotelName(), pluginCollectType.getCode(), pluginDataCollectQO.getCount());
        return pluginCollectionDetailsService.save(build);

    }
}
