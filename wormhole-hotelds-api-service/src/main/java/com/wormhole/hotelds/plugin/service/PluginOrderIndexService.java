package com.wormhole.hotelds.plugin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregate;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregation;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsBucket;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import co.elastic.clients.json.JsonData;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.base.Preconditions;
import com.wormhole.agent.core.util.Querys;
import com.wormhole.common.constant.BooleanEnum;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.plugin.model.dto.LatestOrderDTO;
import com.wormhole.hotelds.plugin.model.dto.LatestOrderItem;
import com.wormhole.hotelds.plugin.model.dto.PlatformStatsItem;
import com.wormhole.hotelds.plugin.model.dto.PluginOrderStatsDto;
import com.wormhole.hotelds.plugin.model.entity.PluginCollectionDetails;
import com.wormhole.hotelds.plugin.model.enums.PluginCollectType;
import com.wormhole.hotelds.plugin.model.enums.PluginOrderTabEnum;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderDealQo;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderPageByTabQo;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderQo;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderTabListQo;
import com.wormhole.hotelds.plugin.model.vo.PluginOrderTabVo;
import com.wormhole.hotelds.plugin.model.vo.PluginOrderUpdateMoveStatusVo;
import com.wormhole.task.model.constant.PluginOrderMoveStatusEnum;
import com.wormhole.task.model.constant.PluginOrderStatusEnum;
import com.wormhole.task.model.constant.PluginOutCallStatusEnum;
import com.wormhole.task.model.constant.PluginPlatformEnum;
import com.wormhole.task.model.dto.PluginCtripOrderDTO;
import com.wormhole.task.model.dto.PluginOrderJsonDTO;
import com.wormhole.task.model.entity.filed.PluginOrderField;
import com.wormhole.task.model.entity.index.PluginOrderIndex;
import com.wormhole.task.model.vo.PluginOrderVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchAggregations;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.AggregationsContainer;
import org.springframework.data.elasticsearch.core.ReactiveElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.Criteria;
import org.springframework.data.elasticsearch.core.query.CriteriaQuery;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.data.elasticsearch.core.query.UpdateResponse;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class PluginOrderIndexService {

    @Resource
    private ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;

    @Resource
    private ReactiveElasticsearchOperations reactiveElasticsearchOperations;

    @Resource
    private PluginOrderRecordService pluginOrderRecordService;
    @Autowired
    private PluginCollectionDetailsService pluginCollectionDetailsService;

    public Mono<PluginOrderIndex> saveOrderIndex(PluginOrderIndex orderIndex) {
        return reactiveElasticsearchTemplate.save(orderIndex)
                .doOnSuccess(e -> log.info("PluginOrderIndexService.saveOrderIndex, 插件订单存储成功, orderId: {}", orderIndex.getId()))
                .doOnError(e -> log.error("PluginOrderIndexService.saveOrderIndex, 插件订单存储失败, orderId: {}, error: {}", orderIndex.getId(), e.getMessage(), e));
    }

    private PluginOrderIndex convertToIndex(PluginCtripOrderDTO pluginOrderDTO) {
//        PluginCtripOrderDTO pluginOrderDTO = (PluginCtripOrderDTO) orderDTO;
        String platform = pluginOrderDTO.getPlatform();
        PluginPlatformEnum pluginPlatformEnum = PluginPlatformEnum.fromCode(platform);

        String orderStatusDesc = pluginOrderDTO.getOrderStatusDisplay();
        PluginOrderStatusEnum pluginOrderStatusEnum = PluginOrderStatusEnum.fromExternalPlatformOrderStatus(pluginPlatformEnum, orderStatusDesc);
        Instant now = Instant.now();
        PluginOrderIndex.OutCallInfo outCallInfo = new PluginOrderIndex.OutCallInfo();
        outCallInfo.setCurrentStatus(PluginOutCallStatusEnum.NO_OUT_CALL.getCode());
        outCallInfo.setOutCallCount(0);
        Set<PluginOrderStatusEnum> defaultAlreadyMoveOrderStatusSet = CollUtil.newHashSet(
                PluginOrderStatusEnum.ALREADY_CHECK_IN, PluginOrderStatusEnum.CANCEL,
                PluginOrderStatusEnum.ALREADY_CHECK_OUT
        );
        return PluginOrderIndex.builder()
                .id(pluginOrderDTO.getOrderID())
                .hotelCode(pluginOrderDTO.getHotelCode())
                .hotelName(pluginOrderDTO.getHotelName())
                .channel(pluginOrderDTO.getChannel())
                .platform(platform)
                .orderStatus(pluginOrderStatusEnum.getValue())
                .orderCreateDate(pluginOrderDTO.getOrderCreateDate())
                .orderNo(pluginOrderDTO.getOrderID())
                .changes(pluginOrderDTO.getChanges())
                .adultCount(pluginOrderDTO.getRoomLimitAdultCount())
                .childCount(pluginOrderDTO.getRoomLimitChildCount())
                .checkInDate(pluginOrderDTO.getArrival())
                .checkOutDate(pluginOrderDTO.getDeparture())
                .arrivalEarlyAndLatestTime(pluginOrderDTO.getArrivalEarlyAndLatestTime())
                .bedType(pluginOrderDTO.getBedType())
                .roomCount(pluginOrderDTO.getQuantity())
                .breakfast(pluginOrderDTO.getOrderRoomPrices() != null && !pluginOrderDTO.getOrderRoomPrices().isEmpty() ? pluginOrderDTO.getOrderRoomPrices().get(0).getBreakfast() : null)
                .roomName(pluginOrderDTO.getRoomName())
                .bookerName(pluginOrderDTO.getClientName())
                .contactNumber(extractVirtualNumbers(pluginOrderDTO.getCtripRemarks()))
                .discountTotalCost(pluginOrderDTO.getOrderRateInfoVM() != null ? pluginOrderDTO.getOrderRateInfoVM().getCostRateInfo().getDiscountTotal() : null)
                .totalCost(pluginOrderDTO.getOrderRateInfoVM() != null ? pluginOrderDTO.getOrderRateInfoVM().getCostRateInfo().getTotal() : null)
                .finalTotalCost(pluginOrderDTO.getOrderRateInfoVM() != null ? pluginOrderDTO.getOrderRateInfoVM().getCostRateInfo().getFinalTotal() : null)
                .discountTotalPrice(pluginOrderDTO.getOrderRateInfoVM() != null ? pluginOrderDTO.getOrderRateInfoVM().getPriceRateInfo().getDiscountTotal() : null)
                .totalPrice(pluginOrderDTO.getOrderRateInfoVM() != null ? pluginOrderDTO.getOrderRateInfoVM().getPriceRateInfo().getTotal() : null)
                .finalTotalPrice(pluginOrderDTO.getOrderRateInfoVM() != null ? pluginOrderDTO.getOrderRateInfoVM().getPriceRateInfo().getFinalTotal() : null)
                .paymentType(pluginOrderDTO.getPaymentType())
                .personCountDesc(pluginOrderDTO.getPersonCountDesc())
                .stayDays(pluginOrderDTO.getLiveDays())
                .roomNights(pluginOrderDTO.getRoomNights())
                .orderType(pluginOrderDTO.getOrderType())
                .isOvertimeModify(pluginOrderDTO.isOvertimeModify())
                .orderRemark(new PluginOrderIndex.OrderRemark(
                        pluginOrderDTO.getSpRemarksMulti(),
                        pluginOrderDTO.getBedRemarks(),
                        pluginOrderDTO.getCommonRemark()))
                .moveOrderStatus(defaultAlreadyMoveOrderStatusSet.contains(pluginOrderStatusEnum)
                        ? PluginOrderMoveStatusEnum.MOVED_ORDER.getValue()
                        : PluginOrderMoveStatusEnum.UNMOVED_ORDER.getValue()
                )
                .formDate(pluginOrderDTO.getFormDate())
                .sourceType(pluginOrderDTO.getSourceType())
                .receiveType(pluginOrderDTO.getReceiveType())
                .outCallInfo(outCallInfo)
                .updatedAt(now)
                .build();
    }

    public static String extractVirtualNumbers(String input) {
        // 定义正则表达式，匹配 "请拨打：" 和 "（" 或 "(" 之间的所有字符，兼容全角和半角冒号及左括号
        String regex = "请拨打[:：](.*?)\\s*[（(]";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        StringBuilder result = new StringBuilder();
        boolean firstMatch = true;
        // 遍历匹配结果
        while (matcher.find()) {
            if (!firstMatch) {
                result.append("，");
            }
            result.append(matcher.group(1));  // 添加匹配到的所有字符
            firstMatch = false;
        }
        return result.toString().replace("，", ",");
    }

    public static void main(String[] args) {
        String a = "<b>订单确认号：</b>DL-202507151394733653762027520<br>";
        System.out.println(extractVirtualNumbers(a));
    }

    public Mono<PageResult<PluginOrderVO>> pagePluginOrder(PluginOrderPageByTabQo pluginOrderQo) {
        String hotelCode = pluginOrderQo.getHotelCode();
        String platform = pluginOrderQo.getPlatform();
        PluginOrderTabEnum pluginOrderTabEnum = PluginOrderTabEnum.fromValue(pluginOrderQo.getTabType());
        PluginOrderQo searchQo = pluginOrderTabEnum.buildOrderQo(hotelCode, platform);
        searchQo.setPageSize(pluginOrderQo.getPageSize());
        searchQo.setCursor(pluginOrderQo.getCursor());
        searchQo.setTabEnum(pluginOrderTabEnum);
        return listPluginOrder(searchQo, this::buildPluginOrderVo)
                .map(pluginOrders -> PageResult
                        .create(0L, pluginOrders, 0, pluginOrderQo.getPageSize())
                );
    }

    public <T> Mono<PageResult<T>> pagePluginOrder(PluginOrderQo pluginOrderQo, Function<PluginOrderIndex, T> mapper) {
        int pageNumber = pluginOrderQo.getPageNumber();
        int skip = (pageNumber - 1) * pluginOrderQo.getPageSize();
        int limit = pluginOrderQo.getPageSize();
        // 记录数
        return countPluginOrder(pluginOrderQo)
                .flatMap(count -> {
                    if (count < 1 || count <= skip) {
                        return Mono.just(PageResult.create(count, null, pageNumber, limit));
                    }
                    return listPluginOrder(pluginOrderQo, mapper)
                            .map(pluginOrders ->
                                    PageResult.create(count, pluginOrders, pageNumber, limit)
                            );
                })
                ;
    }

    public <T> Mono<List<T>> listPluginOrder(PluginOrderQo pluginOrderQo, Function<PluginOrderIndex, T> mapper) {
        Query.Builder builder = new Query.Builder();
        buildBaseQueryCondition(builder, pluginOrderQo);
        Query query = builder.build();
        NativeQueryBuilder nativeQueryBuilder = NativeQuery.builder()
                .withQuery(query);
        Optional.ofNullable(pluginOrderQo.getSort())
                .ifPresent(nativeQueryBuilder::withSort);
        if (Objects.nonNull(pluginOrderQo.getTabEnum())) {
            PluginOrderTabEnum tabEnum = pluginOrderQo.getTabEnum();
            nativeQueryBuilder.withSearchAfter(tabEnum.buildSearchAfter(pluginOrderQo.getCursor()));
        }
        // 分页
        Optional.ofNullable(pluginOrderQo.getPageSize())
                .ifPresent(pageParam -> nativeQueryBuilder.withPageable(PageRequest.of(0, pluginOrderQo.getPageSize())));
        NativeQuery nativeQuery = nativeQueryBuilder.build();
        return reactiveElasticsearchOperations.search(nativeQuery, PluginOrderIndex.class)
                .map(SearchHit::getContent)
                .map(mapper)
                .collectList()
                .doOnError(throwable -> log.error("查询订单索引数据异常,param:{}", JacksonUtils.writeValueAsString(pluginOrderQo), throwable));
    }

    /**
     * 获取这条数据，在这个查询条件和 排序 下的索引位置
     */
    public Mono<Long> getIndexNumBySort(PluginOrderIndex pluginOrderIndex, PluginOrderQo pluginOrderQo) {
        BoolQuery.Builder boolQueryBuild = buildBoolQueryBuild(pluginOrderQo);
        LocalDate checkInDate = pluginOrderIndex.getCheckInDate();
        String format = checkInDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // asc -> lt  desc -> gt
        // check_in_date asc
        boolQueryBuild.should(
                should -> should.range(range -> range
                        .untyped(t -> t.field(PluginOrderField.CHECK_IN_DATE.getColumn()).lt(JsonData.of(format))))
        );
        TermQuery checkInDateTermQuery = TermQuery.of(t -> t.field(PluginOrderField.CHECK_IN_DATE.getColumn())
                .value(format));

        // move_order_status asc
        boolQueryBuild.should(
                should -> should
                        .bool(bool -> bool.must(must -> must.term(checkInDateTermQuery))
                                .must(must -> must.range(range -> range
                                        .untyped(t -> t.field(PluginOrderField.MOVE_ORDER_STATUS.getColumn())
                                                .lt(JsonData.of(pluginOrderIndex.getMoveOrderStatus()))
                                        )
                                ))
                        )
        );
        TermQuery moveOrderStatusTermQuery = TermQuery.of(t -> t.field(PluginOrderField.MOVE_ORDER_STATUS.getColumn())
                .value(pluginOrderIndex.getMoveOrderStatus()));

        ZonedDateTime zonedDateTime = pluginOrderIndex.getUpdatedAt().atZone(ZoneId.systemDefault());
        DateTimeFormatter formatter = DateTimeFormatter.ISO_INSTANT;
        String createAtFormat = formatter.format(zonedDateTime);

        // create_at desc
        boolQueryBuild.should(
                should -> should
                        .bool(bool -> bool.must(must -> must.term(checkInDateTermQuery))
                                .must(must -> must.term(moveOrderStatusTermQuery))
                                .must(must -> must.range(range -> range
                                        .untyped(t -> t.field(PluginOrderField.UPDATED_AT.getColumn())
                                                .gt(JsonData.of(createAtFormat))
                                        )
                                ))
                        )

        );
        TermQuery createAtTermQuery = TermQuery.of(t -> t.field(PluginOrderField.UPDATED_AT.getColumn())
                .value(createAtFormat));
        boolQueryBuild.should(should -> should
                .bool(bool -> bool.must(must -> must.term(checkInDateTermQuery))
                        .must(must -> must.term(moveOrderStatusTermQuery))
                        .must(must -> must.term(createAtTermQuery))
                        .must(must -> must.range(range -> range
                                .untyped(t -> t.field(PluginOrderField.ID.getColumn())
                                        .gt(JsonData.of(pluginOrderIndex.getId()))
                                )
                        ))
                )
        );


        BoolQuery boolQuery = boolQueryBuild.minimumShouldMatch("1").build();
        Query query = new Query.Builder().bool(boolQuery).build();
        NativeQuery build = NativeQuery.builder()
                .withQuery(query)
                .build();

        return reactiveElasticsearchOperations.count(build, PluginOrderIndex.class);
    }

    public Mono<Long> countPluginOrder(PluginOrderQo pluginOrderQo) {
        Query.Builder builder = new Query.Builder();
        buildBaseQueryCondition(builder, pluginOrderQo);
        Query query = builder.build();
        NativeQuery nativeQuery = NativeQuery.builder()
                .withQuery(query)
                .build();
        return reactiveElasticsearchOperations.count(nativeQuery, PluginOrderIndex.class)
                .doOnError(throwable -> log.error("查询订单数错误,param:{}", JacksonUtils.writeValueAsString(pluginOrderQo), throwable));
    }

    private <T extends PluginOrderQo> void buildBaseQueryCondition(Query.Builder queryBuilder, T params) {
        BoolQuery.Builder boolQueryBuilder = buildBoolQueryBuild(params);

        queryBuilder.bool(boolQueryBuilder.build());
    }

    private static <T extends PluginOrderQo> BoolQuery.@NotNull Builder buildBoolQueryBuild(T params) {
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        Optional.ofNullable(params.getId())
                .ifPresent(id -> boolQueryBuilder.filter(
                        Querys.term(PluginOrderField.ID.getColumn(), id)
                ));

        // 日期范围过滤
        Optional.ofNullable(params.getStartDateTime())
                .filter(start -> Objects.nonNull(params.getEndDateTime()))
                .ifPresent(start -> boolQueryBuilder.filter(
                        Querys.range(PluginOrderField.ORDER_CREATE_DATE.getColumn(), start, params.getEndDateTime())
                ));

        // 平台过滤
        Optional.ofNullable(params.getPlatform())
                .filter(StrUtil::isNotEmpty)
                .ifPresent(platform -> boolQueryBuilder.filter(
                        Querys.term(PluginOrderField.PLATFORM.getColumn(), platform)
                ));

        // 酒店编码过滤
        Optional.ofNullable(params.getHotelCode())
                .filter(StrUtil::isNotBlank)
                .ifPresent(code -> boolQueryBuilder.filter(
                        Querys.term(PluginOrderField.HOTEL_CODE.getColumn(), code)
                ));

        // 订单状态
        Optional.ofNullable(params.getOrderStatus())
                .ifPresent(orderStatusList -> boolQueryBuilder.filter(
                        Querys.terms(PluginOrderField.ORDER_STATUS.getColumn(), new ArrayList<>(orderStatusList))
                ));

        // 搬单状态
        Optional.ofNullable(params.getMoveOrderStatusList())
                .filter(CollUtil::isNotEmpty)
                .ifPresent(statusList -> boolQueryBuilder.filter(
                        Querys.terms(PluginOrderField.MOVE_ORDER_STATUS.getColumn(), statusList)
                ));

        // 更新情况
        Optional.ofNullable(params.getUpdateFlag())
                .ifPresent(flag -> boolQueryBuilder.filter(
                        Querys.term(PluginOrderField.IS_OVERTIME_MODIFY.getColumn(),
                                Objects.equals(flag, BooleanEnum.YES.getCode())
                                        ? Boolean.TRUE.toString() : Boolean.FALSE.toString()
                        )
                ));

        Optional.ofNullable(params.getCheckInDate())
                .ifPresent(checkInDate -> boolQueryBuilder.filter(
                        Querys.term(PluginOrderField.CHECK_IN_DATE.getColumn(),
                                checkInDate.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)))
                ));

        Optional.ofNullable(params.getOutCallStatusList())
                .ifPresent(outCallStatus -> boolQueryBuilder.filter(
                        e -> e.nested(
                                nested -> nested.path(PluginOrderField.OUT_CALL_INFO.getColumn())
                                        .query(Querys.terms(StrUtil.join(StrUtil.DOT, PluginOrderField.OUT_CALL_INFO.getColumn(), "current_status"), outCallStatus))
                        )
                ));

        Optional.ofNullable(params.getOrderNoList())
                .ifPresent(orderNoList -> boolQueryBuilder.filter(
                        Querys.terms(PluginOrderField.ORDER_NO.getColumn(), new ArrayList<>(orderNoList))
                ));
        Optional.ofNullable(params.getMaxCallOutCount())
                .ifPresent(minCallOutCount -> boolQueryBuilder.filter(
                        e -> e.nested(nested -> nested.path(PluginOrderField.OUT_CALL_INFO.getColumn())
                                .query(Querys.range(StrUtil.join(StrUtil.DOT, PluginOrderField.OUT_CALL_INFO.getColumn(), "out_call_count"), params.getMinCallOutCount(),params.getMaxCallOutCount()))
                        )
                ));

        return boolQueryBuilder;
    }

    private <R> Mono<R> aggregation(Consumer<Query.Builder> queryConsumer, Map<String, Aggregation> aggregationsMap, Function<AggregationsContainer<?>, R> resultMapper) {


        Query.Builder builder = new Query.Builder();
        Optional.ofNullable(queryConsumer)
                .ifPresent(c -> c.accept(builder));

        // 构建查询
        NativeQueryBuilder queryBuilder = NativeQuery.builder()
                .withQuery(builder.build())
                .withMaxResults(0); // size: 100

        // 添加所有聚合
        aggregationsMap.forEach(queryBuilder::withAggregation);


        NativeQuery nativeQuery = queryBuilder.build();
        // 执行搜索
        return reactiveElasticsearchOperations.searchForHits(
                        nativeQuery,
                        PluginOrderIndex.class)
                .map(searchHits -> {
                    AggregationsContainer<?> aggregations = searchHits.getAggregations();
                    return resultMapper.apply(aggregations);
                });
    }

    public Mono<PluginOrderStatsDto> getOrderStatsByPlatform(PluginOrderQo params) {
        Map<String, Aggregation> aggregationMap = createAggregations();
        return aggregation(builder -> buildBaseQueryCondition(builder, params), aggregationMap, this::toOrderStatsDto);
    }

    public Mono<LatestOrderDTO> fetchLatestOrderStatsBySourceType(PluginOrderQo params) {
        // 核心的聚合查询逻辑不变
        Map<String, Aggregation> aggregationMap = createLatestOrderAggs();
        return aggregation(builder -> buildBaseQueryCondition(builder, params), aggregationMap, this::parseLatestOrderAgg);
    }

    public Mono<LatestOrderDTO> getLatestOrderBySourceType(PluginOrderQo params) {
        Map<String, Aggregation> aggregationMap = createLatestOrderAggs();

        return aggregation(builder -> buildBaseQueryCondition(builder, params), aggregationMap, this::parseLatestOrderAgg);
    }

    private Map<String, Aggregation> createLatestOrderAggs() {
        Map<String, Aggregation> aggregationMap = new HashMap<>();

        aggregationMap.put("source_type_groups",
                Aggregation.of(a -> a
                        .terms(t -> t
                                .field("source_type")
                                .size(2)
                        )
                        .aggregations("latest_order",
                                Aggregation.of(subAgg -> subAgg
                                        .topHits(th -> th
                                                .size(1)
                                                .sort(s -> s
                                                        .field(f -> f
                                                                .field("form_date")
                                                                .order(SortOrder.Desc)
                                                        )
                                                )
                                        )
                                )
                        )
                )
        );

        return aggregationMap;
    }

    private LatestOrderDTO parseLatestOrderAgg(AggregationsContainer<?> aggregationsContainer) {
        LatestOrderDTO dto = new LatestOrderDTO(); // 你可以自定义这个 DTO
        Optional.ofNullable(aggregationsContainer)
                .map(e -> (ElasticsearchAggregations) e)
                .map(e -> e.get("source_type_groups"))
                .map(e -> e.aggregation().getAggregate())
                .map(aggregation -> aggregation.sterms().buckets().array())
                .ifPresentOrElse(
                        buckets -> parseLatestOrderBuckets(buckets, dto),
                        () -> log.warn("No buckets found in source_type_groups aggregation")
                );
        return dto;
    }

    private void parseLatestOrderBuckets(List<StringTermsBucket> buckets, LatestOrderDTO dto) {
        List<LatestOrderItem> itemList = new ArrayList<>();

        for (StringTermsBucket bucket : buckets) {
            String sourceType = (String) bucket.key()._get();
            Map<String, Aggregate> subAggregations = bucket.aggregations();

            Aggregate topHitsAgg = subAggregations.get("latest_order");
            if (topHitsAgg == null || topHitsAgg.topHits() == null) {
                continue;
            }

            String isoDateStr = topHitsAgg.topHits().hits().hits().get(0).source().toJson().asJsonObject().get("form_date").toString();
            String isoDate = isoDateStr.replaceAll("^\"|\"$", ""); // 去掉开头和结尾的引号

            Instant instant = Instant.parse(isoDate);


            LatestOrderItem item = new LatestOrderItem();
            item.setSourceType(sourceType);
//            item.setFormDate(Date.from(instant));
            item.setFormDate(LocalDateTime.ofInstant(instant, ZoneId.systemDefault()));
            itemList.add(item);

        }

        dto.setItems(itemList);
    }


    // 构造聚合逻辑
    private Map<String, Aggregation> createAggregations() {
        Map<String, Aggregation> aggregationMap = new HashMap<>();
        aggregationMap.put("platform_stats",
                Aggregation.of(a -> a.terms(t -> t.field("platform").size(50))
                        .aggregations("new_orders", Aggregation.of(agg -> agg.valueCount(vc -> vc.field("id"))))
                        .aggregations("total_revenue", Aggregation.of(agg -> agg.sum(s -> s.field("final_total_cost"))))
                        .aggregations("total_orders", Aggregation.of(agg -> agg.valueCount(vc -> vc.field("id"))))
                        .aggregations("cancelled_orders", Aggregation.of(agg ->
                                agg.filter(f -> f.term(t -> t.field("order_status").value("400"))))
                        )
                )
        );
        return aggregationMap;
    }

    private PluginOrderStatsDto toOrderStatsDto(AggregationsContainer<?> aggregationsContainer) {
        PluginOrderStatsDto dto = new PluginOrderStatsDto();
        // 解析 "platform_stats" 聚合
        Optional.ofNullable(aggregationsContainer)
                .map(e -> (ElasticsearchAggregations) e)
                .map(e -> e.get("platform_stats"))
                .map(e -> e.aggregation().getAggregate())
                .map(aggregation -> aggregation.sterms().buckets().array())
                .ifPresentOrElse(
                        buckets -> parseAggregationBuckets(buckets, dto),
                        () -> setEmptyStats(dto)
                );
        return dto;
    }

    private void parseAggregationBuckets(List<StringTermsBucket> buckets, PluginOrderStatsDto dto) {
        Long totalNewOrders = null;
        BigDecimal totalRevenue = null;
        Long totalOrders = null;
        Long cancelledOrders = null;
        List<PlatformStatsItem> platformStatsList = new ArrayList<>();

        for (StringTermsBucket bucket : buckets) {
            String platform = (String) bucket.key()._get();
            ;
            Map<String, Aggregate> subAggregations = bucket.aggregations();

            Double newOrdersVal = getValueCountAggregationValue(subAggregations.get("new_orders"));
            Long newOrders = newOrdersVal != null ? newOrdersVal.longValue() : null;

            Double revenueVal = getSumAggregationValue(subAggregations.get("total_revenue"));
            BigDecimal revenue = revenueVal != null ? BigDecimal.valueOf(revenueVal) : null;

            Double ordersVal = getValueCountAggregationValue(subAggregations.get("total_orders"));
            Long orders = ordersVal != null ? ordersVal.longValue() : null;
            Long cancelled = getFilterAggregationValue(subAggregations.get("cancelled_orders"));

            // 累加时保持 null 的语义
            totalNewOrders = sumNullableLong(totalNewOrders, newOrders);
            totalRevenue = sumNullableBigDecimal(totalRevenue, revenue);
            totalOrders = sumNullableLong(totalOrders, orders);
            cancelledOrders = sumNullableLong(cancelledOrders, cancelled);

            // 构造每个平台的统计项
            PlatformStatsItem item = new PlatformStatsItem();
            item.setPlatform(platform);
            item.setNewOrders(newOrders);
            item.setTotalRevenue(revenue);
            item.setTotalOrders(orders);
            item.setCancelledOrders(cancelled);
            platformStatsList.add(item);
        }

        // 设置总计数据到 DTO
        dto.setTotalNewOrders(totalNewOrders);
        dto.setTotalRevenue(totalRevenue);
        dto.setTotalOrders(totalOrders);
        dto.setCancelledOrders(cancelledOrders);
        dto.setPlatformStats(platformStatsList);
    }

    private Long sumNullableLong(Long a, Long b) {
        if (a == null) return b;
        if (b == null) return a;
        return a + b;
    }

    private BigDecimal sumNullableBigDecimal(BigDecimal a, BigDecimal b) {
        if (a == null) return b;
        if (b == null) return a;
        return a.add(b);
    }


    private Double getSumAggregationValue(Aggregate aggregate) {
        try {
            return aggregate.sum().value();
        } catch (Exception e) {
            log.error("getValueCountAggregationValue error: {}", e.getMessage(), e);
            return null;
        }
    }

    private Double getValueCountAggregationValue(Aggregate aggregate) {
        try {
            return aggregate.valueCount().value();
        } catch (Exception e) {
            log.error("getValueCountAggregationValue error: {}", e.getMessage(), e);
            return null;
        }

    }

    private Long getFilterAggregationValue(Aggregate aggregate) {
        try {
            return aggregate.filter().docCount();
        } catch (Exception e) {
            log.error("getValueCountAggregationValue error: {}", e.getMessage(), e);
            return null;
        }
    }

    private void setEmptyStats(PluginOrderStatsDto dto) {
        dto.setTotalNewOrders(null);
        dto.setTotalRevenue(null);
        dto.setTotalOrders(null);
        dto.setCancelledOrders(null);
        dto.setPlatformStats(new ArrayList<>());
    }


    private List<PluginOrderVO> toVO(List<PluginOrderIndex> pluginOrderIndices) {
        return pluginOrderIndices.stream()
                .map(this::buildPluginOrderVo
                ).toList();
    }

    private PluginOrderVO buildPluginOrderVo(PluginOrderIndex orderIndex) {
        return PluginOrderVO.builder()
                .id(orderIndex.getId())
                .hotelCode(orderIndex.getHotelCode())
                .hotelName(orderIndex.getHotelName())
                .channel(orderIndex.getChannel())
                .platform(orderIndex.getPlatform())
                .orderStatus(orderIndex.getOrderStatus())
                .orderStatusDesc(
                        Optional.ofNullable(PluginOrderStatusEnum.fromValue(orderIndex.getOrderStatus()))
                                .map(PluginOrderStatusEnum::getDesc)
                                .orElse(StrUtil.EMPTY)
                )
                .isOvertimeModify(orderIndex.isOvertimeModify())
                .moveOrderStatus(orderIndex.getMoveOrderStatus())
                .moveOrderStatusDesc(
                        Optional.ofNullable(orderIndex.getMoveOrderStatus())
                                .map(PluginOrderMoveStatusEnum::fromValue)
                                .map(PluginOrderMoveStatusEnum::getDesc)
                                .orElse(StrUtil.EMPTY)
                )
                .orderNo(orderIndex.getOrderNo())
                .orderCreateDate(orderIndex.getOrderCreateDate())
                .bookerName(orderIndex.getBookerName())
                .contactNumber(orderIndex.getContactNumber())
                .checkInDate(orderIndex.getCheckInDate())
                .checkOutDate(orderIndex.getCheckOutDate())
                .stayDays(orderIndex.getStayDays())
                .guestCount((Objects.nonNull(orderIndex.getAdultCount()) ? orderIndex.getAdultCount() : 0) + (Objects.nonNull(orderIndex.getChildCount()) ? orderIndex.getChildCount() : 0))
                .adultCount(orderIndex.getAdultCount())
                .childCount(orderIndex.getChildCount())
                .personCountDesc(orderIndex.getPersonCountDesc())
                .roomName(orderIndex.getRoomName())
                .roomCount(orderIndex.getRoomCount())
                .roomNights(orderIndex.getRoomNights())
                .breakfast(orderIndex.getBreakfast())
                .bedType(orderIndex.getBedType())
                .paymentType(orderIndex.getPaymentType())
                .arrivalEarlyAndLatestTime(orderIndex.getArrivalEarlyAndLatestTime())
                .totalCost(orderIndex.getTotalCost())
                .discountTotalCost(orderIndex.getDiscountTotalCost())
                .finalTotalCost(orderIndex.getFinalTotalCost())
                .totalPrice(orderIndex.getTotalPrice())
                .discountTotalPrice(orderIndex.getDiscountTotalPrice())
                .finalTotalPrice(orderIndex.getFinalTotalPrice())
                .spRemarksMulti(orderIndex.getOrderRemark() != null ? orderIndex.getOrderRemark().getSpRemarksMulti() : null)
                .commonRemark(orderIndex.getOrderRemark() != null ? orderIndex.getOrderRemark().getCommonRemark() : null)
                .bedRemarks(orderIndex.getOrderRemark() != null ? orderIndex.getOrderRemark().getBedRemarks() : null)
                .changes(orderIndex.getChanges()) // 假设 changes 是一个 List<OrderChange>
                .updatedAt(orderIndex.getUpdatedAt())
                // 其他字段...
                .build();
    }

    public Mono<List<PluginOrderTabVo>> listOrderTab(PluginOrderTabListQo qo) {
        String hotelCode = qo.getHotelCode();
        Preconditions.checkNotNull(hotelCode, "酒店code不能为空");

        String platform = qo.getPlatform();

        List<Mono<Long>> tabCountMonoList = new ArrayList<>();

        List<PluginOrderTabEnum> tabEnums = PluginOrderTabEnum.listTabEnum();
        tabEnums
                .forEach(tab -> {
                    Mono<Long> countMono;
                    if (tab.isTipsDisplay()) {
                        PluginOrderQo searchQo = tab.buildOrderQo(hotelCode, platform);
                        countMono = countPluginOrder(searchQo);
                    } else {
                        countMono = Mono.just(0L);
                    }
                    tabCountMonoList.add(countMono);
                });

        return Mono.zip(tabCountMonoList, tabCountList -> {
            List<PluginOrderTabVo> tabVoList = new ArrayList<>();

            for (int i = 0; i < tabEnums.size(); i++) {
                PluginOrderTabEnum tabEnum = tabEnums.get(i);
                Long count = (Long) tabCountList[i];
                PluginOrderTabVo tabVo = new PluginOrderTabVo(tabEnum.getValue(), tabEnum.getName(), count, tabEnum.isTipsDisplay());
                tabVoList.add(tabVo);
            }
            return tabVoList;
        });

    }

    public Mono<PluginOrderUpdateMoveStatusVo> moveOrderNotDeal(PluginOrderDealQo qo) {
        PluginOrderTabEnum pluginOrderTabEnum = PluginOrderTabEnum.fromValue(qo.getTabType());
        return updateMoveOrderStatus(pluginOrderTabEnum, qo.getId(), PluginOrderMoveStatusEnum.NOT_DEAL);
    }

    public Mono<PluginOrderUpdateMoveStatusVo> dealMoveOrder(PluginOrderDealQo qo) {
        PluginOrderTabEnum pluginOrderTabEnum = PluginOrderTabEnum.fromValue(qo.getTabType());
        return updateMoveOrderStatus(pluginOrderTabEnum, qo.getId(), PluginOrderMoveStatusEnum.MOVED_ORDER);
    }


    private Mono<PluginOrderUpdateMoveStatusVo> updateMoveOrderStatus(PluginOrderTabEnum tabEnum, Long id, PluginOrderMoveStatusEnum moveStatusEnum) {

        // 更新
        return updateMoveOrderStatus(id, moveStatusEnum)
                .flatMap(e -> {
                    PluginOrderQo pluginOrderQo = tabEnum.buildOrderQo(e.getHotelCode(), e.getPlatform());
                    pluginOrderQo.setTabEnum(tabEnum);
                    pluginOrderQo.setId(e.getId());
                    return listPluginOrder(pluginOrderQo, Function.identity())
                            .flatMap(orders -> {
                                pluginOrderQo.setId(null);
                                return CollUtil.isEmpty(orders)
                                        ? Mono.just(-1L) : getIndexNumBySort(e, pluginOrderQo);
                            })
                            .map(indexPosition -> new PluginOrderUpdateMoveStatusVo(indexPosition, buildPluginOrderVo(e)));
                })
                .doOnError(error -> log.error("更新move_order_status失败", error));

    }

    private Mono<PluginOrderIndex> updateMoveOrderStatus(Long id, PluginOrderMoveStatusEnum moveStatusEnum) {
        Criteria criteria = Criteria.where("id").is(id);
        CriteriaQuery criteriaQuery = CriteriaQuery.builder(criteria).build();

        return reactiveElasticsearchOperations.search(criteriaQuery, PluginOrderIndex.class)
                .next()
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "数据不存在")))
                .flatMap(searchHit -> {
                    String indexName = searchHit.getIndex();
                    IndexCoordinates indexCoordinates = IndexCoordinates.of(indexName);
                    Document document = Document.create();
                    document.put(PluginOrderField.MOVE_ORDER_STATUS.getColumn(), moveStatusEnum.getValue());
                    UpdateQuery updateQuery = UpdateQuery.builder(id.toString())
                            .withDocument(document)
                            .build();
                    return reactiveElasticsearchOperations.update(updateQuery, indexCoordinates)
                            .map(this::isUpdateSuccessful)
                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "数据不存在")))
                            .filter(Boolean::booleanValue)
                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新失败")))
                            .flatMap(success -> refreshIndex(indexCoordinates))
                            .thenReturn(searchHit.getContent())
                            ;
                })
                .doOnError(error -> log.error("更新move_order_status失败", error))
                ;
    }


    private boolean isUpdateSuccessful(UpdateResponse response) {
        return UpdateResponse.Result.UPDATED.equals(response.getResult()) ||
                UpdateResponse.Result.NOOP.equals(response.getResult());
    }

    private Mono<Void> refreshIndex(IndexCoordinates indexCoordinates) {
        return reactiveElasticsearchOperations.indexOps(indexCoordinates)
                .refresh()
                .doOnError(error -> log.error("refresh index error"));
    }

    public Mono<Void> refreshIndex(){
        IndexCoordinates indexCoordinates = IndexCoordinates.of("plugin-order");
        return reactiveElasticsearchOperations.indexOps(indexCoordinates)
                .refresh()
                .doOnError(error -> log.error("refresh index error"));
    }

//    public Mono<PluginOrderTimeVO> getFromDate(PluginOrderFormDateDTO formDateDTO) {
//        // Validate input
//        if (formDateDTO == null || StrUtil.isBlank(formDateDTO.getHotelCode()) || StrUtil.isBlank(formDateDTO.getPlatform())) {
//            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "酒店编码和平台不能为空"));
//        }
//
//        return pluginOrderRecordService.getRecord(formDateDTO)
//                .flatMap(recordMap -> {
//                    if (recordMap.isEmpty()) {
//                        return Mono.empty();
//                    }
//                    PluginOrderQo params = new PluginOrderQo();
//                    params.setHotelCode(formDateDTO.getHotelCode());
//                    params.setPlatform(formDateDTO.getPlatform());
//
//                    // Fetch the latest order statistics
//                    return this.getLatestOrderBySourceType(params)
//                            .map(statsDto -> {
//                                // Filter items based on existence in the recordMap
//                                List<PluginOrderTimeVO.FormDateItem> filteredItems = statsDto.getItems().stream()
//                                        .filter(item -> recordMap.keySet().contains(item.getSourceType()))
//                                        .map(item -> new PluginOrderTimeVO.FormDateItem(item.getSourceType(), item.getFormDate()))
//                                        .toList();
//
//                                // Construct the result object
//                                PluginOrderTimeVO timeVO = new PluginOrderTimeVO();
//                                timeVO.setHotelCode(formDateDTO.getHotelCode());
//                                timeVO.setFormDateList(filteredItems);
//                                return timeVO;
//                            });
//                })
//                .switchIfEmpty(Mono.empty());
//    }

    // 在 Controller.java 中
    public Mono<PluginOrderIndex> orderSync(PluginOrderJsonDTO pluginOrderJsonDTO) {
        if (pluginOrderJsonDTO == null || pluginOrderJsonDTO.getOrder() == null) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "订单数据不能为空"));
        }
        ObjectMapper objectMapper = new ObjectMapper();
        PluginCtripOrderDTO pluginOrderDTO = null;
        try {
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            objectMapper.registerModule(new JavaTimeModule());
            objectMapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
            objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

            pluginOrderDTO = objectMapper.treeToValue(pluginOrderJsonDTO.getOrder(), PluginCtripOrderDTO.class);
        } catch (JsonProcessingException e) {
            return Mono.error(new RuntimeException("处理订单 JSON 失败", e));
        }
        pluginOrderDTO.setPlatform(pluginOrderJsonDTO.getPlatform());
        pluginOrderDTO.setChannel(pluginOrderJsonDTO.getChannel());
        pluginOrderDTO.setHotelCode(pluginOrderJsonDTO.getHotelCode());
        pluginOrderDTO.setHotelName(pluginOrderJsonDTO.getHotelName());
        PluginOrderIndex newOrderIndex = convertToIndex(pluginOrderDTO);
        return reactiveElasticsearchTemplate.get(pluginOrderDTO.getOrderID(), PluginOrderIndex.class)
                .flatMap(existingOrder -> {

                    boolean hasChanges = haveChangesOccurred(existingOrder, newOrderIndex);
                    boolean newCancel = isNewCancel(existingOrder, newOrderIndex);
                    log.info("hasChanges:{} newCancel:{}", hasChanges, newCancel);

                    if (hasChanges || newCancel) {
                        log.info("订单 {} 內容有变更，更新搬单状态。", newOrderIndex.getId());
                        newOrderIndex.setMoveOrderStatus(PluginOrderMoveStatusEnum.UNMOVED_ORDER.getValue());
                    }
                    // 將儲存操作串接到 (可能的) 更新操作之後執行。
                    return this.saveOrderIndex(newOrderIndex)
                            .flatMap(e -> saveCollectionRecord(pluginOrderJsonDTO).thenReturn(e));
                })
                .switchIfEmpty(Mono.defer(() -> this.saveOrderIndex(newOrderIndex)
                        .flatMap(e -> saveCollectionRecord(pluginOrderJsonDTO).thenReturn(e))
                )
                .doOnError(error -> log.error("订单同步失败", error)));

    }

    public Mono<Boolean> saveCollectionRecord(PluginOrderJsonDTO pluginOrderJsonDTO) {
        if (Objects.isNull(pluginOrderJsonDTO.getOperatorInfo())) {
            return Mono.just(false);
        }
        return pluginCollectionDetailsService.save(PluginCollectionDetails
                .build(pluginOrderJsonDTO.getOperatorInfo().getHotelCode(), pluginOrderJsonDTO.getOperatorInfo().getHotelName(), PluginCollectType.HOTEL_ORDERS_COLLECTED.getCode(), 1));
    }

    private boolean isNewCancel(PluginOrderIndex existingOrder, PluginOrderIndex newOrderIndex) {
        return !Objects.equals(existingOrder.getOrderStatus(), PluginOrderStatusEnum.CANCEL.getValue())
                && Objects.equals(newOrderIndex.getOrderStatus(), PluginOrderStatusEnum.CANCEL.getValue())
                ;
    }

    /**
     * 检查订单是否有变更。
     */
    private boolean haveChangesOccurred(PluginOrderIndex existingOrder, PluginOrderIndex newOrderIndex) {
        // 1. 安全地获取列表，如果 getChanges() 返回 null，则视为空列表。
        List<PluginOrderIndex.OrderChange> dbChanges = existingOrder.getChanges() == null
                ? Collections.emptyList()
                : existingOrder.getChanges();

        List<PluginOrderIndex.OrderChange> inputChanges = newOrderIndex.getChanges() == null
                ? Collections.emptyList()
                : newOrderIndex.getChanges();

        // 2. 比较列表大小。现在 dbChanges 和 inputChanges 都不为 null，可以安全调用 .size()。
        if (dbChanges.size() != inputChanges.size()) {
            return true;
        }

        // 3. 如果两个列表都为空（可能原本就是空的，也可能原本是 null），则认为它们没有变化。
        if (dbChanges.isEmpty()) {
            return false;
        }

        // 4. 列表大小相同且不为空，再比较内容。此时可以安全地创建 HashSet。
        Set<PluginOrderIndex.OrderChange> dbSet = new HashSet<>(dbChanges);
        Set<PluginOrderIndex.OrderChange> inputSet = new HashSet<>(inputChanges);

        return !dbSet.equals(inputSet);
    }

    public Mono<PluginOrderIndex> getById(String id){
        return reactiveElasticsearchTemplate.get(id, PluginOrderIndex.class);
    }
}
