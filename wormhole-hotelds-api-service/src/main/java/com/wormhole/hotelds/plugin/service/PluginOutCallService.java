package com.wormhole.hotelds.plugin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Preconditions;
import com.wormhole.common.constant.BooleanEnum;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.web.dao.HdsHotelInfoDao;
import com.wormhole.hotelds.core.model.entity.HdsHotelInfoEntity;
import com.wormhole.hotelds.plugin.config.CommonProperties;
import com.wormhole.hotelds.plugin.config.PluginCommentProperties;
import com.wormhole.hotelds.plugin.manager.PluginOutCallManager;
import com.wormhole.hotelds.plugin.model.dto.PluginOutCallContext;
import com.wormhole.hotelds.plugin.model.dto.PluginOutCallDTO;
import com.wormhole.hotelds.plugin.model.dto.PluginOutCallExecuteCallBackMsgResult;
import com.wormhole.hotelds.plugin.model.dto.PluginOutCallRecordAnalyzeDTO;
import com.wormhole.hotelds.plugin.model.entity.PluginCollectionDetails;
import com.wormhole.hotelds.plugin.model.enums.PluginCollectType;
import com.wormhole.hotelds.plugin.model.enums.PluginOutCallTabEnum;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderQo;
import com.wormhole.hotelds.plugin.model.qo.PluginOutCallQo;
import com.wormhole.hotelds.plugin.model.vo.PluginOrderTabVo;
import com.wormhole.hotelds.plugin.model.vo.PluginOutCallRecordAnalyzeVO;
import com.wormhole.hotelds.plugin.model.vo.PluginOutCallVO;
import com.wormhole.outcall.core.dto.JobDetailDto;
import com.wormhole.outcall.core.enums.OutCallApiEnum;
import com.wormhole.task.model.constant.PluginOrderStatusEnum;
import com.wormhole.task.model.constant.PluginOutCallStatusEnum;
import com.wormhole.task.model.entity.filed.PluginOrderField;
import com.wormhole.task.model.entity.index.PluginOrderIndex;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.query.Order;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;

@Service
@Slf4j
public class PluginOutCallService {


    @Resource
    private PluginOrderIndexService pluginOrderIndexService;

    @Resource
    private PluginCommentProperties pluginCommentProperties;

    @Resource
    private PluginOutCallManager pluginOutCallManager;

    @Resource
    private CommonProperties commonProperties;

    @Resource
    private WorkflowExecutor workflowExecutor;

    @Resource
    private HdsHotelInfoDao hdsHotelInfoDao;
    @Resource
    private PluginCollectionDetailsService pluginCollectionDetailsService ;

    private final Map<PluginOutCallTabEnum, Function<String, PluginOrderQo>> conditionFunctionMap = Map.of(
            PluginOutCallTabEnum.WAIT_OUT_CALL, this::buildWaitOutCallCondition,
            PluginOutCallTabEnum.OUT_CALL_SUCCESS, this::buildOutCallSuccessCondition,
            PluginOutCallTabEnum.OUT_CALL_FAIL, this::buildOutCallFailCondition
    );

//    @Resource
//    private PluginOutCallRepository pluginOutCallRepository;

    public Mono<List<PluginOutCallVO>> list(PluginOutCallDTO qo) {
        Preconditions.checkArgument(StringUtils.isNotBlank(qo.getHotelCode()), "hotel must not be blank");
        Preconditions.checkArgument(Objects.nonNull(qo.getTabType()), "tab_type must not be blank");

        PluginOrderQo searchCondition = buildPluginOrderSearchCondition(qo.getTabType(), qo.getHotelCode());
        searchCondition.setPageNumber(1);
        searchCondition.setPageSize(10000);
        return pluginOrderIndexService.listPluginOrder(searchCondition, this::buildPluginOutCallVo);
    }

    private PluginOutCallVO buildPluginOutCallVo(PluginOrderIndex pluginOrderIndex) {
        PluginOutCallVO pluginOutCallVO = new PluginOutCallVO();
        pluginOutCallVO.setOrderNo(pluginOrderIndex.getOrderNo());
        pluginOutCallVO.setBookerName(pluginOrderIndex.getBookerName());
        pluginOutCallVO.setContactNumber(pluginOrderIndex.getContactNumber());

        pluginOutCallVO.setPlatform(pluginOrderIndex.getPlatform());
        pluginOutCallVO.setChannel(pluginOrderIndex.getChannel());
        PluginCommentProperties.OutCall outCall = pluginCommentProperties.getOutCall();
        String tips = outCall.isExcludeNumberPrefix(pluginOrderIndex.getContactNumber()) ?
                outCall.getTips() : StrUtil.EMPTY;
        pluginOutCallVO.setTips(tips);

        Optional<String> newRemarksOptional = Optional.ofNullable(pluginOrderIndex.getOutCallInfo())
                .map(PluginOrderIndex.OutCallInfo::getRemarks)
                .filter(e -> StrUtil.isNotBlank(e) && !"无".equals(e));

        String remarks = pluginOrderIndex.getOrderRemarkStr(StrUtil.COMMA);

        PluginOutCallVO.RemarksInfo remarksInfo = new PluginOutCallVO.RemarksInfo();
        remarksInfo.setRemarks(newRemarksOptional.orElse(remarks));
        remarksInfo.setUpdateFlag(newRemarksOptional.isEmpty() ? BooleanEnum.NO.getCode() : BooleanEnum.YES.getCode());
        pluginOutCallVO.setRemarksInfo(remarksInfo);

        Optional<String> newArrivalTimeOptional = Optional.ofNullable(pluginOrderIndex.getOutCallInfo())
                .map(PluginOrderIndex.OutCallInfo::getArrivalTime)
                .filter(e -> StrUtil.isNotBlank(e) && !"无".equals(e));
        String arrivalTime = pluginOrderIndex.getArrivalEarlyAndLatestTime();

        PluginOutCallVO.ArrivalTimeInfo arrivalTimeInfo = new PluginOutCallVO.ArrivalTimeInfo();
        arrivalTimeInfo.setArrivalTime(newArrivalTimeOptional.orElse(arrivalTime));
        arrivalTimeInfo.setUpdateFlag(newArrivalTimeOptional.isEmpty() ? BooleanEnum.NO.getCode() : BooleanEnum.YES.getCode());
        pluginOutCallVO.setArrivalTimeInfo(arrivalTimeInfo);

        List<PluginOutCallVO.OutCallRecord> outCallRecords = Optional.ofNullable(pluginOrderIndex.getOutCallInfo())
                .map(PluginOrderIndex.OutCallInfo::getOutCallRecords)
                .map(records -> records
                        .stream()
                        .map(this::toOutCallRecord)
                        .toList()
                )
                .orElse(Collections.emptyList());

        pluginOutCallVO.setOutCallRecordList(outCallRecords);

        Integer currentStatus = Optional.ofNullable(pluginOrderIndex.getOutCallInfo())
                .map(PluginOrderIndex.OutCallInfo::getCurrentStatus)
                .orElse(PluginOutCallStatusEnum.NO_OUT_CALL.getCode());
        pluginOutCallVO.setOutCallStatus(currentStatus);

        return pluginOutCallVO;
    }

    private PluginOutCallVO.OutCallRecord toOutCallRecord(PluginOrderIndex.OutCallRecord record) {
        DateTimeFormatter formatter = DateTimeFormatter
                .ofPattern(DatePattern.NORM_DATETIME_PATTERN)
                .withZone(ZoneId.systemDefault());

        PluginOutCallVO.OutCallRecord result = new PluginOutCallVO.OutCallRecord();
        result.setTime(formatter.format(record.getTime()));
        result.setOutCallStatus(record.getStatus());
        result.setOutCallStatusDesc(PluginOutCallStatusEnum.getByCode(record.getStatus()).getDesc());

        return result;
    }

    public Mono<List<PluginOrderTabVo>> listTab(PluginOutCallDTO qo) {
        Preconditions.checkArgument(StringUtils.isNotBlank(qo.getHotelCode()), "hotel must not be blank");

        List<Mono<Long>> countMonoList = new ArrayList<>();

        PluginOutCallTabEnum[] tabList = PluginOutCallTabEnum.values();
        for (PluginOutCallTabEnum value : tabList) {
            Function<String, PluginOrderQo> buildConditionFunction = conditionFunctionMap.get(value);
            PluginOrderQo condition = buildConditionFunction.apply(qo.getHotelCode());
            Mono<Long> countMono = pluginOrderIndexService.countPluginOrder(condition);
            countMonoList.add(countMono);
        }
        return Mono.zip(countMonoList, countList -> {
            List<PluginOrderTabVo> resultList = new ArrayList<>(tabList.length);

            for (int i = 0; i < tabList.length; i++) {

                PluginOutCallTabEnum tabEnum = tabList[i];
                Long count = (Long) countList[i];
                PluginOrderTabVo tabVo = new PluginOrderTabVo(tabEnum.getValue(), tabEnum.getName(), count, true);
                resultList.add(tabVo);
            }
            return resultList;
        });
    }


    /**
     * 待外呼
     */
    private PluginOrderQo buildWaitOutCallCondition(String hotelCode) {
        PluginOrderQo pluginOrderQo = new PluginOrderQo();
        pluginOrderQo.setHotelCode(hotelCode);
        pluginOrderQo.setCheckInDate(LocalDate.now());
        pluginOrderQo.setOutCallStatusList(
                CollUtil.toList(
                        PluginOutCallStatusEnum.NO_OUT_CALL.getCode(), PluginOutCallStatusEnum.OUT_CALLING.getCode()
                )
        );
        pluginOrderQo.setOrderStatus(CollUtil.toList(PluginOrderStatusEnum.ALREADY_CONFIRMED.getValue()));
        pluginOrderQo.setSort(Sort.by(Sort.Order.desc(PluginOrderField.UPDATED_AT.getColumn())));
        pluginOrderQo.setMaxCallOutCount(1);
        pluginOrderQo.setMinCallOutCount(0);
        return pluginOrderQo;
    }

    private PluginOrderQo buildOutCallSuccessCondition(String hotelCode) {
        PluginOrderQo pluginOrderQo = new PluginOrderQo();
        pluginOrderQo.setHotelCode(hotelCode);
        pluginOrderQo.setCheckInDate(LocalDate.now());
        pluginOrderQo.setOutCallStatusList(
                CollUtil.toList(
                        PluginOutCallStatusEnum.OUT_CALL_SUCCESS.getCode()
                )
        );
        String property = StrUtil.join(StrUtil.DOT, PluginOrderField.OUT_CALL_INFO.getColumn(), "update_flag");
        Order order = new Order(Sort.Direction.DESC, property)
                .withNested(Order.Nested.builder(PluginOrderField.OUT_CALL_INFO.getColumn()).build());

        pluginOrderQo.setSort(Sort.by(order));
        return pluginOrderQo;
    }

    private PluginOrderQo buildOutCallFailCondition(String hotelCode) {
        PluginOrderQo pluginOrderQo = new PluginOrderQo();
        pluginOrderQo.setHotelCode(hotelCode);
        pluginOrderQo.setCheckInDate(LocalDate.now());
        pluginOrderQo.setOutCallStatusList(
                CollUtil.toList(
                        PluginOutCallStatusEnum.OUT_CALL_FAIL.getCode(), PluginOutCallStatusEnum.OUT_CALLING.getCode()
                )
        );
        pluginOrderQo.setMaxCallOutCount(Integer.MAX_VALUE);
        pluginOrderQo.setMinCallOutCount(1);
        String property = StrUtil.join(StrUtil.DOT, PluginOrderField.OUT_CALL_INFO.getColumn(), "last_call_time");
        Order order = new Order(Sort.Direction.DESC, property)
                .withNested(Order.Nested.builder(PluginOrderField.OUT_CALL_INFO.getColumn()).build());

        pluginOrderQo.setSort(Sort.by(order));
        return pluginOrderQo;
    }

    /**
     * 外呼
     */
    public Mono<Boolean> call(PluginOutCallQo qo) {
        checkOutCallParam(qo);
        List<String> orderNoList = qo.getOrderNoList();

        // todo lock
        PluginOrderQo searchCondition = buildPluginOrderSearchCondition(qo.getTabType(), qo.getHotelCode());
        searchCondition.setOrderNoList(orderNoList);

        LocalDateTime date = LocalDateTime.now();
        ZoneId zoneId = ZoneId.systemDefault();
        Instant instant = date.atZone(zoneId).toInstant();
        return pluginOrderIndexService.listPluginOrder(searchCondition, Function.identity())
                .flatMap(orders -> {
                    List<PluginOutCallContext.OutCallItem> outCallItemList = orders
                            .stream()
                            .map(this::toOutCallItem)
                            .toList();
                    PluginOutCallContext pluginOutCallContext = new PluginOutCallContext();
                    pluginOutCallContext.setDate(date);
                    pluginOutCallContext.setOutCallItems(outCallItemList);

                    return pluginOutCallManager.outCall(pluginOutCallContext)
                            .flatMap(result -> {
                                List<Mono<PluginOrderIndex>> saveMonoList = orders
                                        .stream()
                                        .filter(e -> !result.isInvalid(e.getId()))
                                        .peek(order -> {
                                            boolean fail = result.isFail(order.getId());
                                            Integer currentStatus;
                                            PluginOrderIndex.OutCallInfo outCallInfo = order.getOutCallInfo();
                                            List<PluginOrderIndex.OutCallRecord> records = Optional.ofNullable(outCallInfo.getOutCallRecords()).orElse(new ArrayList<>());
                                            if (fail) {
                                                currentStatus = PluginOutCallStatusEnum.OUT_CALL_FAIL.getCode();
                                                records.add(0, new PluginOrderIndex.OutCallRecord(instant, PluginOutCallStatusEnum.OUT_CALL_FAIL.getCode()));
                                            } else {
                                                currentStatus = PluginOutCallStatusEnum.OUT_CALLING.getCode();
                                            }
                                            outCallInfo.setCurrentStatus(currentStatus);
                                            outCallInfo.setOutCallCount(outCallInfo.getOutCallCount() + 1);
                                            outCallInfo.setOutCallRecords(records);
                                        })
                                        .map(pluginOrderIndexService::saveOrderIndex)
                                        .toList();
                                return Mono.zip(saveMonoList, objects -> true)
                                        .then(pluginOrderIndexService.refreshIndex())
                                        .thenReturn(true);
                            });
                });
    }

    private PluginOutCallContext.OutCallItem toOutCallItem(PluginOrderIndex order) {
        PluginOutCallContext.OutCallItem outCallItem = new PluginOutCallContext.OutCallItem();
        outCallItem.setOrderId(order.getId());
        // 美团是#
        String contactNumber = order.getContactNumber()
                .replaceAll("#",",");
        outCallItem.setPhoneNumber(contactNumber);
        outCallItem.setName(order.getBookerName());

        String hotelName = Optional.ofNullable(order.getHotelName())
                .orElseGet(() -> Optional.ofNullable(hdsHotelInfoDao.queryByHotelCode(order.getHotelCode()))
                        .map(e -> e
                                .map(HdsHotelInfoEntity::getHotelName)
                                .block()
                        )
                        .orElse(StrUtil.EMPTY));
        outCallItem.setVariable(Map.of("酒店名称", hotelName,
                "预订人名称", order.getBookerName()
        ));
        return outCallItem;
    }

    /**
     * 批量外呼校验
     */
    private void checkOutCallParam(PluginOutCallQo qo) {
        String hotelCode = qo.getHotelCode();
        Preconditions.checkArgument(StringUtils.isNotBlank(hotelCode), "hotel must not be blank");

        List<String> orderNoList = qo.getOrderNoList();
        Preconditions.checkArgument(CollUtil.isNotEmpty(orderNoList), "order list must not be empty");
    }

    /**
     * 构建 订单查询的条件
     */
    private PluginOrderQo buildPluginOrderSearchCondition(Integer tabType, String hotelCode) {
        Optional<PluginOutCallTabEnum> tabOptional = Optional.ofNullable(tabType)
                .map(PluginOutCallTabEnum::fromValue)
                .orElseThrow(() -> new IllegalArgumentException("tabType is Illegal"));

        return tabOptional.map(conditionFunctionMap::get)
                .map(e -> e.apply(hotelCode))
                .orElseThrow(() -> new IllegalArgumentException("tabType is Illegal"));
    }

    public Mono<Map<String, Object>> handleOutCallCallback(OutCallApiEnum apiEnum, String json) {
        log.info("shu ke  callback json :{}", json);
        JobDetailDto jobDetailDto = pluginOutCallManager.convertCallbackData(apiEnum, json);
        handleOutCallCallback(jobDetailDto)
                .subscribeOn(Schedulers.boundedElastic()).subscribe();

        return Mono.just(Map.of("code", "10000"));
    }

    public Mono<Void> handleOutCallCallback(JobDetailDto jobDetailDto) {
        return pluginOutCallManager.executeOutCallMsg(jobDetailDto)
                .flatMap(result -> {
                    Boolean outCallSuccess = result.getOutCallSuccess();
                    if (outCallSuccess) {
                        return handleSuccessOutCall(jobDetailDto, result);
                    } else {
                        return handFailOutCall(jobDetailDto, result);
                    }
                })
                .then();
    }

    public Mono<Void> handFailOutCall(JobDetailDto jobDetailDto, PluginOutCallExecuteCallBackMsgResult result) {
        String orderId = result.getOrderId();
        return pluginOrderIndexService.getById(orderId)
                .flatMap(pluginOrderIndex -> {
                    PluginOrderIndex.OutCallInfo outCallInfo = pluginOrderIndex.getOutCallInfo();
                    outCallInfo.setCurrentStatus(PluginOutCallStatusEnum.OUT_CALL_FAIL.getCode());

                    List<PluginOrderIndex.OutCallRecord> records = Optional.ofNullable(outCallInfo.getOutCallRecords()).orElse(new ArrayList<>());
                    records.add(0, new PluginOrderIndex.OutCallRecord(jobDetailDto.getStartTime().toInstant(), PluginOutCallStatusEnum.OUT_CALL_FAIL.getCode()));
                    outCallInfo.setOutCallRecords(records);
                    return pluginOrderIndexService.saveOrderIndex(pluginOrderIndex);
                })
                .flatMap(
                        pluginOrderIndex -> pluginCollectionDetailsService
                                .save(PluginCollectionDetails.build(pluginOrderIndex.getHotelCode(), pluginOrderIndex.getHotelName(), PluginCollectType.HOTEL_OUT_CALL.getCode(), 1))
                )
                .then();
    }

    private Mono<Void> handleSuccessOutCall(JobDetailDto jobDetailDto, PluginOutCallExecuteCallBackMsgResult result) {
        return analyzeOutCallRecord(new PluginOutCallRecordAnalyzeDTO(JacksonUtils.writeValueAsString(result.getRecordDetailEntities())))
                .flatMap(analyzeVO -> {
                    String orderId = result.getOrderId();
                    String arrival = analyzeVO.getArrival();
                    String remark = analyzeVO.getRemark();
                    return pluginOrderIndexService.getById(orderId)
                            .flatMap(pluginOrderIndex -> {
                                PluginOrderIndex.OutCallInfo outCallInfo = pluginOrderIndex.getOutCallInfo();
                                outCallInfo.setCurrentStatus(PluginOutCallStatusEnum.OUT_CALL_SUCCESS.getCode());
                                outCallInfo.setArrivalTime(arrival);
                                outCallInfo.setRemarks(remark);
                                List<PluginOrderIndex.OutCallRecord> records = Optional.ofNullable(outCallInfo.getOutCallRecords()).orElse(new ArrayList<>());
                                records.add(0, new PluginOrderIndex.OutCallRecord(jobDetailDto.getStartTime().toInstant(), PluginOutCallStatusEnum.OUT_CALL_SUCCESS.getCode()));
                                outCallInfo.setOutCallRecords(records);
                                return pluginOrderIndexService.saveOrderIndex(pluginOrderIndex);
                            });
                })
                .flatMap(
                        pluginOrderIndex -> pluginCollectionDetailsService
                                .save(PluginCollectionDetails.build(pluginOrderIndex.getHotelCode(), pluginOrderIndex.getHotelName(), PluginCollectType.HOTEL_OUT_CALL.getCode(), 1)))
                .then();
    }


    public Mono<PluginOutCallRecordAnalyzeVO> analyzeOutCallRecord(PluginOutCallRecordAnalyzeDTO dto) {

        Map<String, Object> initialInput = Map.of(
                "USER_INPUT", dto.getContent());

        String workflowCode = commonProperties.getOutCallAnalyzeWorkFlowCode();
        return workflowExecutor.execute(workflowCode, initialInput)
                .flatMap(responseList -> responseList.stream()
                        .filter(node -> "end".equals(node.getType()))
                        .findFirst()
                        .map(node -> Optional.ofNullable(node.getOutput())
                                .map(output -> {
                                    @SuppressWarnings("unchecked")
                                    Map<String, Object> map = (Map<String, Object>) output;
                                    Map<String, String> res = (Map<String, String>) map.get("output");
                                    PluginOutCallRecordAnalyzeVO analyzeVO = new PluginOutCallRecordAnalyzeVO();
                                    analyzeVO.setArrival(res.get("arrival"));
                                    analyzeVO.setRemark(res.get("remark"));
                                    return Optional.ofNullable(analyzeVO)
                                            .orElse(new PluginOutCallRecordAnalyzeVO());
                                })
                                .orElse(new PluginOutCallRecordAnalyzeVO()))
                        .map(Mono::just)
                        .orElse(Mono.just(new PluginOutCallRecordAnalyzeVO())))
                .onErrorResume(e -> {
                    log.error("外呼调用工作流失败, error: {}", e.getMessage(), e);
                    return Mono.just(new PluginOutCallRecordAnalyzeVO());
                });
    }

}
