server:
  port: 8082

management:
  endpoints:
    web:
      exposure:
        include: health
      base-path: /actuator
  endpoint:
    health:
      probes:
        enabled: true
  metrics:
    tags:
      application: ${spring.application.name}
  tracing:
    enabled: true

spring:
  main:
    allow-circular-references: true
  application:
    name: wormhole-hotelds-api
  reactor:
    context-propagation: auto
  freemarker:
    check-template-location: false
  data:
    redis:
      host: ************
      database: 8
      password: 6iaEg*fXjl6Rzxe*
  elasticsearch:
    uris: http://***********:9200
    username: es-user
    password: Abcd234@#$

  r2dbc:
    url: r2dbc:mysql://***********:3306/wormhole?characterEncoding=UTF8&autoReconnect=true&allowMultiQueries=true&rewriteBatchedStatements=true&serverTimezone=GMT%2B8&zeroDateTimeBehavior=convertToNull&useSSL=false
    username: dev_rw
    password: z8E3SuIy.1MlCril8

wormhole:
  auth:
    api-key:
      excluded-paths:
        - "/actuator/**"
        - "/feishu/**"
        - "/chat/**"
        - "/actuator/**"
        - "/api/**"
      valid-api-keys:
        "sk-delonix-office"
  ai:
    azure:
      openai:
        ai-configs:
          - region: japan
            enabled: true
            default-config: true
            api-key: ********************************
            endpoint: https://dl-japan-prod.openai.azure.com
          - region: east-us
            enabled: true
            default-config: false
            api-key: ********************************
            endpoint: https://dl-openai-east-us.openai.azure.com
          - region: west-us
            enabled: true
            default-config: false
            api-key: ********************************
            endpoint: https://dl-openai-west-us.openai.azure.com
    aliyun:
      qwen:
        apikey: sk-********************************
        endpoint: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
        embedding-endpoint: https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding
    jina:
      access-key: jina_a9171dfe3aa74bfa9379040fa4a6c0f3gBz-clP52PTD0aebm1n0WQtEuzJm
      rerank-endpoint: https://api.jina.ai/v1/rerank
      embeddings-endpoint: https://api.jina.ai/v1/embeddings
      classify-endpoint: https://api.jina.ai/v1/classify
      reader-endpoint: https://r.jina.ai
      segment-endpoint: https://segment.jina.ai
      param:
        segment:
          return-tokens: true
    zilliz:
      endpoint: https://in05-7dbe69e2faa4607.serverless.ali-cn-hangzhou.cloud.zilliz.com.cn
      token: ea6b048d11a5d4c67bcf11c8c4e7000752da60ef74d7dc2dace66df1bad7e9952158873bc7e34305310cc8b91e69c4704a80eebc
    bge:
      url: http://bge-rerank.delonix.group/compare_sentences
    doubao:
      api-key: 6591bbc0-23ad-423e-a34e-1de58f9a80d6
      base-url: https://ark.cn-beijing.volces.com/api/v3/
    aws:
      access-key-id: ********************
      secret-access-key: GtsJEU3yq5TanRanl1FkjuX2pmjKAGrrT5CB6t+m
  buckets:
    common-bucket-name: bdw-test
  object-storage:
    configs:
      OSS:
        enabled: true
        buckets:
          - bucket-name: bdw-test
            accessKeyId: LTAI5tLjDNipNDcoyrHzVTkq
            accessKeySecret: ******************************
            public-endpoint: https://oss-cn-shenzhen.aliyuncs.com
            intranet-endpoint: https://oss-cn-shenzhen.aliyuncs.com
            cdn-domain: https://test.img.betterwood.com
            maxSize: 50
            policyExpire: 300
            region: cn-shenzhen
            sts-config:
              bucket-name: bdw-test
              access-key-id: LTAI5tLjDNipNDcoyrHzVTkq
              access-key-secret: ******************************
              region: cn-shenzhen
              roleArn: acs:ram::1908865586809974:role/test-oss-kms
              durationSeconds: 900
      COS:
        enabled: false
        buckets:
          - bucket-name: bdw-test-1326469441
            access-key-id: AKIDPz74vb9Wjsz5EHCBC4mZMA1nhU6c2LWY
            access-key-secret: 340ACLVjsskQzNet15zgAXounaDdZM1X
            region: ap-guangzhou
            cdn-domain: https://dev-img.betterwood.com
            public-endpoint: https://bdw-test-1326469441.cos.ap-guangzhou.myqcloud.com
            intranet-endpoint: https://bdw-test-1326469441.cos.ap-guangzhou.myqcloud.com
            enabled: false
            skipMd5Check: false

  xxl:
    job:
      addresses: https://dev-xxl-job-admin.delonix.work/xxl-job-admin
      port: 9999
      log-retention-days: 30
      log-path: /var/delonix/logs/local/jobs/

theme:
  defaultLogo: https://dev-img.betterwood.com/logo.png
  defaultHotelLogo: https://dev-img.betterwood.com/logo.png
  defaultBanner: https://dev-img.betterwood.com/favicon.ico


