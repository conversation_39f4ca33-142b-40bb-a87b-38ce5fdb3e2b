package com.wormhole.hotelds.plugin.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.ComponentScan;

/**
 * 插件模块测试配置
 * 专门用于插件相关的单元测试和集成测试
 * 
 * <AUTHOR>
 * @date 2025/8/7
 */
@TestConfiguration
@ComponentScan(basePackages = {
        "com.wormhole.hotelds.plugin.dao",
        "com.wormhole.hotelds.plugin.service",
        "com.wormhole.hotelds.plugin.controller"
})
public class PluginTestConfiguration {
}
