package com.wormhole.hotelds.plugin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.plugin.manager.PluginOutCallManager;
import com.wormhole.hotelds.plugin.model.dto.*;
import com.wormhole.hotelds.plugin.model.vo.GetPreReplyVO;
import com.wormhole.hotelds.plugin.model.vo.HotelAbbreviationResultVO;
import com.wormhole.task.model.entity.PluginCommentLabelEntity;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("local")
@SpringBootTest(classes = {Application.class})
class PluginCommonControllerTest {
    @Resource
    private PluginCommonController pluginCommonController;

    @Resource
    private PluginOutCallManager pluginOutCallManager;

    @Test
    public void test(){

    }

    @Test
    void findAll() {
        Result<List<PluginCommentLabelEntity>> block = pluginCommonController.findAll().block();
        System.out.println(JacksonUtils.writeValuePretty(block));
    }

    @Test
    void findLeafLabel() {
        Result<String> block = pluginCommonController.findLeafLabel().block();
        System.out.println(JacksonUtils.writeValuePretty(block));
    }


    @Test
    void saveEntity() {
        String json = "{\"label_detail\":[{\"label\":\"前台接待效率体验好\",\"sentiment\":\"正面\",\"mention\":\"前台嘉嘉服务不错\"},{\"label\":\"交通便利\",\"sentiment\":\"正面\",\"mention\":\"酒店位置好，交通便利\"}],\"main_focus\":\"酒店的前台服务和位置交通\",\"main_sentiment\":\"正面\",\"score\":0.8,\"channel\":\"ZhiXing\",\"comment_id\":\"22465\",\"hotel_code\":\"HC001\"}";
        SaveEntityDTO saveEntityDTO = new SaveEntityDTO();
        saveEntityDTO.setJson(json);
        saveEntityDTO.setDtoType("com.wormhole.hotelds.plugin.model.dto.CommentTagAnalysisDTO");
        Map<String, Object> queryFields = Map.of(
                "hotelCode", "HC001",
                "channel", "ZhiXing",
                "commentId", "22465"
        );
        saveEntityDTO.setQueryFields(JacksonUtils.writeValueAsString(queryFields));
        pluginCommonController.saveEntity(saveEntityDTO)
                .subscribe(result -> {
                    System.out.println(result);
                    assertNotNull(result);
                    assertTrue(result.isSuccess());
                });
    }

    @Test
    void saveIndex() {
        SaveIndexDTO saveIndexDTO = new SaveIndexDTO();
        saveIndexDTO.setCommentId("22465");
        pluginCommonController.saveIndex(saveIndexDTO).block();
    }

    @Test
    void savePre() {
        SavePreReplyDTO savePreReplyDTO = new SavePreReplyDTO();
        savePreReplyDTO.setContent("嘻嘻，感谢您的反馈，我们会继续努力提升服务质量。");
        savePreReplyDTO.setHotelCode("HC001");
        savePreReplyDTO.setReplyStyle("正面");
        savePreReplyDTO.setOnlyPositive(1);
        pluginCommonController.savePreReply(savePreReplyDTO).block();
    }

    @Test
    void getPre() {
        GetPreReplyDTO getPreReplyDTO = new GetPreReplyDTO();
        getPreReplyDTO.setHotelCode("HC0001");
        Result<GetPreReplyVO> block = pluginCommonController.getPreReply(getPreReplyDTO).block();
        System.out.println(JacksonUtils.writeValuePretty(block));
    }

    @Test
    void aa(){
        Result<String> block = pluginCommonController.hotelAbbreviation("Ctrip").block();
        System.out.println(JacksonUtils.writeValuePretty(block));
    }

    @Test
    void generateHotelAbbreviationName() {
        List<PluginHotelAbbreviationDTO> list = new ArrayList<>();

        PluginHotelAbbreviationDTO dto = new PluginHotelAbbreviationDTO();
        dto.setHotelName("北京万豪酒店");
        dto.setPlatform("Ctrip");
        dto.setChannel("Ctrip");
        dto.setExternalHotelId("123456");

        PluginHotelAbbreviationDTO dto1 = new PluginHotelAbbreviationDTO();
        dto1.setHotelName("万豪酒店深圳大学店");
        dto1.setPlatform("Ctrip");
        dto1.setChannel("Ctrip");
        dto1.setExternalHotelId("234567");

        list.add(dto);
        list.add(dto1);

        Result<List<HotelAbbreviationResultVO>> block1 = pluginCommonController.batchGenerateHotelAbbreviationName(list).block();
        System.out.println(JacksonUtils.writeValuePretty(block1));
    }
}