package com.wormhole.hotelds.plugin.dao;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.TestApplication;
import com.wormhole.hotelds.plugin.model.entity.PluginCollectionDetails;
import com.wormhole.hotelds.plugin.model.enums.PluginCollectType;
import com.wormhole.hotelds.plugin.model.qo.PluginCollectionDetailsQO;
import com.wormhole.hotelds.plugin.service.PluginCollectionSummaryService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.data.r2dbc.DataR2dbcTest;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import reactor.test.StepVerifier;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * PluginCollectionDetails 数据插入和查询测试
 *
 * <AUTHOR>
 * @date 2025/8/7
 */
@SpringBootTest(classes = TestApplication.class)
@ActiveProfiles("dev")
class PluginCollectionDetailsDataTest {

    @Autowired
    private PluginCollectionDetailsDao pluginCollectionDetailsDao;
    @Autowired
    private PluginCollectionSummaryService pluginCollectionSummaryService;

    /**
     * 测试批量插入采集详情数据
     */
    @Test
    void testInsertCollectionDetailsData() {
        // 准备测试数据
        List<PluginCollectionDetails> testData = createTestCollectionDetailsData();
        testData.forEach(detail -> {
            pluginCollectionDetailsDao.save(detail).subscribe();
        });
    }
    @Test
    void testSummaryData() {

        pluginCollectionSummaryService.executeHotelDataStatistics(LocalDate.of(2025, 8, 1), LocalDate.of(2025, 8, 10),null).subscribe();

    }



    /**
     * 创建测试用的采集详情数据
     */
    private List<PluginCollectionDetails> createTestCollectionDetailsData() {
        List<PluginCollectionDetails> dataList = new ArrayList<>();
        
        // 酒店1的数据
        dataList.addAll(createHotelData("HOTEL001", "希尔顿酒店", LocalDate.of(2025, 8, 1)));
        dataList.addAll(createHotelData("HOTEL001", "希尔顿酒店", LocalDate.of(2025, 8, 2)));
        dataList.addAll(createHotelData("HOTEL001", "希尔顿酒店", LocalDate.of(2025, 8, 3)));
        
        // 酒店2的数据
        dataList.addAll(createHotelData("HOTEL002", "万豪酒店", LocalDate.of(2025, 8, 1)));
        dataList.addAll(createHotelData("HOTEL002", "万豪酒店", LocalDate.of(2025, 8, 2)));
        
        // 酒店3的数据
        dataList.addAll(createHotelData("HOTEL003", "洲际酒店", LocalDate.of(2025, 8, 1)));
        
        System.out.println("创建了 " + dataList.size() + " 条测试数据");
        return dataList;
    }

    /**
     * 为特定酒店和日期创建采集数据
     */
    private List<PluginCollectionDetails> createHotelData(String hotelCode, String hotelName, LocalDate date) {
        List<PluginCollectionDetails> hotelData = new ArrayList<>();
        
        // 报告生成数据
        hotelData.add(createDetail(hotelCode, hotelName, 
                PluginCollectType.HOTEL_REPORT_GENERATED.getCode(), 
                2, date.atTime(9, 0)));
        
        // 评论采集数据
        hotelData.add(createDetail(hotelCode, hotelName, 
                PluginCollectType.HOTEL_REVIEWS_COLLECTED.getCode(), 
                15, date.atTime(10, 30)));
        
        hotelData.add(createDetail(hotelCode, hotelName, 
                PluginCollectType.HOTEL_REVIEWS_COLLECTED.getCode(), 
                8, date.atTime(15, 45)));
        
        // 订单采集数据
        hotelData.add(createDetail(hotelCode, hotelName, 
                PluginCollectType.HOTEL_ORDERS_COLLECTED.getCode(), 
                5, date.atTime(11, 15)));
        
        // 价格采集数据
        hotelData.add(createDetail(hotelCode, hotelName, 
                PluginCollectType.HOTEL_PRICES_COLLECTED.getCode(), 
                3, date.atTime(12, 0)));
        
        hotelData.add(createDetail(hotelCode, hotelName, 
                PluginCollectType.HOTEL_PRICES_COLLECTED.getCode(), 
                4, date.atTime(18, 30)));
        
        // 评分采集数据
        hotelData.add(createDetail(hotelCode, hotelName, 
                PluginCollectType.HOTEL_COMMENT_RATING_COLLECTED.getCode(),
                1, date.atTime(14, 20)));
        
        return hotelData;
    }

    /**
     * 创建单个采集详情记录
     */
    private PluginCollectionDetails createDetail(String hotelCode, String hotelName, 
                                                String collectType, Integer count, 
                                                LocalDateTime createdAt) {
        PluginCollectionDetails detail = new PluginCollectionDetails();
        detail.setHotelCode(hotelCode);
        detail.setHotelName(hotelName);
        detail.setCollectType(collectType);
        detail.setCollectCount(count);
        detail.setCreatedAt(createdAt);
        detail.setUpdatedAt(createdAt);
        detail.setRowStatus(1); // 有效状态
        detail.setCreatedBy("test_user");
        detail.setCreatedByName("测试用户");
        detail.setUpdatedBy("test_user");
        detail.setUpdatedByName("测试用户");
        
        return detail;
    }


}
