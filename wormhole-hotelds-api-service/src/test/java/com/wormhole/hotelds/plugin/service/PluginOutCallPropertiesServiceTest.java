package com.wormhole.hotelds.plugin.service;

import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.plugin.model.dto.PluginOutCallRecordAnalyzeDTO;
import com.wormhole.hotelds.plugin.model.vo.PluginOutCallRecordAnalyzeVO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@ActiveProfiles("local")
@SpringBootTest(classes = {Application.class})
class PluginOutCallPropertiesServiceTest {
    @Resource
    private PluginOutCallService pluginOutCallService;

    @Test
    void analyzeOutCallRecord() {
        PluginOutCallRecordAnalyzeDTO dto = new PluginOutCallRecordAnalyzeDTO();
        dto.setContent(" {             \"msg\": \"您好，请问是{周全}老师吗？【停顿两秒】我这边是{酒店名称}客服，想确认下您今天预计到店时间(PS:不可打断)(语境分类[A类])\",             \"user\": \"0\",             \"uniqueid\": \"6_8600234_911897cb4710a3f4f6c226b2df2a8d4d_211_1129330677\",             \"create_time\": \"2025-07-03 11:34:01\",             \"path\": \"\"         },         {             \"msg\": \"喂，你好。\",             \"user\": \"1\",             \"uniqueid\": \"6_8600234_911897cb4710a3f4f6c226b2df2a8d4d_211_1129330677\",             \"create_time\": \"2025-07-03 11:34:03\",             \"path\": \"https://ai21.ytcall.net/robot_asrdir/2025-07-03/8600234_70fdbb96-9218-45a9-b688-668af1aa8109_7.wav\"         },         {             \"msg\": \"嗯，是啊。\",             \"user\": \"1\",             \"uniqueid\": \"6_8600234_911897cb4710a3f4f6c226b2df2a8d4d_211_1129330677\",             \"create_time\": \"2025-07-03 11:34:07\",             \"path\": \"https://ai21.ytcall.net/robot_asrdir/2025-07-03/8600234_70fdbb96-9218-45a9-b688-668af1aa8109_8.wav\"         },         {             \"msg\": \"等待用户讲话中 ....\",             \"user\": \"0\",             \"uniqueid\": \"6_8600234_911897cb4710a3f4f6c226b2df2a8d4d_211_1129330677\",             \"create_time\": \"2025-07-03 11:34:10\",             \"path\": \"\"         },         {             \"msg\": \"呃，可能凌晨2点或者3点吧；帮我准备一个充电宝吧\",             \"user\": \"1\",             \"uniqueid\": \"6_8600234_911897cb4710a3f4f6c226b2df2a8d4d_211_1129330677\",             \"create_time\": \"2025-07-03 11:34:17\",             \"path\": \"https://ai21.ytcall.net/robot_asrdir/2025-07-03/8600234_70fdbb96-9218-45a9-b688-668af1aa8109_9.wav\"         }");
        PluginOutCallRecordAnalyzeVO block = pluginOutCallService.analyzeOutCallRecord(dto).block();
        System.out.println(JacksonUtils.writeValueAsString(block));
    }
}