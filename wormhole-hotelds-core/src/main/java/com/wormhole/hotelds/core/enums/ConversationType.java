package com.wormhole.hotelds.core.enums;

import lombok.*;

import java.util.*;

/**
 * 会话类型枚举
 */
@Getter
@AllArgsConstructor
public enum ConversationType {
    TEXT(0, "文字"),
    VOICE(1, "语音"),
    NOT_CONSERVATION(2, "非会话"),
    ;

    private final Integer code;
    private final String description;

    private static final Map<Integer, ConversationType> CONVERSATION_TYPE_MAP = new HashMap<>();

    static {
        for (ConversationType type : ConversationType.values()) {
            CONVERSATION_TYPE_MAP.put(type.getCode(), type);
        }
    }

    public static ConversationType getByCode(Integer code) {
        return CONVERSATION_TYPE_MAP.get(code);
    }
}