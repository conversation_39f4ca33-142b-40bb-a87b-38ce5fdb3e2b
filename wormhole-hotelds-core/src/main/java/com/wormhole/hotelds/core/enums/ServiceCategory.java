package com.wormhole.hotelds.core.enums;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.*;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.*;


@Getter
@AllArgsConstructor
public enum ServiceCategory {
    DELIVERY("DELIVERY", "送物", Lists.newArrayList(
            ServiceSubcategory.DE_BATHROOM,
            ServiceSubcategory.DE_BEDDING,
            ServiceSubcategory.DE_BEVERAGE,
            ServiceSubcategory.DE_ELECTRONIC,
            ServiceSubcategory.DE_AMENITY,
            ServiceSubcategory.DE_SERVICE,
            ServiceSubcategory.DE_RETURN_ITEMS
    ), "送", 0, 0,"酒店正在为您派送「%s」，请稍后"),

    CLEANING("CLEANING", "清洁", Lists.newArrayList(
            ServiceSubcategory.CL_ROOM
    ), "洁", 0, 0,"酒店正派人为您上门处理「%s」，请稍等"),

    MAINTENANCE("MAINTENANCE", "维修", Lists.newArrayList(
            ServiceSubcategory.MT_PLUMBING,
            ServiceSubcategory.MT_ELECTRIC,
            ServiceSubcategory.MT_HVAC,
            ServiceSubcategory.MT_NETWORK,
            ServiceSubcategory.MT_SECURITY,
            ServiceSubcategory.MT_FFE,
            ServiceSubcategory.MT_OTHER
    ), "修", 0, 0,"酒店正派人为您上门处理「%s」，请稍等"),

    LAUNDRY("LAUNDRY", "洗衣", Lists.newArrayList(
            ServiceSubcategory.LD_STANDARD,
            ServiceSubcategory.LD_EXPRESS,
            ServiceSubcategory.LD_SPECIAL
    ), "衣", 0, 0,"您的洗衣服务「%s」已由工作人员确认，将按时为您服务"),

    STAY("STAY", "入离", Lists.newArrayList(
            ServiceSubcategory.ST_EXTENSION,
            ServiceSubcategory.ST_EARLY,
            ServiceSubcategory.ST_LATE,
            ServiceSubcategory.ST_CHANGE,
            ServiceSubcategory.ST_UPGRADE,
            ServiceSubcategory.ST_ADD_GUEST
    ), "住", 0, 1,"您的住中需求「%s」已由工作人员确认"),

    TRANSPORT("TRANSPORT", "交通", Lists.newArrayList(
            ServiceSubcategory.TP_TAXI,
            ServiceSubcategory.TP_SHUTTLE,
            ServiceSubcategory.TP_RENTAL,
            ServiceSubcategory.TP_PARKING
    ), "行", 0, 1,"您的出行服务「%s」已由工作人员确认，将按时为您安排"),

    FOOD("FOOD", "餐饮", Lists.newArrayList(
            ServiceSubcategory.FD_ORDER,
            ServiceSubcategory.FD_DELIVERY
    ), "餐", 0, 1,"您的订餐服务「%s」已由工作人员确认，将按时为您提供"),


    COMPLAINT("COMPLAINT", "投诉", Lists.newArrayList(
            ServiceSubcategory.CP_SERVICE,
            ServiceSubcategory.CP_FACILITY,
            ServiceSubcategory.CP_NOISE,
            ServiceSubcategory.CP_BILLING,
            ServiceSubcategory.CP_ROOM,
            ServiceSubcategory.CP_FOOD,
            ServiceSubcategory.CP_ENVIRONMENT,
            ServiceSubcategory.CP_PRIVACY,
            ServiceSubcategory.CP_PRICE
    ), "诉", 0, 1,"您的投诉「%s」已由工作人员处理，再次向您道歉！"),


    WAKEUP("WAKEUP", "叫醒", Lists.newArrayList(
            ServiceSubcategory.WAKEUP
    ), "醒", 0, 1,"您的叫醒服务「%s」已由工作人员确认，将按时为您安排"),

    EMERGENCY("EMERGENCY", "紧急", Lists.newArrayList(
            ServiceSubcategory.EM_MEDICAL,
            ServiceSubcategory.EM_SECURITY,
            ServiceSubcategory.EM_FACILITY,
            ServiceSubcategory.EM_IMMEDIATE,
            ServiceSubcategory.EM_SOS
    ), "急", 0, 1,"您的需求「%s」已由工作人员处理，希望能够帮到您！"),


    HOTEL_INFO("HOTEL_INFO", "咨询(酒店)", Arrays.asList(
            ServiceSubcategory.HI_FACILITY,
            ServiceSubcategory.HI_SERVICE,
            ServiceSubcategory.HI_POLICY,
            ServiceSubcategory.HI_HOURS,
            ServiceSubcategory.HI_ENTERTAINMENT,
            ServiceSubcategory.HI_LOCATION,
            ServiceSubcategory.HI_PRICE,
            ServiceSubcategory.HI_ROOM
    ), "咨", 1, 0,""),

    LOCAL_INFO("LOCAL_INFO", "咨询(周边)", Arrays.asList(
            ServiceSubcategory.LI_ATTRACTION,
            ServiceSubcategory.LI_DINING,
            ServiceSubcategory.LI_SHOPPING,
            ServiceSubcategory.LI_TRANSPORT
    ), "咨", 1, 0,""),

    GENERAL("GENERAL", "咨询(其他)", List.of(
            ServiceSubcategory.GI_WEATHER
    ), "咨", 1, 0,""),

    OTHER("OTHER", "其他", List.of(ServiceSubcategory.OTHER), "其", 0, 1,"您的需求「%s」已由工作人员处理，希望能够帮到您！"),

    STAFF("STAFF", "员工", List.of(ServiceSubcategory.STAFF), "员工", 1, 1,""),
    ;


    private final String code;
    private final String chineseName;
    private final List<ServiceSubcategory> subcategories;
    private final ServiceType serviceType;
    /**
     * 前端展示文案
     */
    private final String displayText;
    /**
     * 初始状态
     */
    private final Integer initStatus;
    /**
     * 未完成状态的处理逻辑 (0-直接点击处理 1-回电)
     * 示例值: 0
     */
    private final Integer incompleteProcessType;

    /**
     * 处理完成反馈文案
     */
    private final String feedbackText;


    ServiceCategory(String code, String chineseName, List<ServiceSubcategory> subcategories, String displayText, Integer initStatus, Integer incompleteProcessType,String feedbackText) {
        this.code = code;
        this.chineseName = chineseName;
        this.subcategories = new CopyOnWriteArrayList<>(subcategories);
        this.serviceType = determineServiceType();
        this.displayText = displayText;
        this.initStatus = initStatus;
        this.incompleteProcessType = incompleteProcessType;
        this.feedbackText = feedbackText;
    }


    private ServiceType determineServiceType() {
        return switch (this.code) {
            case "DELIVERY", "CLEANING", "MAINTENANCE", "LAUNDRY", "STAY", "TRANSPORT", "FOOD", "COMPLAINT", "WAKEUP" ->
                    ServiceType.SERVICE;
            case "EMERGENCY" -> ServiceType.EMERGENCY;
            case "HOTEL_INFO", "LOCAL_INFO", "GENERAL" -> ServiceType.INQUIRY;
            case "OTHER" -> ServiceType.OTHER;
            case "STAFF" -> ServiceType.STAFF;
            default -> throw new IllegalArgumentException("未知分类代码: " + code);
        };
    }

    private final static Map<String, ServiceCategory> SERVICE_CATEGORIES_MAP = new HashMap<>();

    static {
        for (ServiceCategory value : ServiceCategory.values()) {
            SERVICE_CATEGORIES_MAP.put(value.getCode(), value);
        }
    }

    public static ServiceCategory getByCode(String code,Boolean isStuffTicket) {
        if(isStuffTicket) {
            return SERVICE_CATEGORIES_MAP.get(STAFF.name());
        }
        return SERVICE_CATEGORIES_MAP.get(code);
    }

    public static String getChineseNameByCode(String code) {
        ServiceCategory category = SERVICE_CATEGORIES_MAP.get(code);
        if (category == null){
            return null;
        }
        return category.getChineseName();
    }

    public static List<ServiceCategory> getServiceCategoriesByType(ServiceType type) {
        return Arrays.stream(ServiceCategory.values())
                .filter(category -> category.getServiceType().equals(type))
                .collect(Collectors.toList());
    }


    public static Boolean isPriorityType(String code) {
        return Arrays.stream(ServiceCategory.values())
                .filter(ele -> ele.getCode().equals(code))
                .anyMatch(ele -> Objects.equals(code,EMERGENCY.getCode())|| Objects.equals(code,COMPLAINT.getCode()));
    }


    public static ServiceType getServiceType(String serviceCategoryCode) {
        return switch (serviceCategoryCode) {
            case "DELIVERY", "CLEANING", "MAINTENANCE", "LAUNDRY", "STAY", "TRANSPORT", "FOOD", "COMPLAINT", "WAKEUP" ->
                    ServiceType.SERVICE;
            case "EMERGENCY" -> ServiceType.EMERGENCY;
            case "HOTEL_INFO", "LOCAL_INFO", "GENERAL" -> ServiceType.INQUIRY;
            case "OTHER" -> ServiceType.OTHER;
            case "STAFF" -> ServiceType.STAFF;
            default -> null;
        };
    }

}