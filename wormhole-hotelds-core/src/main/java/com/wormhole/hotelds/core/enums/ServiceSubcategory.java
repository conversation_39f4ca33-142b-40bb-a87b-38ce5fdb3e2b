package com.wormhole.hotelds.core.enums;


import lombok.*;

import java.util.*;

@Getter
@AllArgsConstructor
public enum ServiceSubcategory {
    // 送物服务子分类
    DE_BATHROOM("DE_BATHROOM", "卫浴用品", 
            "毛巾、洗漱用品、浴袍、拖鞋、浴巾等卫生间用品"),
    DE_BEDDING("DE_BEDDING", "床上用品", 
            "枕头、被子、床单、毯子等床铺相关用品"),
    DE_BEVERAGE("DE_BEVERAGE", "饮品服务", 
            "饮用水、迷你吧饮料、咖啡、茶、冰桶等饮品相关"),
    DE_ELECTRONIC("DE_ELECTRONIC", "电子用品", 
            "充电器、转换器、遥控器、电池等电子相关设备"),
    DE_AMENITY("DE_AMENITY", "便利用品", 
            "衣架、洗衣袋、蚊香液、医疗用品、缝纫包等杂项用品"),
    DE_SERVICE("DE_SERVICE", "特殊服务", 
            "夜床服务、额外物品、特殊请求等其他送物服务"),
    DE_RETURN_ITEMS("DE_RETURN_ITEMS", "归还物品", 
            "归还所借用物品"),

    // 清洁服务子分类
    CL_ROOM("CL_ROOM", "房间清洁",  ""),

    // 维修服务子分类
    MT_PLUMBING("MT_PLUMBING", "卫浴设施", 
            "马桶、水龙头、花洒、淋浴喷头、洗手间台面、浴缸、热水器、地漏、下水道、排风扇、浴室门、镜子、洗手池、冲水按钮"),
    MT_ELECTRIC("MT_ELECTRIC", "电器故障", 
            "灯、灯泡、开关、插座、电源、电吹风、电视、冰箱、电热壶、电磁炉、充电口、遥控器、电闸、排气扇、床头灯、走廊灯"),
    MT_HVAC("MT_HVAC", "空调系统", 
            "空调、空调遥控器、出风口、空调管道、中央空调、空调面板、空调灯"),
    MT_NETWORK("MT_NETWORK", "网络通讯", 
            "Wi-Fi、路由器、网线、电视盒子"),
    MT_SECURITY("MT_SECURITY", "安保设备", 
            "门锁、门禁、房卡、电子锁、门铃、安全扣、猫眼、监控摄像头、门把手、保险柜"),
    MT_FFE("MT_FFE", "家具设备", 
            "床、椅子、桌子、衣柜、书桌、床头柜、茶几、沙发、抽屉、梳妆台、衣架、柜门、窗帘、地毯"),
    MT_OTHER("MT_OTHER", "其他问题", 
            "墙面、天花板、地板、墙纸、窗户、玻璃、墙角"),

    // 洗衣服务子分类
    LD_STANDARD("LD_STANDARD", "标准洗衣", 
            "包括普通洗衣和干洗服务"),
    LD_EXPRESS("LD_EXPRESS", "快速洗衣", 
            "加急洗衣服务，更快完成"),
    LD_SPECIAL("LD_SPECIAL", "特殊洗衣", 
            "熨烫服务及其他特殊需求"),

    // 入住相关子分类
    ST_EXTENSION("ST_EXTENSION", "续住服务",  ""),
    ST_EARLY("ST_EARLY", "提前入住",  ""),
    ST_LATE("ST_LATE", "延迟退房",  ""),
    ST_CHANGE("ST_CHANGE", "换房",  ""),
    ST_UPGRADE("ST_UPGRADE", "升级房型",  ""),
    ST_ADD_GUEST("ST_ADD_GUEST", "增加入住人",  ""),
    ST_LUGGAGE_STORAGE("ST_LUGGAGE_STORAGE", "行李寄存",  ""),

    // 交通服务子分类
    TP_TAXI("TP_TAXI", "出租车预订",  ""),
    TP_SHUTTLE("TP_SHUTTLE", "接送服务",  ""),
    TP_RENTAL("TP_RENTAL", "租车服务",  ""),
    TP_PARKING("TP_PARKING", "停车服务",  ""),

    // 餐饮服务子分类
    FD_ORDER("FD_ORDER", "点餐服务",  ""),
    FD_DELIVERY("FD_DELIVERY", "送餐服务",  ""),

    // 投诉处理子分类
    CP_SERVICE("CP_SERVICE", "服务投诉", "态度差、服务响应慢、处理问题敷衍、沟通有问题、服务缺失、其他"),
    CP_FACILITY("CP_FACILITY", "设施投诉", "设备故障、空调问题、水电异常、门锁问题、遥控失效、其他"),
    CP_NOISE("CP_NOISE", "噪音投诉", "隔音差、施工声、空调响、走廊噪音、邻居吵、其他"),
    CP_BILLING("CP_BILLING", "账单投诉", "多扣费、账单错、押金问题、收费不符、其他"),
    CP_ROOM("CP_ROOM", "客房投诉", "异味重、空间小、环境潮、采光差、设施旧、其他"),
    CP_FOOD("CP_FOOD", "餐饮投诉", "早餐差、食物冷、送餐慢、口味差、卫生差、其他"),
    CP_ENVIRONMENT("CP_ENVIRONMENT", "环境卫生投诉", "床品脏、垃圾未清、异味、浴室脏、墙地污、其他"),
    CP_PRIVACY("CP_PRIVACY", "隐私问题投诉", "未获允许进入房间、怀疑有监控、门窗不遮私、隐私打扰、其他"),
    CP_PRICE("CP_PRICE", "价格倒挂投诉", "价格贵于平台、会员价无效、现场比线上贵、价格波动快、其他"),


    // 新增紧急分类子项
    EM_MEDICAL("EM_MEDICAL", "医疗紧急",
            "医疗协助、救护车需求、急救需求、食品安全问题"),
    EM_SECURITY("EM_SECURITY", "安全紧急",
            "可疑情况、盗窃事件、人身威胁、人身安全等安全问题"),
    EM_FACILITY("EM_FACILITY", "设施紧急",
            "火灾隐患、水患事件、电力故障、结构问题等设施紧急故障"),
    EM_IMMEDIATE("EM_IMMEDIATE", "紧急协助",
            "被锁事件、其他需要立即协助的紧急情况"),
    EM_SOS("EM_SOS", "客房SOS服务",
            "客房SOS服务"),

//    EM_TRANSFER_TO_HUMAN("EM_TRANSFER_TO_HUMAN", "转人工",
//            "转人工"),


    // 酒店信息子分类
    HI_FACILITY("HI_FACILITY", "酒店设施信息", ""),
    HI_SERVICE("HI_SERVICE", "酒店服务信息", ""),
    HI_POLICY("HI_POLICY", "酒店政策信息", ""),
    HI_HOURS("HI_HOURS", "营业时间", ""),
    HI_ENTERTAINMENT("HI_ENTERTAINMENT", "酒店房间娱乐信息", ""),
    HI_LOCATION("HI_LOCATION", "酒店位置与交通信息", ""),
    HI_PRICE("HI_PRICE", "酒店价格信息", ""),
    HI_ROOM("HI_ROOM", "房间信息", ""),

    // 周边信息子分类
    LI_ATTRACTION("LI_ATTRACTION", "景点咨询",  ""),
    LI_DINING("LI_DINING", "餐饮咨询",  ""),
    LI_SHOPPING("LI_SHOPPING", "购物咨询",  ""),
    LI_TRANSPORT("LI_TRANSPORT", "交通咨询",  ""),

    // 一般咨询子分类
    GI_WEATHER("GI_WEATHER", "天气咨询",  ""),
    // 通用服务子分类
    GE_OTHER("GE_OTHER", "其他服务",  ""),


    OTHER("OTHER", "其他服务",  ""),
    STAFF("STAFF", "员工服务",  ""),


    WAKEUP("WU_WAKEUP", "叫醒服务", ""),

    ;
    private final String code;
    private final String chineseName;
    private final String description;



    private final static Map<String, ServiceSubcategory> SERVICE_SUBCATEGORIES_MAP = new HashMap<>();

    static {
        for (ServiceSubcategory value : ServiceSubcategory.values()) {
            SERVICE_SUBCATEGORIES_MAP.put(value.getCode(), value);
        }
    }
    public static ServiceSubcategory getByCode(String code,Boolean isStuffTicket) {
        if(isStuffTicket) {
            return SERVICE_SUBCATEGORIES_MAP.get(STAFF.name());
        }
        return SERVICE_SUBCATEGORIES_MAP.get(code);
    }



}