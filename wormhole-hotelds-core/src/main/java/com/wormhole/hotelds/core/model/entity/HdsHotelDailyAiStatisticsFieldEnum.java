package com.wormhole.hotelds.core.model.entity;

/**
 * 酒店每日AI统计表字段枚举
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
public enum HdsHotelDailyAiStatisticsFieldEnum {
    /**
     * 主键ID
     */
    id,

    /**
     * 业务统计日期
     */
    business_date,

    /**
     * 酒店编码
     */
    hotel_code,

    // 反馈统计指标
    /**
     * 客诉工单数
     */
    complaint_ticket_count,

    /**
     * 客诉预警工单数
     */
    complaint_warning_ticket_count,

    /**
     * OTA差评数
     */
    ota_negative_review_count,

    // 通话指标
    /**
     * AI通话数量
     */
    ai_call_count,

    /**
     * 回拨通话数量
     */
    return_call_count,

    // 工单指标
    /**
     * 总工单数量
     */
    ticket_count,

    /**
     * 已完成工单数量
     */
    completed_ticket_count,

    // 一级分类统计
    /**
     * 问询总数
     */
    inquiry_count,

    /**
     * 问询已完成总数
     */
    inquiry_completed_count,

    /**
     * 客需工单总数
     */
    service_need_count,

    /**
     * 客需工单已完成总数
     */
    service_need_completed_count,

    /**
     * 客诉工单已完成总数
     */
    complaint_completed_count,

    /**
     * 紧急事项总数
     */
    emergency_count,

    /**
     * 紧急事项工单已完成总数
     */
    emergency_completed_count,

    /**
     * 使用房间数
     */
    room_use_count,

    /**
     * 平均通话时长（秒）
     */
    avg_call_duration_seconds,

    /**
     * 文字对话数
     */
    text_dialogue_count,

    /**
     * AI解决数
     */
    ai_solve_count,

    /**
     * 平均处理时长（秒）
     */
    avg_complete_duration_seconds,

    /**
     * 超时工单数
     */
    overdue_count,

    /**
     * 有效处理时长工单数
     */
    valid_complete_duration_ticket_count,

    /**
     * 人工回拨工单数
     */
    return_call_ticket_count,

    /**
     * 创建人ID
     */
    created_by,

    /**
     * 创建人姓名
     */
    created_by_name,

    /**
     * 更新人ID
     */
    updated_by,

    /**
     * 更新人姓名
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 行状态
     */
    row_status
} 