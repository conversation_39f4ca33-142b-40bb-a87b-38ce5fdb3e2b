package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.time.*;

/**
 * 门店表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_hotel_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsHotelInfoEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    private Integer id;


    /**
     * 酒店编码
     */
    @Column("hotel_code")
    private String hotelCode;


    /**
     * 酒店类型 1单体 2连锁
     */
    @Column("hotel_type")
    private Integer hotelType;


    /**
     * 房间数
     */
    @Column("total_room")
    private Integer totalRoom;


    /**
     * 品牌编码
     */
    @Column("brand_code")
    private String brandCode;


    /**
     * 商家编码
     */
    @Column("merchant_id")
    private String merchantId;


    /**
     * 状态[0关店 1营业 2筹备中 3暂停营业]
     */
    @Column("book_flag")
    private Integer bookFlag;


    /**
     * 酒店联系人
     */
    @Column("main_person")
    private String mainPerson;


    /**
     * 联系人电话
     */
    @Column("phone")
    private String phone;

    /**
     * 前台电话
     */
    @Column("front_phone")
    private String frontPhone;


    /**
     * 邮箱
     */
    @Column("email")
    private String email;


    /**
     * 开业日期
     */
    @Column("open_date")
    private String openDate;


    /**
     * 国家编码
     */
    @Column("country_code")
    private String countryCode;


    /**
     * 省编码
     */
    @Column("province_code")
    private String provinceCode;


    /**
     * 城市编码
     */
    @Column("city_code")
    private String cityCode;


    /**
     * 区县编码
     */
    @Column("district_code")
    private String districtCode;


    /**
     * 酒店名称
     */
    @Column("hotel_name")
    private String hotelName;


    /**
     * 酒店全拼
     */
    @Column("hotel_name_pin_yin")
    private String hotelNamePinYin;


    /**
     * 企业主体名称
     */
    @Column("subject_name")
    private String subjectName;


    /**
     * 品牌名称
     */
    @Column("brand_name")
    private String brandName;


    /**
     * 商户名称
     */
    @Column("merchant_name")
    private String merchantName;


    /**
     * 国家名称
     */
    @Column("country_name")
    private String countryName;


    /**
     * 省份名称
     */
    @Column("province_name")
    private String provinceName;


    /**
     * 城市名称
     */
    @Column("city_name")
    private String cityName;


    /**
     * 区县名称
     */
    @Column("district_name")
    private String districtName;


    /**
     * 酒店描述
     */
    @Column("description")
    private String description;


    /**
     * 酒店地址
     */
    @Column("address")
    private String address;


    /**
     * 酒店logo图片url
     */
    @Column("hotel_logo")
    private String hotelLogo;


    /**
     * 携程ebk地址
     */
    @Column("ctrip_ebk_url")
    private String ctripEbkUrl;


    /**
     * 设备状态: 1-正常, 0-停用 2:未完成初始化
     */
    @Column("status")
    private Integer status;


    /**
     * 高德坐标经度
     */
    @Column("gaode_longitude")
    private String gaodeLongitude;


    /**
     * 高德坐标纬度
     */
    @Column("gaode_latitude")
    private String gaodeLatitude;

    /**
     * 门店创建来源
     */
    private Integer source;

    /**
     * ai产品类型
     */
    @Column("ai_product_types")
    private String aiProductTypes;

    /**
     * 邀请码
     */
    @Column("invitation_code")
    private String invitationCode;

    /**
     * 被邀请码
     */
    @Column("invite_code")
    private String inviteCode;

    /**
     * ota延期月数
     */
    @Column("ota_extend_months")
    private Integer otaExtendMonths;

    /**
     * ota奖励月数
     */
    @Column("ota_reward_months")
    private Integer otaRewardMonths;

    /**
     * 初始化完成时间
     */
    @Column("init_finished_at")
    private LocalDateTime initFinishedAt;

    /**
     * sos启用开关;0-否 1-是
     */
    @Column("sos_switch")
    private Integer sosSwitch;

    @Column("time_zone")
    private String timeZone;
    /**
     * 通话指令：token_req（AI通话）、transfer_to_human（真人通话）
     */
    private String callCommand;
    /**
     * 对话框开关；0-关闭 1-开启
     */
    private Integer dialogSwitch;
}