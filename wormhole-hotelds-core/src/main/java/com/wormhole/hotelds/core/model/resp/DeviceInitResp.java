package com.wormhole.hotelds.core.model.resp;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceInitResp {

    /**
     * rtc token
     */
    private String rtcToken;

    /**
     * rtc 厂商
     */
    private String rtcManufacturer;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 用户id deviceType + deviceId
     */
    private String userId;

    /**
     * 设备code
     */
    private String deviceId;

    /**
     * 房间唯一编码
     */
    private String positionCode;

    private String positionName;
    private String positionFullName;

    /**
     * 设备类型 （前台设备 & 客房设备）
     */
    private String deviceType;

    /**
     * 语音房id
     */
    private String rtcRoomId;

    /**
     * 服务端回调地址
     */
    private String serverCallBackUrl;

    /**
     * 加密
     */
    private String secret;

    /**
     * 超时时间
     */
    private Integer callTimeOutSeconds;


    private String hotelName;

    private String deviceQrCodeUrl;

    /**
     * 普通APP二维码地址
     */
    private String normalQrCodeUrl;

    private String botCode;

    private String rtcUserId;
    /**
     * sos启用开关;0-否 1-是
     */
    private Integer sosSwitch;

    private String rtcAppId;

    private String callCommand;

    private Integer dialogSwitch;
}
